<?xml version="1.0" encoding="UTF-8"?>
<actions>
        <action>
            <actionName>build</actionName>
            <goals>
                <goal>install</goal>
            </goals>
            <properties>
                <urlImportZW>http://homologacao.pactosolucoes.com.br:82/app-tronco/IntegracaoCadastrosWS?wsdl</urlImportZW>
                <urlImportAdmWS>http://homologacao.pactosolucoes.com.br:82/app-tronco/AdmWS?wsdl</urlImportAdmWS>
                <instanceSchedulingName>disable</instanceSchedulingName>
                <COOKIE_FAIL>true</COOKIE_FAIL>
                <FOTOS_NUVEM>true</FOTOS_NUVEM>
            </properties>
            <activatedProfiles>
                <activatedProfile>tomcat7</activatedProfile>
                
            </activatedProfiles>
        </action>
        <action>
            <actionName>CUSTOM-Deploy Remoto</actionName>
            <displayName>Deploy Remoto</displayName>
            <goals>
                <goal>clean</goal>
                <goal>install</goal>
                
            </goals>
            <properties>
                <sshHost>vitacorpus.dyndns.org</sshHost>
            </properties>
            <activatedProfiles>
                <activatedProfile>glassfish3</activatedProfile>
                <activatedProfile>deployment</activatedProfile>
                
            </activatedProfiles>
        </action>
        <action>
            <actionName>rebuild</actionName>
            <goals>
                <goal>clean</goal>
                <goal>install</goal>
            </goals>
            <properties>
                <urlImportZW>http://homologacao.pactosolucoes.com.br:82/app-tronco/IntegracaoCadastrosWS?wsdl</urlImportZW>
                <urlImportAdmWS>http://homologacao.pactosolucoes.com.br:82/app-tronco/AdmWS?wsdl</urlImportAdmWS>
                <instanceSchedulingName>disable</instanceSchedulingName>
                <COOKIE_FAIL>true</COOKIE_FAIL>
                <FOTOS_NUVEM>true</FOTOS_NUVEM>
            </properties>
            <activatedProfiles>
                <activatedProfile>tomcat7</activatedProfile>
            </activatedProfiles>
        </action>
        <action>
            <actionName>build-with-dependencies</actionName>
            <reactor>also-make</reactor>
            <goals>
                <goal>install</goal>
            </goals>
            <properties>
                <urlImportZW>http://homologacao.pactosolucoes.com.br:82/app-tronco/IntegracaoCadastrosWS?wsdl</urlImportZW>
                <urlImportAdmWS>http://homologacao.pactosolucoes.com.br:82/app-tronco/AdmWS?wsdl</urlImportAdmWS>
                <instanceSchedulingName>disable</instanceSchedulingName>
                <COOKIE_FAIL>true</COOKIE_FAIL>
                <FOTOS_NUVEM>true</FOTOS_NUVEM>
            </properties>
            <activatedProfiles>
                <activatedProfile>tomcat7</activatedProfile>
            </activatedProfiles>
        </action>
        <action>
            <actionName>run</actionName>
            <goals>
                <goal>package</goal>
            </goals>
            <properties>
                <netbeans.deploy>true</netbeans.deploy>
                <urlImportZW>http://homologacao.pactosolucoes.com.br:82/app-tronco/IntegracaoCadastrosWS?wsdl</urlImportZW>
                <urlImportAdmWS>http://homologacao.pactosolucoes.com.br:82/app-tronco/AdmWS?wsdl</urlImportAdmWS>
                <instanceSchedulingName>disable</instanceSchedulingName>
                <COOKIE_FAIL>true</COOKIE_FAIL>
                <FOTOS_NUVEM>true</FOTOS_NUVEM>
            </properties>
            <activatedProfiles>
                <activatedProfile>tomcat7</activatedProfile>
            </activatedProfiles>
        </action>
        <action>
            <actionName>debug</actionName>
            <goals>
                <goal>package</goal>
            </goals>
            <properties>
                <netbeans.deploy.debugmode>true</netbeans.deploy.debugmode>
                <netbeans.deploy>true</netbeans.deploy>
                <urlImportZW>http://homologacao.pactosolucoes.com.br:82/app-tronco/IntegracaoCadastrosWS?wsdl</urlImportZW>
                <urlImportAdmWS>http://homologacao.pactosolucoes.com.br:82/app-tronco/AdmWS?wsdl</urlImportAdmWS>
                <instanceSchedulingName>disable</instanceSchedulingName>
                <COOKIE_FAIL>true</COOKIE_FAIL>
                <FOTOS_NUVEM>true</FOTOS_NUVEM>
            </properties>
            <activatedProfiles>
                <activatedProfile>tomcat7</activatedProfile>
            </activatedProfiles>
        </action>
        <action>
            <actionName>profile</actionName>
            <goals>
                <goal>package</goal>
            </goals>
            <properties>
                <netbeans.deploy>true</netbeans.deploy>
                <netbeans.deploy.profilemode>true</netbeans.deploy.profilemode>
            </properties>
            <activatedProfiles>
                <activatedProfile>tomcat7</activatedProfile>
            </activatedProfiles>
        </action>
        <action>
            <actionName>debug.test.single</actionName>
            <goals>
                <goal>test-compile</goal>
                <goal>surefire:test</goal>
            </goals>
            <properties>
                <test>${packageClassName}</test>
                <forkMode>once</forkMode>
                <maven.surefire.debug>-Xdebug -Xrunjdwp:transport=dt_socket,server=n,address=${jpda.address}</maven.surefire.debug>
                <jpda.listen>true</jpda.listen>
            </properties>
            <activatedProfiles>
                <activatedProfile>tomcat7</activatedProfile>
            </activatedProfiles>
        </action>
    </actions>
