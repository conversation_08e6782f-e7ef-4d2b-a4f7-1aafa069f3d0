<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.5.1.final using JasperReports Library version 6.5.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="Anamnese" pageWidth="550" pageHeight="842" columnWidth="550" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isSummaryNewPage="true" uuid="4e97cda0-7e4a-494e-b0d4-6ae08374f035">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="valor1" class="java.lang.String"/>
	<field name="valor2" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="30" splitType="Stretch">
			<frame>
				<reportElement x="0" y="0" width="550" height="30" uuid="51dde39a-3a54-4841-ba00-21da595fd22b"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField isStretchWithOverflow="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="0" y="0" width="350" height="20" forecolor="#90949A" uuid="e81cccfa-4cf2-4391-981f-5e2c527397c9"/>
					<textElement textAlignment="Justified" verticalAlignment="Middle">
						<font fontName="Arial" size="12" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{valor1}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="350" y="0" width="100" height="20" forecolor="#1DC06F" uuid="934f1f2a-1007-4d44-b607-de5b1f28b768"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
						<font fontName="Arial" size="12" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{valor2}.equals("Não")?"<style forecolor='#DB2C3D'>"+$F{valor2}+"</style>":"<style forecolor='#1DC06F'>"+$F{valor2}+"</style>"]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
</jasperReport>
