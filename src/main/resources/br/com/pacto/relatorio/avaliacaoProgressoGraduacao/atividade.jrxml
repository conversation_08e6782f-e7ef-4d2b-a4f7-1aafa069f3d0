<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.5.1.final using JasperReports Library version 6.5.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="Anamnese" pageWidth="550" pageHeight="842" columnWidth="550" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isSummaryNewPage="true" uuid="4e97cda0-7e4a-494e-b0d4-6ae08374f035">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<parameter name="valor4" class="java.lang.String"/>
	<parameter name="sub_atividadeJR" class="java.lang.Object"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="valor1" class="java.lang.String"/>
	<field name="valor2" class="java.lang.String"/>
	<field name="valor3" class="java.lang.String"/>
	<field name="sub_atividadeJR" class="java.lang.Object"/>
	<field name="SUBREPORT_DIR" class="java.lang.String"/>
	<field name="valor4" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="126" splitType="Stretch">
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="0" y="10" width="530" height="20" forecolor="#6F747B" uuid="d7a9a23d-3965-41f7-bf07-5da3e5827264"/>
				<textElement textAlignment="Justified">
					<font fontName="Arial" size="14" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{valor1}]]></textFieldExpression>
			</textField>
			<frame>
				<reportElement x="20" y="44" width="511" height="71" uuid="cc40eb3f-8e93-496b-a69a-09eaab4edf8b"/>
				<image hAlign="Center" vAlign="Top" onErrorType="Icon">
					<reportElement x="0" y="2" width="67" height="67" uuid="5e06ba29-ce48-414b-bb7b-26c1dc778756"/>
					<imageExpression><![CDATA[$F{valor4}]]></imageExpression>
				</image>
				<subreport>
					<reportElement stretchType="RelativeToTallestObject" x="80" y="0" width="430" height="71" backcolor="#FFFFFF" uuid="90d95ac3-0019-496c-93af-0e605eb15647"/>
					<dataSourceExpression><![CDATA[$F{sub_atividadeJR}]]></dataSourceExpression>
					<subreportExpression><![CDATA[$F{SUBREPORT_DIR} + "sub_atividade.jasper"]]></subreportExpression>
				</subreport>
			</frame>
		</band>
	</detail>
</jasperReport>
