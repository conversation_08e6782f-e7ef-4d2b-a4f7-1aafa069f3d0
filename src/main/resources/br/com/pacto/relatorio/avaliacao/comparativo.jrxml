<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.5.1.final using JasperReports Library version 6.5.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="Perimetria" pageWidth="550" pageHeight="842" columnWidth="550" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isSummaryNewPage="true" uuid="4e97cda0-7e4a-494e-b0d4-6ae08374f035">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="New Data Adapter "/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="valor1" class="java.lang.String"/>
	<field name="valor2" class="java.lang.String"/>
	<field name="valor3" class="java.lang.String"/>
	<field name="valor4" class="java.lang.String"/>
	<field name="valor5" class="java.lang.String"/>
	<field name="descricao" class="java.lang.String"/>
	<detail>
		<band height="22" splitType="Stretch">
			<textField isStretchWithOverflow="true">
				<reportElement x="0" y="0" width="150" height="20" uuid="d7a9a23d-3965-41f7-bf07-5da3e5827264"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle" markup="html"/>
				<textFieldExpression><![CDATA[$F{descricao}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="150" y="0" width="80" height="20" uuid="f3380d2f-c51c-4e02-99dd-b7a2e0b94da3"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="html">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{valor1}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="230" y="0" width="80" height="20" uuid="1b9c2e15-b732-4734-a7af-9f9815bca76d"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="html">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{valor2}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="310" y="0" width="80" height="20" uuid="cf051c92-2589-4fea-85bf-3cd9b893a7d4"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="html">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{valor3}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="390" y="0" width="80" height="20" uuid="2a800e37-1422-4dd0-8452-35030bc031b5"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="html">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{valor4}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="470" y="0" width="80" height="20" uuid="b041ff20-a8b1-4b09-825e-615df939e5b0"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="html">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{valor5}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement positionType="Float" x="0" y="21" width="550" height="1" forecolor="#E6E6E6" uuid="038c8fb7-27c0-410d-8549-cfc749db240f"/>
			</line>
		</band>
	</detail>
</jasperReport>
