<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.5.1.final using JasperReports Library version 6.5.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="Perimetria" pageWidth="275" pageHeight="842" columnWidth="275" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isSummaryNewPage="true" uuid="4e97cda0-7e4a-494e-b0d4-6ae08374f035">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="New Data Adapter "/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="valor1" class="java.lang.String"/>
	<field name="valor2" class="java.lang.String"/>
	<field name="valor3" class="java.lang.String"/>
	<columnHeader>
		<band height="25">
			<staticText>
				<reportElement x="136" y="1" width="66" height="20" uuid="f025eb0f-1c2e-4d2f-b738-a8cd4170a62a"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Esquerda (cm)]]></text>
			</staticText>
			<staticText>
				<reportElement x="209" y="1" width="66" height="20" uuid="8c4905cb-fea5-45e9-9a42-6f8019a3db0a"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Direita (cm)]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="25" splitType="Stretch">
			<textField isStretchWithOverflow="true">
				<reportElement x="0" y="0" width="135" height="20" uuid="d7a9a23d-3965-41f7-bf07-5da3e5827264"/>
				<box>
					<bottomPen lineWidth="1.0" lineColor="#EDEDED"/>
				</box>
				<textFieldExpression><![CDATA["  "+$F{valor1}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="136" y="0" width="66" height="20" uuid="f3380d2f-c51c-4e02-99dd-b7a2e0b94da3"/>
				<box>
					<bottomPen lineWidth="1.0" lineColor="#EDEDED"/>
				</box>
				<textFieldExpression><![CDATA[$F{valor2}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="209" y="0" width="66" height="20" uuid="b046cd74-13f4-4104-b746-00add807eea7"/>
				<box>
					<bottomPen lineWidth="1.0" lineColor="#EDEDED"/>
				</box>
				<textFieldExpression><![CDATA[$F{valor3}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
