<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.5.1.final using JasperReports Library version 6.5.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="avaliacao_fisica" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="acfe1f23-0c9b-4c60-baa1-f7c0ae928e2b">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="Sample DB"/>
	<property name="com.jaspersoft.studio.report.description" value=""/>
	<parameter name="logoPadraoRelatorio" class="java.lang.String" isForPrompting="false">
		<parameterDescription><![CDATA[logoPadraoRelatorio]]></parameterDescription>
	</parameter>
	<parameter name="diametroJoelho" class="java.lang.String"/>
	<parameter name="alcanceMaximo" class="java.lang.String"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="empresaNome" class="java.lang.String"/>
	<parameter name="pesoAtual" class="java.lang.String"/>
	<parameter name="empresaSite" class="java.lang.String"/>
	<parameter name="empresaEndereco" class="java.lang.String"/>
	<parameter name="fotoAluno" class="java.lang.String"/>
	<parameter name="nomeAluno" class="java.lang.String"/>
	<parameter name="idade" class="java.lang.String"/>
	<parameter name="avaliador" class="java.lang.String"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["C:\\PactoJ\\Sistemas\\treino-tronco\\src\\main\\resources\\br\\com\\pacto\\relatorio\\avaliacao\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="dataAvaliacao" class="java.lang.String"/>
	<parameter name="sexo" class="java.lang.String"/>
	<parameter name="contato" class="java.lang.String"/>
	<parameter name="proximaAvaliacao" class="java.lang.String"/>
	<parameter name="imc" class="java.lang.String"/>
	<parameter name="anamneselista" class="java.util.List" isForPrompting="false"/>
	<parameter name="anterior" class="java.lang.String"/>
	<parameter name="perimetriaJR" class="java.lang.Object"/>
	<parameter name="diametroPunho" class="java.lang.String"/>
	<parameter name="altura" class="java.lang.String"/>
	<parameter name="peso" class="java.lang.String"/>
	<parameter name="resultadoIMC" class="java.lang.String"/>
	<parameter name="gordura" class="java.lang.String"/>
	<parameter name="circunferencia" class="java.lang.String"/>
	<parameter name="circunferenciaResultado" class="java.lang.String"/>
	<parameter name="imcResultado" class="java.lang.String"/>
	<parameter name="usuario" class="java.lang.String"/>
	<parameter name="composicaoResultado" class="java.lang.String"/>
	<parameter name="horaEmissao" class="java.lang.String"/>
	<parameter name="classificacaoFlexibilidade" class="java.lang.String"/>
	<parameter name="percGordura" class="java.lang.String"/>
	<parameter name="percOssos" class="java.lang.String"/>
	<parameter name="percResiduos" class="java.lang.String"/>
	<parameter name="percMusculos" class="java.lang.String"/>
	<parameter name="recomendacoes" class="java.lang.String"/>
	<parameter name="objetivosAluno" class="java.lang.String"/>
	<parameter name="peso1" class="java.lang.String"/>
	<parameter name="alturaAtual" class="java.lang.String" isForPrompting="false"/>
	<parameter name="peso2" class="java.lang.String"/>
	<parameter name="posterior" class="java.lang.String"/>
	<parameter name="vo2" class="java.lang.String"/>
	<parameter name="pressaoArterial" class="java.lang.String"/>
	<parameter name="peso3" class="java.lang.String"/>
	<parameter name="freqCardiaca" class="java.lang.String" isForPrompting="false"/>
	<parameter name="showOsseo" class="java.lang.Boolean"/>
	<parameter name="peso4" class="java.lang.String"/>
	<parameter name="dataPeso1" class="java.lang.String"/>
	<parameter name="dataPeso2" class="java.lang.String"/>
	<parameter name="dataPeso3" class="java.lang.String"/>
	<parameter name="dataPeso4" class="java.lang.String"/>
	<parameter name="totalDobras" class="java.lang.String"/>
	<parameter name="protocolo" class="java.lang.String"/>
	<parameter name="anamneseJR" class="java.lang.Object" isForPrompting="false"/>
	<parameter name="rmlAbdomen" class="java.lang.String"/>
	<parameter name="ombrosAssimetricos" class="java.lang.String"/>
	<parameter name="assimetriaQuadril" class="java.lang.String"/>
	<parameter name="limiar1" class="java.lang.String"/>
	<parameter name="limiar2" class="java.lang.String"/>
	<parameter name="testeCampo" class="java.lang.String"/>
	<parameter name="valor2TesteCampo" class="java.lang.String"/>
	<parameter name="valor1TesteCampo" class="java.lang.String"/>
	<parameter name="parq" class="java.lang.String"/>
	<parameter name="parqJR" class="java.lang.Object"/>
	<parameter name="dobrasJR" class="java.lang.Object" isForPrompting="false"/>
	<parameter name="pesoGordura" class="java.lang.String"/>
	<parameter name="showventilometria" class="java.lang.Boolean"/>
	<parameter name="pesoOsseo" class="java.lang.String"/>
	<parameter name="pesoMuscular" class="java.lang.String"/>
	<parameter name="visaoLateral" class="java.lang.String"/>
	<parameter name="pesoResidual" class="java.lang.String"/>
	<parameter name="obsFlexibilidade" class="java.lang.String"/>
	<parameter name="obsPostural" class="java.lang.String"/>
	<parameter name="rmlBraco" class="java.lang.String"/>
	<parameter name="valor3TesteCampo" class="java.lang.String"/>
	<parameter name="urlFrente" class="java.lang.String"/>
	<parameter name="urlDireita" class="java.lang.String"/>
	<parameter name="urlEsquerda" class="java.lang.String"/>
	<parameter name="urlCosta" class="java.lang.String"/>
	<parameter name="showdobras" class="java.lang.Boolean"/>
	<parameter name="showperimetria" class="java.lang.Boolean"/>
	<parameter name="showflexibilidade" class="java.lang.Boolean"/>
	<parameter name="showpostural" class="java.lang.Boolean"/>
	<parameter name="showrml" class="java.lang.Boolean"/>
	<parameter name="showvo2max" class="java.lang.Boolean"/>
	<parameter name="showcomparacoes" class="java.lang.Boolean"/>
	<parameter name="showrecomendacoes" class="java.lang.Boolean"/>
	<parameter name="showanamnese" class="java.lang.Boolean"/>
	<parameter name="showparq" class="java.lang.Boolean"/>
	<parameter name="showpesoaltura" class="java.lang.Boolean"/>
	<parameter name="showobjetivos" class="java.lang.Boolean"/>
	<parameter name="comparativo" class="java.lang.Object"/>
	<parameter name="apresentarAssinatura" class="java.lang.Boolean"/>
	<parameter name="cargaAstrand" class="java.lang.Double"/>
	<parameter name="assinatura" class="java.lang.String"/>
	<parameter name="ectomorfia" class="java.lang.String"/>
	<parameter name="mesomorfia" class="java.lang.String"/>
	<parameter name="vo2MaxAstrand" class="java.lang.Double"/>
	<parameter name="endomorfia" class="java.lang.String"/>
	<parameter name="somatotipia" class="java.lang.Boolean"/>
	<parameter name="diametroCotovelo" class="java.lang.String"/>
	<parameter name="vo2Astrand" class="java.lang.Double"/>
	<parameter name="diametroTornozelo" class="java.lang.String"/>
	<parameter name="vo2Queens" class="java.lang.String"/>
	<parameter name="Parameter1" class="java.lang.Boolean"/>
	<parameter name="showQueens" class="java.lang.Boolean"/>
	<parameter name="fcQueens" class="java.lang.String"/>
	<parameter name="metaGordura" class="java.lang.String"/>
	<parameter name="showMeta" class="java.lang.Boolean"/>
	<parameter name="frequenciaAstrand" class="java.lang.Double"/>
	<parameter name="showAstrand" class="java.lang.Boolean"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<variable name="REPORT_PAGE" class="java.lang.Integer" resetType="Page">
		<variableExpression><![CDATA[1]]></variableExpression>
		<initialValueExpression><![CDATA[$V{PAGE_NUMBER} + 1]]></initialValueExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band height="47">
			<image scaleImage="RetainShape" hAlign="Left" onErrorType="Icon" evaluationTime="Page">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="100" height="45" uuid="3854af15-81f3-42c9-952e-ff0c19b19273"/>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<imageExpression><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<textField>
				<reportElement x="110" y="0" width="340" height="15" uuid="4608a8c7-1007-4603-8886-9c71f47cc96e"/>
				<textFieldExpression><![CDATA[$P{empresaNome}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="110" y="15" width="340" height="15" uuid="52030124-f695-4247-8312-f9068c5d92c6"/>
				<textFieldExpression><![CDATA[$P{empresaEndereco}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="110" y="30" width="340" height="15" uuid="1229b89c-608a-4a81-bfd0-d16fe946ee7b"/>
				<textFieldExpression><![CDATA[$P{empresaSite}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="103" y="0" width="1" height="45" forecolor="#EDEDED" backcolor="#EDEDED" uuid="22ca44ef-6b97-4f74-928f-6eedafe1aef7"/>
			</line>
			<line>
				<reportElement x="0" y="46" width="550" height="1" uuid="fcab230f-a44e-488c-b694-fe161b9aaa70"/>
			</line>
		</band>
	</pageHeader>
	<detail>
		<band height="372">
			<rectangle>
				<reportElement x="0" y="0" width="550" height="41" backcolor="#EDEDED" uuid="010643d5-88d3-4f8f-bf0f-f12f76e0d53d"/>
				<graphicElement>
					<pen lineWidth="0.0" lineColor="#EDEDED"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="0" y="0" width="550" height="41" uuid="db79f180-4db2-41d4-8964-8dd4fdfb2bc6"/>
				<box leftPadding="0">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[Avaliação Física]]></text>
			</staticText>
			<image scaleImage="RetainShape" hAlign="Center" isUsingCache="false" onErrorType="Icon" evaluationTime="Report">
				<reportElement positionType="FixRelativeToBottom" stretchType="RelativeToTallestObject" x="9" y="57" width="90" height="75" uuid="aedc526b-00ff-4755-a38c-5bec294ccb5f"/>
				<imageExpression><![CDATA[$P{fotoAluno}]]></imageExpression>
			</image>
			<textField>
				<reportElement x="112" y="56" width="438" height="25" uuid="81088660-25f5-4053-9209-41f052b571bb"/>
				<textElement>
					<font fontName="Arial" size="14" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{nomeAluno}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Page">
				<reportElement x="180" y="81" width="128" height="15" uuid="12f80edc-4973-4863-af1b-05ba09c1efec"/>
				<textElement>
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{idade}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="112" y="81" width="68" height="15" uuid="24b932a8-75ed-4253-9455-660fdd6b23a9"/>
				<textElement>
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Idade:]]></text>
			</staticText>
			<textField evaluationTime="Page">
				<reportElement x="180" y="96" width="368" height="15" uuid="a82e6d92-39e7-416c-b954-e52a315c32d2"/>
				<textElement>
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{avaliador}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="112" y="96" width="68" height="15" uuid="f827cd65-bf18-4299-80a5-e49341541f1a"/>
				<textElement>
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Avaliador:]]></text>
			</staticText>
			<staticText>
				<reportElement x="340" y="81" width="68" height="15" uuid="b65e177b-a4e8-4e0a-9834-f04bd118429c"/>
				<textElement>
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Sexo:]]></text>
			</staticText>
			<textField evaluationTime="Page">
				<reportElement x="408" y="81" width="140" height="15" uuid="0a3b4446-bbca-4229-8580-918f1f6c7845"/>
				<textElement>
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{sexo}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="112" y="111" width="68" height="15" uuid="558dff94-e0ce-4a9c-8943-8f88e53eb443"/>
				<textElement>
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Data:]]></text>
			</staticText>
			<textField evaluationTime="Page">
				<reportElement x="180" y="111" width="128" height="15" uuid="19d10f9e-264d-4768-a257-f03833c56c3e"/>
				<textElement>
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{dataAvaliacao}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="340" y="111" width="68" height="15" uuid="e32d3057-e3c8-4fe5-be67-77cce5da3819"/>
				<textElement>
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Próxima:]]></text>
			</staticText>
			<textField evaluationTime="Page">
				<reportElement x="408" y="111" width="140" height="15" uuid="f5322c27-7cf9-4b4b-a589-01b19560f145"/>
				<textElement>
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{proximaAvaliacao}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="0" y="158" width="550" height="1" uuid="929d11cd-de4d-46e1-8967-a0c77bddff72"/>
			</line>
			<staticText>
				<reportElement x="0" y="167" width="130" height="15" uuid="5e45346d-5eeb-4876-ba12-689805678f1e"/>
				<box>
					<pen lineWidth="1.0" lineColor="#CCCCCC"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#EDEDED"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Índice de Massa corporal (IMC)]]></text>
			</staticText>
			<staticText>
				<reportElement x="418" y="167" width="130" height="15" uuid="46cb3a05-5179-42f2-b70c-0942e784bfdf"/>
				<box>
					<pen lineWidth="1.0" lineColor="#EDEDED"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#EDEDED"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Peso Atual]]></text>
			</staticText>
			<staticText>
				<reportElement x="278" y="167" width="130" height="15" uuid="aa5c4831-90f3-4f52-9c5a-37933967b5d3"/>
				<box>
					<pen lineWidth="1.0" lineColor="#CCCCCC"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#EDEDED"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Risco Cardiovascular]]></text>
			</staticText>
			<line>
				<reportElement x="135" y="167" width="1" height="47" uuid="420f705d-aeca-4d45-bf4e-a63c9603a060"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#EDEDED"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="274" y="167" width="1" height="47" uuid="fe478947-52e3-49c5-9495-af8c66a3f627"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#EDEDED"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="412" y="167" width="1" height="47" uuid="08349dc2-9041-4c13-b3ec-732637d7c81a"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#EDEDED"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement x="140" y="167" width="130" height="15" uuid="0fa5b34a-0996-497a-a317-1e2c3fc253f9"/>
				<box>
					<pen lineWidth="1.0" lineColor="#CCCCCC"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#EDEDED"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Composição Corporal]]></text>
			</staticText>
			<textField evaluationTime="Page">
				<reportElement x="0" y="187" width="130" height="45" uuid="c31c2b94-b809-4a08-ba3f-2723dacd4dce"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="28" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{imc}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Page">
				<reportElement x="278" y="187" width="130" height="45" uuid="8ac39e4d-848e-4437-945e-f388274c7cbe"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="28" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{circunferencia}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Page">
				<reportElement x="418" y="187" width="130" height="45" uuid="2f3e581a-ad6d-4836-94db-02711251f566"/>
				<box>
					<bottomPen lineWidth="1.0" lineColor="#EDEDED"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="28" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{peso}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="278" y="232" width="130" height="18" uuid="422395c3-7fb2-404a-8656-e4c2ac18d9ff"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<text><![CDATA[Circunferência Abdominal]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="232" width="130" height="18" forecolor="#000000" uuid="cb715d2c-407f-4c71-8996-8a7c909c531e"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#EDEDED"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<text><![CDATA[IMC]]></text>
			</staticText>
			<textField evaluationTime="Page">
				<reportElement x="278" y="277" width="130" height="39" uuid="5491de2b-b2ae-434f-94ba-ac18a32900ab"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{circunferenciaResultado}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="278" y="259" width="130" height="18" uuid="6f71d3bc-1608-420f-ac7c-c7f06f080041"/>
				<box>
					<topPen lineWidth="1.0" lineColor="#EDEDED"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<text><![CDATA[Resultado:]]></text>
			</staticText>
			<staticText>
				<reportElement x="140" y="267" width="130" height="18" uuid="014089f9-672e-4062-a47f-809e191446ea"/>
				<box>
					<topPen lineWidth="1.0" lineColor="#EDEDED"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<text><![CDATA[Resultado:]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="277" width="130" height="18" uuid="f0ffe92c-0045-4e15-8cbb-f270d415e3ff"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<text><![CDATA[Resultado:]]></text>
			</staticText>
			<textField evaluationTime="Page">
				<reportElement x="140" y="285" width="130" height="22" uuid="981d4d46-ef9b-4508-a457-e452d90acd1b"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{composicaoResultado}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Page">
				<reportElement x="0" y="295" width="130" height="22" uuid="dd84432b-f0e4-4945-8584-a890faccfdbb"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{imcResultado}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Page">
				<reportElement x="0" y="250" width="65" height="18" uuid="5989a586-cc01-4cb1-adee-501d3eb82718"/>
				<box>
					<rightPen lineWidth="1.0" lineColor="#EDEDED"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{altura}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Page">
				<reportElement x="65" y="250" width="65" height="18" uuid="da33576d-7d0e-4f2e-9303-15394a9aac8c"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{peso}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="268" width="65" height="9" forecolor="#000000" uuid="7e28adc0-b71a-44fa-9148-f9f19a47c0c0"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#EDEDED"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#EDEDED"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<text><![CDATA[Altura]]></text>
			</staticText>
			<staticText>
				<reportElement x="65" y="268" width="65" height="9" forecolor="#000000" uuid="7ad4f43f-8055-4326-b2fe-041951fbcb98"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#EDEDED"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="5"/>
				</textElement>
				<text><![CDATA[Peso]]></text>
			</staticText>
			<textField evaluationTime="Page">
				<reportElement x="148" y="187" width="115" height="15" uuid="c15fe49a-1c26-464f-bf17-66b4186cf01c"/>
				<box>
					<leftPen lineWidth="4.0" lineColor="#EDDA07"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["  Gordura: " + $P{percGordura}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Page">
				<reportElement x="148" y="208" width="115" height="15" uuid="9b953230-6e15-4a32-8d80-b04fac5d2cec"/>
				<box>
					<leftPen lineWidth="4.0" lineColor="#005AC2"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["  Resíduos: " + $P{percResiduos}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Page">
				<reportElement x="148" y="229" width="115" height="15" uuid="c52fe3b9-697f-4a7f-8bc2-8dbfbe3ca032"/>
				<box>
					<leftPen lineWidth="4.0" lineColor="#00A803"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["  Ossos: " + $P{percOssos}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Page">
				<reportElement x="148" y="248" width="115" height="15" uuid="56795143-5e18-4735-b4ce-cbb8064c8750"/>
				<box>
					<pen lineWidth="0.0"/>
					<leftPen lineWidth="4.0" lineColor="#FF0000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["  Músculos: " + $P{percMusculos}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Page">
				<reportElement x="424" y="239" width="118" height="18" uuid="06951055-8e5b-436b-89d8-b53ab927145c"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{peso1}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Page">
				<reportElement x="424" y="259" width="118" height="18" uuid="79734f45-ee34-4a90-a310-27401379d89d"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{peso2}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Page">
				<reportElement x="424" y="299" width="118" height="18" uuid="0783edec-4990-4834-beed-c6c8cae6b5a8"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{peso4}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Page">
				<reportElement x="424" y="279" width="118" height="18" uuid="2d621251-f12b-4620-a828-3114836da7eb"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{peso3}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Page">
				<reportElement x="140" y="328" width="130" height="22" uuid="707ceea1-bc4c-419a-a7bd-851586cc7220">
					<printWhenExpression><![CDATA[$P{showMeta}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{metaGordura}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="140" y="310" width="130" height="18" uuid="8a3526b5-f49e-4352-9c98-5f9fbc97c99f">
					<printWhenExpression><![CDATA[$P{showMeta}]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineColor="#EDEDED"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<text><![CDATA[Meta de % de gordura da próxima avaliação:]]></text>
			</staticText>
		</band>
		<band height="99">
			<printWhenExpression><![CDATA[$P{showobjetivos}]]></printWhenExpression>
			<rectangle>
				<reportElement x="0" y="3" width="550" height="41" backcolor="#EDEDED" uuid="763366b1-d4c1-4bde-9d00-e5968643e21e"/>
				<graphicElement>
					<pen lineWidth="0.0" lineColor="#EDEDED"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="0" y="3" width="550" height="41" uuid="5dbc53dd-6516-46f7-b5dd-9cc9e9035c8e"/>
				<box leftPadding="0">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[Objetivos do Aluno]]></text>
			</staticText>
			<textField evaluationTime="Page">
				<reportElement x="0" y="54" width="550" height="39" uuid="2063ba67-8365-4bf0-8363-c167f62fe41a"/>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{objetivosAluno}]]></textFieldExpression>
			</textField>
		</band>
		<band height="263" splitType="Stretch">
			<printWhenExpression><![CDATA[$P{showrecomendacoes}]]></printWhenExpression>
			<rectangle>
				<reportElement x="0" y="5" width="550" height="41" backcolor="#EDEDED" uuid="bf0f5fde-9ae4-438f-918d-99d9306a1825"/>
				<graphicElement>
					<pen lineWidth="0.0" lineColor="#EDEDED"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="0" y="5" width="550" height="41" uuid="ca7c4ffa-9420-418e-b937-f8ba30850bc1"/>
				<box leftPadding="0">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[Recomendações do Avaliador]]></text>
			</staticText>
			<rectangle radius="10">
				<reportElement stretchType="RelativeToTallestObject" x="1" y="53" width="550" height="210" uuid="4bffceb2-3d13-4478-9b44-d113841d0867"/>
			</rectangle>
			<textField isStretchWithOverflow="true">
				<reportElement x="10" y="60" width="532" height="190" uuid="7254255b-89f2-428b-935f-875d6edcbc91"/>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{recomendacoes}]]></textFieldExpression>
			</textField>
		</band>
		<band height="145" splitType="Stretch">
			<printWhenExpression><![CDATA[$P{showanamnese}]]></printWhenExpression>
			<rectangle>
				<reportElement x="0" y="8" width="550" height="41" backcolor="#EDEDED" uuid="8dac5012-d74a-49c9-863d-1356b5962e4f"/>
				<graphicElement>
					<pen lineWidth="0.0" lineColor="#EDEDED"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="0" y="8" width="550" height="41" uuid="dbc64f20-bd77-4e90-90da-cc90a607fa01"/>
				<box leftPadding="0">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[Anamnese]]></text>
			</staticText>
			<break>
				<reportElement isPrintRepeatedValues="false" x="0" y="5" width="550" height="1" uuid="802aa22d-354a-4f56-aa64-7db8c8c89d66"/>
			</break>
			<subreport>
				<reportElement stretchType="ContainerHeight" x="0" y="49" width="550" height="91" uuid="9e56da40-ce35-4edb-9344-fe445bfef31d"/>
				<dataSourceExpression><![CDATA[$P{anamneseJR}]]></dataSourceExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "anamnese.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="332">
			<printWhenExpression><![CDATA[$P{showparq}]]></printWhenExpression>
			<rectangle>
				<reportElement x="0" y="10" width="550" height="41" backcolor="#EDEDED" uuid="c3e8ae52-832a-40dc-963e-978a868cd5a7"/>
				<graphicElement>
					<pen lineWidth="0.0" lineColor="#EDEDED"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="0" y="10" width="550" height="41" uuid="8e045dde-7444-4c64-9fc3-df5c68aeb9f3"/>
				<box leftPadding="0">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[Par-Q]]></text>
			</staticText>
			<break>
				<reportElement isPrintRepeatedValues="false" x="0" y="5" width="549" height="1" uuid="19be9370-a4ee-4069-8efd-0039181d082e"/>
			</break>
			<subreport>
				<reportElement x="2" y="59" width="550" height="31" uuid="1e516440-259d-4673-8cd3-9fc153fe54fa"/>
				<dataSourceExpression><![CDATA[$P{parqJR}]]></dataSourceExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "anamnese.jasper"]]></subreportExpression>
			</subreport>
			<textField evaluationTime="Page">
				<reportElement positionType="Float" x="126" y="122" width="308" height="45" uuid="b6f72208-e386-4498-acdd-baa340a8deb5"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="20" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{parq}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="126" y="96" width="308" height="25" uuid="a54c799a-4572-41f4-9ab9-b90dd41aa7c0"/>
				<box leftPadding="0">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="false"/>
				</textElement>
				<text><![CDATA[Resultado:]]></text>
			</staticText>
			<image scaleImage="RetainShape" hAlign="Center" vAlign="Middle" isUsingCache="false" onErrorType="Icon" evaluationTime="Report">
				<reportElement positionType="Float" x="0" y="172" width="553" height="127" uuid="e4c4983f-78b4-4d69-87a8-7877926b397b">
					<printWhenExpression><![CDATA[$P{apresentarAssinatura}]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[$P{assinatura}]]></imageExpression>
			</image>
			<staticText>
				<reportElement positionType="Float" x="80" y="304" width="400" height="25" uuid="89956fdc-b170-488b-b2db-0eb861f567af">
					<printWhenExpression><![CDATA[$P{apresentarAssinatura}]]></printWhenExpression>
				</reportElement>
				<box leftPadding="0">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Assinatura do Avaliado]]></text>
			</staticText>
		</band>
		<band height="165">
			<rectangle>
				<reportElement x="0" y="8" width="550" height="41" backcolor="#EDEDED" uuid="338584e7-45d0-444d-abd4-399151e79c3b"/>
				<graphicElement>
					<pen lineWidth="0.0" lineColor="#EDEDED"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="0" y="8" width="550" height="41" uuid="727e7344-b515-4794-a4b8-c990be280ba0"/>
				<box leftPadding="0">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[Peso, Altura, Pressão Arterial e Frequência Cardíaca]]></text>
			</staticText>
			<break>
				<reportElement isPrintRepeatedValues="false" x="0" y="3" width="546" height="1" uuid="354dea96-0fb9-4a76-9e2b-087760379ba5"/>
			</break>
			<line>
				<reportElement x="137" y="59" width="1" height="90" uuid="e1de16f0-5559-4130-b1b7-0083090b5172"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#EDEDED"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="274" y="59" width="1" height="90" uuid="6d36980b-6f85-4576-ad0a-f6b2f1769416"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#EDEDED"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="412" y="59" width="1" height="90" uuid="2bc6ba71-6a08-4dc5-be99-4b4d1009d444"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#EDEDED"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement x="4" y="59" width="130" height="15" uuid="a71d0dae-121b-4d66-ade8-7c93471cff50"/>
				<box>
					<pen lineWidth="1.0" lineColor="#CCCCCC"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#EDEDED"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Peso Atual]]></text>
			</staticText>
			<staticText>
				<reportElement x="141" y="59" width="130" height="15" uuid="*************-415d-b142-a48bac6c332e"/>
				<box>
					<pen lineWidth="1.0" lineColor="#CCCCCC"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#EDEDED"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Altura]]></text>
			</staticText>
			<staticText>
				<reportElement x="279" y="59" width="130" height="15" uuid="0bc83b1c-315b-4ba1-8251-2eed4c1a5b29"/>
				<box>
					<pen lineWidth="1.0" lineColor="#CCCCCC"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#EDEDED"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Pressão Arterial]]></text>
			</staticText>
			<staticText>
				<reportElement x="416" y="59" width="130" height="15" uuid="9413f1ff-85f8-421e-86ee-c1697a15ab38"/>
				<box>
					<pen lineWidth="1.0" lineColor="#CCCCCC"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#EDEDED"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Freq. Cardíaca em repouso]]></text>
			</staticText>
			<textField evaluationTime="Page">
				<reportElement x="4" y="80" width="130" height="45" uuid="00b39965-33b0-4a2e-b252-0183eea14e64"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="28" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{pesoAtual}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="4" y="125" width="130" height="18" forecolor="#000000" uuid="89b1b0de-e409-490a-a35e-9915fc439129"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#EDEDED"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<text><![CDATA[quilos]]></text>
			</staticText>
			<textField evaluationTime="Page">
				<reportElement x="141" y="80" width="130" height="45" uuid="77807d5a-fab2-4b17-91b8-e8e47843a9ef"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="28" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{alturaAtual}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="141" y="125" width="130" height="18" forecolor="#000000" uuid="1e26c139-925c-4e35-8b9f-e935ad9d105f"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#EDEDED"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<text><![CDATA[metros]]></text>
			</staticText>
			<staticText>
				<reportElement x="279" y="125" width="130" height="18" forecolor="#000000" uuid="425559d9-7239-4f7a-82ad-8980aad5b623"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#EDEDED"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<text><![CDATA[mmHg]]></text>
			</staticText>
			<textField evaluationTime="Page">
				<reportElement x="279" y="80" width="130" height="45" uuid="aab30418-56c4-4d07-be9d-9c2bd5f24ebe"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="28" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{pressaoArterial}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Page">
				<reportElement x="416" y="80" width="130" height="45" uuid="c5d453d5-ff11-4ed3-bd72-8bfc42989165"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="28" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{freqCardiaca}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="416" y="125" width="130" height="18" forecolor="#000000" uuid="bb4f9eca-810f-44ce-8c3b-91acc5ff37e8"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#EDEDED"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<text><![CDATA[bpm]]></text>
			</staticText>
		</band>
		<band height="99">
			<printWhenExpression><![CDATA[$P{showdobras}]]></printWhenExpression>
			<rectangle>
				<reportElement x="0" y="7" width="550" height="41" backcolor="#EDEDED" uuid="8d8b9a0e-466a-4a45-aad4-6644d570e416"/>
				<graphicElement>
					<pen lineWidth="0.0" lineColor="#EDEDED"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="0" y="7" width="550" height="41" uuid="70d90ff6-107b-4df3-9a09-6eb6e6f1add4"/>
				<box leftPadding="0">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[Dobras cutâneas]]></text>
			</staticText>
			<textField evaluationTime="Page">
				<reportElement x="0" y="56" width="550" height="16" uuid="6a3f801b-375c-4a0a-92ab-33af60cedd1f"/>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[" Protocolo: " + $P{protocolo}]]></textFieldExpression>
			</textField>
			<subreport>
				<reportElement positionType="Float" x="0" y="76" width="550" height="16" uuid="d72fcdaf-c75f-4423-a93b-f81e9dfcc4cf"/>
				<dataSourceExpression><![CDATA[$P{dobrasJR}]]></dataSourceExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "dobras.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="185">
			<printWhenExpression><![CDATA[$P{showperimetria}]]></printWhenExpression>
			<rectangle>
				<reportElement positionType="Float" x="0" y="3" width="550" height="41" backcolor="#EDEDED" uuid="b135e539-6f7d-40f1-93c8-b060280a0c3b"/>
				<graphicElement>
					<pen lineWidth="0.0" lineColor="#EDEDED"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement positionType="Float" x="0" y="3" width="550" height="41" uuid="2e16631d-683d-4ba5-9dd5-1ebec8822b86"/>
				<box leftPadding="0">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[Perimetria e diâmetros]]></text>
			</staticText>
			<subreport>
				<reportElement positionType="Float" x="1" y="56" width="275" height="66" uuid="32a8e441-18cf-4e76-b5ab-e26b5873aece"/>
				<dataSourceExpression><![CDATA[$P{perimetriaJR}]]></dataSourceExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "perimetria.jasper"]]></subreportExpression>
			</subreport>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="460" y="81" width="91" height="20" uuid="254ad8ae-1d3e-4934-864d-4073cc493c00">
					<printWhenExpression><![CDATA[!$P{diametroPunho}.equals("")]]></printWhenExpression>
				</reportElement>
				<box>
					<bottomPen lineWidth="1.0" lineColor="#EDEDED"/>
				</box>
				<textFieldExpression><![CDATA[$P{diametroPunho}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="280" y="81" width="170" height="20" uuid="5b56327d-1ce2-4903-8aea-b3d745ef0e7a">
					<printWhenExpression><![CDATA[!$P{diametroPunho}.equals("")]]></printWhenExpression>
				</reportElement>
				<box>
					<bottomPen lineWidth="1.0" lineColor="#EDEDED"/>
				</box>
				<text><![CDATA[Diâmetro do punho ]]></text>
			</staticText>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="460" y="103" width="91" height="20" uuid="efbcf721-0660-4b86-959e-4928e1649270">
					<printWhenExpression><![CDATA[!$P{diametroJoelho}.equals("")]]></printWhenExpression>
				</reportElement>
				<box>
					<bottomPen lineWidth="1.0" lineColor="#EDEDED"/>
				</box>
				<textFieldExpression><![CDATA[$P{diametroJoelho}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="280" y="103" width="170" height="20" uuid="66df6e95-b01d-4fd7-b273-bf7d2aa57624">
					<printWhenExpression><![CDATA[!$P{diametroJoelho}.equals("")]]></printWhenExpression>
				</reportElement>
				<box>
					<bottomPen lineWidth="1.0" lineColor="#EDEDED"/>
				</box>
				<text><![CDATA[Diâmetro do joelho ]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="280" y="56" width="180" height="24" uuid="1af241e6-4a3f-47d9-af21-797fb0e4c01e">
					<printWhenExpression><![CDATA[$P{showOsseo}]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Diâmetros ósseos (cm)]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="280" y="147" width="170" height="20" uuid="900bd2d0-9a25-4d3b-8a9e-f204d2efddef">
					<printWhenExpression><![CDATA[!$P{diametroTornozelo}.equals("")]]></printWhenExpression>
				</reportElement>
				<box>
					<bottomPen lineWidth="1.0" lineColor="#EDEDED"/>
				</box>
				<text><![CDATA[Diâmetro do tornozelo]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="280" y="125" width="170" height="20" uuid="7abac95f-56e9-4572-949d-bce8616e16c6">
					<printWhenExpression><![CDATA[!$P{diametroCotovelo}.equals("")]]></printWhenExpression>
				</reportElement>
				<box>
					<bottomPen lineWidth="1.0" lineColor="#EDEDED"/>
				</box>
				<text><![CDATA[Diâmetro do cotovelo ]]></text>
			</staticText>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="460" y="125" width="91" height="20" uuid="c99762f1-d42d-4249-82a7-f4364f5ce112">
					<printWhenExpression><![CDATA[!$P{diametroCotovelo}.equals("")]]></printWhenExpression>
				</reportElement>
				<box>
					<bottomPen lineWidth="1.0" lineColor="#EDEDED"/>
				</box>
				<textFieldExpression><![CDATA[$P{diametroCotovelo}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="460" y="147" width="91" height="20" uuid="f3ad01c3-0d09-4256-80f0-225868a78386">
					<printWhenExpression><![CDATA[!$P{diametroTornozelo}.equals("")]]></printWhenExpression>
				</reportElement>
				<box>
					<bottomPen lineWidth="1.0" lineColor="#EDEDED"/>
				</box>
				<textFieldExpression><![CDATA[$P{diametroTornozelo}]]></textFieldExpression>
			</textField>
		</band>
		<band height="161">
			<rectangle>
				<reportElement positionType="Float" x="0" y="3" width="550" height="41" backcolor="#EDEDED" uuid="843eda60-cf3a-4f73-a760-4e6583a63678"/>
				<graphicElement>
					<pen lineWidth="0.0" lineColor="#EDEDED"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement positionType="Float" x="0" y="3" width="550" height="41" uuid="74732b22-df7a-4ae9-8304-b43852faa6f3"/>
				<box leftPadding="0">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[Composição Corporal]]></text>
			</staticText>
			<line>
				<reportElement positionType="Float" x="136" y="58" width="1" height="90" uuid="5a512029-cd06-4198-a9ec-86fc5c6f6887"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#EDEDED"/>
				</graphicElement>
			</line>
			<line>
				<reportElement positionType="Float" x="273" y="58" width="1" height="90" uuid="61aa9800-2b8d-48ad-a4f7-2c0aae46b8d4"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#EDEDED"/>
				</graphicElement>
			</line>
			<line>
				<reportElement positionType="Float" x="411" y="58" width="1" height="90" uuid="bdb3096a-51c2-48c4-ad84-1aa801dc5774"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#EDEDED"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement positionType="Float" x="3" y="58" width="130" height="15" uuid="8f3607be-8cc9-4369-a26d-b3c0438a43ee"/>
				<box>
					<pen lineWidth="1.0" lineColor="#CCCCCC"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#EDEDED"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Peso de Gordura]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="140" y="58" width="130" height="15" uuid="6c7f06d7-b14e-45b9-ad0a-0fd8d16fa8f4"/>
				<box>
					<pen lineWidth="1.0" lineColor="#CCCCCC"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#EDEDED"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Peso Residual]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="278" y="58" width="130" height="15" uuid="ece1fde7-1f82-4bd9-b660-41e87cc68d69"/>
				<box>
					<pen lineWidth="1.0" lineColor="#CCCCCC"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#EDEDED"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Peso Muscular]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="415" y="58" width="130" height="15" uuid="e985d49f-ad3e-4dfe-9334-830e136f89b4"/>
				<box>
					<pen lineWidth="1.0" lineColor="#CCCCCC"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#EDEDED"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Peso Ósseo]]></text>
			</staticText>
			<textField evaluationTime="Page">
				<reportElement positionType="Float" x="3" y="79" width="130" height="45" uuid="a5bfb256-dacd-44d2-ad1a-c97239033745"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="28" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{pesoGordura}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="3" y="124" width="130" height="18" forecolor="#000000" uuid="0e2df2cb-5e54-41fe-8e4c-0076ed204656"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#EDEDED"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<text><![CDATA[quilos]]></text>
			</staticText>
			<textField evaluationTime="Page">
				<reportElement positionType="Float" x="140" y="79" width="130" height="45" uuid="a33e135a-be59-455e-828f-d34626cb6f56"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="28" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{pesoResidual}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="140" y="124" width="130" height="18" forecolor="#000000" uuid="40a8e2a1-28f2-4f12-bcb8-6c07d3097f5a"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#EDEDED"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<text><![CDATA[quilos]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="278" y="124" width="130" height="18" forecolor="#000000" uuid="36e15e40-a27e-46c1-857b-fef96a185e3f"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#EDEDED"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<text><![CDATA[quilos]]></text>
			</staticText>
			<textField evaluationTime="Page">
				<reportElement positionType="Float" x="278" y="79" width="130" height="45" uuid="62e72fb2-c7b7-4395-872c-132125a19aa2"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="28" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{pesoMuscular}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Page">
				<reportElement positionType="Float" x="415" y="79" width="130" height="45" uuid="fc080963-2933-4bec-95e8-aecb8294267d"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="28" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{pesoOsseo}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="415" y="124" width="130" height="18" forecolor="#000000" uuid="ed41ce6a-4329-4d34-aaa9-f270391eaa26"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#EDEDED"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<text><![CDATA[quilos]]></text>
			</staticText>
		</band>
		<band height="168">
			<printWhenExpression><![CDATA[$P{showflexibilidade}]]></printWhenExpression>
			<rectangle>
				<reportElement positionType="Float" x="0" y="1" width="550" height="41" backcolor="#EDEDED" uuid="490e84f0-80e8-4fc1-b0a0-b9becadd21a5"/>
				<graphicElement>
					<pen lineWidth="0.0" lineColor="#EDEDED"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement positionType="Float" x="0" y="1" width="550" height="41" uuid="120b7e39-6d20-407b-8922-cabe5a3d6f32"/>
				<box leftPadding="0">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[Flexibilidade]]></text>
			</staticText>
			<line>
				<reportElement positionType="Float" x="411" y="54" width="1" height="100" uuid="bcb04741-7769-48fd-9c3e-35d809a71488"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#EDEDED"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement positionType="Float" x="424" y="56" width="125" height="16" uuid="a3285c18-4349-430d-bb32-2fd9cba7674d"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Tabela de Referência]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="424" y="72" width="125" height="16" uuid="bc0a2164-d758-48fe-96ca-c9c3b85c81bf"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false"/>
				</textElement>
				<text><![CDATA[Fraca: -24]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="424" y="88" width="125" height="16" uuid="f4233a81-c454-4f51-94f4-b434ecc07a6a"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false"/>
				</textElement>
				<text><![CDATA[Regular: 24 - 28]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="424" y="104" width="125" height="16" uuid="244f7ab5-2f79-4f61-beba-5ad2ebdac447"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false"/>
				</textElement>
				<text><![CDATA[Média: 29 - 33]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="424" y="120" width="125" height="16" uuid="5a27de6c-440a-4f20-85ad-37baffd4dd2e"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false"/>
				</textElement>
				<text><![CDATA[Boa: 34 - 38]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="424" y="136" width="125" height="16" uuid="6e54804b-20f8-4548-a53a-e04256761de2"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false"/>
				</textElement>
				<text><![CDATA[Excelente: + 38]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="3" y="56" width="217" height="26" uuid="6cb781db-cb99-4e52-931b-b8d188eb75d8"/>
				<textElement verticalAlignment="Middle">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Banco de Wells & Dilon]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="3" y="82" width="90" height="16" uuid="946c4d88-e259-4d09-bb9b-2bcadcef7d7a"/>
				<textElement verticalAlignment="Middle">
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Alcance máximo:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="3" y="98" width="77" height="16" uuid="d1b54fc0-8c6e-4894-8797-772605923fed"/>
				<textElement verticalAlignment="Middle">
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Classificação:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="3" y="114" width="77" height="16" uuid="d65ccad4-d4ce-4ff5-b47c-7e10574e496b"/>
				<textElement verticalAlignment="Middle">
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Observações:]]></text>
			</staticText>
			<textField>
				<reportElement positionType="Float" x="93" y="82" width="127" height="16" uuid="78deb9af-a78e-4741-a35b-f7417f3c9697"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{alcanceMaximo}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="80" y="98" width="140" height="16" uuid="fa289324-f59d-4935-b7c7-579d76e1ec2f"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{classificacaoFlexibilidade}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="3" y="130" width="405" height="26" uuid="3f248ffd-be28-40be-9b0a-c952a28bbc8a"/>
				<textElement textAlignment="Left" verticalAlignment="Top"/>
				<textFieldExpression><![CDATA[$P{obsFlexibilidade}]]></textFieldExpression>
			</textField>
		</band>
		<band height="419" splitType="Stretch">
			<printWhenExpression><![CDATA[$P{showpostural}]]></printWhenExpression>
			<rectangle>
				<reportElement positionType="Float" x="0" y="1" width="550" height="41" backcolor="#EDEDED" uuid="47ace45b-c64c-4cae-8301-b17d9c6a9168"/>
				<graphicElement>
					<pen lineWidth="0.0" lineColor="#EDEDED"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement positionType="Float" x="0" y="1" width="550" height="41" uuid="c2a23846-8903-4768-8fdc-9064cd8063fa"/>
				<box leftPadding="0">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[Avaliação Postural]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="7" y="185" width="77" height="16" uuid="6a2e90d5-ff68-4f94-9c0f-538498bc8f19"/>
				<textElement verticalAlignment="Middle">
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Visão lateral:]]></text>
			</staticText>
			<textField>
				<reportElement positionType="Float" x="7" y="201" width="546" height="26" uuid="4ece8422-2773-45d5-be81-ad487a8ce0d0"/>
				<textElement textAlignment="Left" verticalAlignment="Top"/>
				<textFieldExpression><![CDATA[$P{visaoLateral}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="7" y="244" width="546" height="26" uuid="9ca129ad-6d3e-48b0-b2c2-cdef62ea75df"/>
				<textElement textAlignment="Left" verticalAlignment="Top"/>
				<textFieldExpression><![CDATA[$P{posterior}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="7" y="228" width="77" height="16" uuid="2a47157f-1993-47ba-83b8-05f4e0a84d94"/>
				<textElement verticalAlignment="Middle">
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Posterior:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="7" y="270" width="77" height="16" uuid="9a78c96e-41b3-46f2-a7dc-2868a893c214"/>
				<textElement verticalAlignment="Middle">
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Anterior:]]></text>
			</staticText>
			<textField>
				<reportElement positionType="Float" x="7" y="286" width="546" height="26" uuid="a9498018-2513-4b45-8dfa-0017af84267f"/>
				<textElement textAlignment="Left" verticalAlignment="Top"/>
				<textFieldExpression><![CDATA[$P{anterior}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="7" y="317" width="77" height="16" uuid="a708d18c-8440-4edc-91e8-9e108b705d0c"/>
				<textElement verticalAlignment="Middle">
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Assimetrias:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="20" y="338" width="190" height="16" uuid="af8fdbdc-2786-4c7d-96da-0e9b716e895f"/>
				<textElement verticalAlignment="Middle">
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Ombros assimétricos:]]></text>
			</staticText>
			<textField>
				<reportElement positionType="Float" x="211" y="338" width="288" height="16" uuid="8ff958c2-ee9f-475f-8ba7-2e364f8111d1"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{ombrosAssimetricos}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="211" y="354" width="286" height="16" uuid="f3d8b55d-6959-48d9-a336-00de6addd282"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{assimetriaQuadril}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="20" y="354" width="190" height="16" uuid="8effc856-e86d-4945-97b4-c0ebd1221ec3"/>
				<textElement verticalAlignment="Middle">
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Assimetria de quadril:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="7" y="377" width="77" height="16" uuid="9b0e04da-0fef-45f4-8f0b-90300ace0b52"/>
				<textElement verticalAlignment="Middle">
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Observações:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" stretchType="ContainerHeight" isPrintRepeatedValues="false" x="7" y="393" width="490" height="26" isPrintWhenDetailOverflows="true" uuid="5ac409e5-5022-490c-a778-4d9737954c0c"/>
				<textElement textAlignment="Left" verticalAlignment="Top"/>
				<textFieldExpression><![CDATA[$P{obsPostural}]]></textFieldExpression>
			</textField>
			<image>
				<reportElement x="28" y="50" width="86" height="119" uuid="b617bb34-221e-41f6-b88b-1f3fe1264ee6">
					<printWhenExpression><![CDATA[$P{urlFrente} != null]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[$P{urlFrente}]]></imageExpression>
			</image>
			<image>
				<reportElement x="165" y="50" width="86" height="119" uuid="f7243bb5-7cfe-4a2f-a09f-c99545f9d5dd">
					<printWhenExpression><![CDATA[$P{urlDireita} != null]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[$P{urlDireita}]]></imageExpression>
			</image>
			<image>
				<reportElement x="439" y="50" width="86" height="119" uuid="d84b7764-3d84-4775-bde4-65b666a558a7">
					<printWhenExpression><![CDATA[$P{urlCosta} != null]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[$P{urlCosta}]]></imageExpression>
			</image>
			<image>
				<reportElement x="302" y="50" width="86" height="119" uuid="60578a42-0ec6-4a0f-9415-334cff77ceef">
					<printWhenExpression><![CDATA[$P{urlEsquerda} != null]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[$P{urlEsquerda}]]></imageExpression>
			</image>
		</band>
		<band height="107">
			<printWhenExpression><![CDATA[$P{showrml}]]></printWhenExpression>
			<rectangle>
				<reportElement positionType="Float" x="0" y="1" width="550" height="41" backcolor="#EDEDED" uuid="ee90daf2-2669-4f86-a52a-fded102866fc"/>
				<graphicElement>
					<pen lineWidth="0.0" lineColor="#EDEDED"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement positionType="Float" x="0" y="1" width="550" height="41" uuid="05a57001-0b2a-4ba3-9c8a-4ac718b1d7c7"/>
				<box leftPadding="0">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[RML]]></text>
			</staticText>
			<textField>
				<reportElement positionType="Float" x="3" y="57" width="368" height="16" uuid="09621502-6128-4e32-9f45-564ea6dc822f"/>
				<textElement verticalAlignment="Middle" markup="html"/>
				<textFieldExpression><![CDATA["<b>RML de Braço - Flexões:</b> " + $P{rmlBraco}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="3" y="77" width="405" height="16" uuid="6b47a6f1-063d-422a-bfcd-21ba7615ae0b"/>
				<textElement verticalAlignment="Middle" markup="html"/>
				<textFieldExpression><![CDATA["<b>RML de Abdômen - Abdominais:</b> " + $P{rmlAbdomen}]]></textFieldExpression>
			</textField>
		</band>
		<band height="47">
			<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
			<printWhenExpression><![CDATA[$P{showvo2max} || $P{showQueens} || $P{showvo2max}==Boolean.FALSE && $P{showAstrand}]]></printWhenExpression>
			<rectangle>
				<reportElement positionType="Float" x="0" y="1" width="550" height="41" backcolor="#EDEDED" uuid="cca42aef-ea83-465e-a1ad-a9b09883f988"/>
				<graphicElement>
					<pen lineWidth="0.0" lineColor="#EDEDED"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement positionType="Float" x="0" y="1" width="550" height="41" uuid="fbc8b3ea-05fa-4584-9bbb-ed8b33548705"/>
				<box leftPadding="0">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[Vo2Max]]></text>
			</staticText>
		</band>
		<band height="105">
			<printWhenExpression><![CDATA[$P{showvo2max}==Boolean.FALSE && $P{showAstrand}]]></printWhenExpression>
			<line>
				<reportElement positionType="Float" x="280" y="4" width="1" height="96" uuid="93f8c562-0dad-4e76-9d7d-cbd5d6e512a1">
					<printWhenExpression><![CDATA[$P{showAstrand}]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#EDEDED"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement positionType="Float" stretchType="ContainerBottom" x="286" y="0" width="260" height="26" uuid="cfc9a481-f97e-4051-a514-23db16c732a4">
					<printWhenExpression><![CDATA[$P{showAstrand}]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Protocolo Astrand em Cicloergômetro]]></text>
			</staticText>
			<textField>
				<reportElement x="387" y="26" width="100" height="16" uuid="fa4c7d42-51fc-44b8-968b-e2aea88094a7">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[$P{showAstrand}]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{frequenciaAstrand}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" stretchType="ElementGroupHeight" x="287" y="26" width="100" height="16" uuid="d48b901e-9254-4b8b-b063-369c75350cb7">
					<printWhenExpression><![CDATA[$P{showAstrand}]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font size="10" isBold="false"/>
				</textElement>
				<text><![CDATA[Frequência Cardiaca:]]></text>
			</staticText>
			<textField>
				<reportElement x="387" y="42" width="140" height="16" uuid="937b8ec6-3bc0-417a-b88b-4ba1bc0e9099">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[$P{showAstrand}]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{cargaAstrand}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" stretchType="ElementGroupHeight" x="287" y="42" width="100" height="16" uuid="8e82eae5-3407-43d1-9843-fede299d54cf">
					<printWhenExpression><![CDATA[$P{showAstrand}]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font size="10" isBold="false"/>
				</textElement>
				<text><![CDATA[Carga:]]></text>
			</staticText>
			<textField>
				<reportElement x="387" y="58" width="140" height="16" uuid="b66bfbfe-9627-4d47-b6bc-66ad379cdfce">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[$P{showAstrand}]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{vo2Astrand}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" stretchType="ElementGroupHeight" x="287" y="58" width="100" height="16" uuid="86677e76-783f-4ee5-bb62-bdbf2da3b0f2">
					<printWhenExpression><![CDATA[$P{showAstrand}]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font size="10" isBold="false"/>
				</textElement>
				<text><![CDATA[VO2:]]></text>
			</staticText>
			<textField>
				<reportElement x="387" y="74" width="140" height="16" uuid="812f7ff7-4fb7-4156-941b-fa479b0f085f">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[$P{showAstrand}]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{vo2MaxAstrand}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" stretchType="ElementGroupHeight" x="287" y="74" width="100" height="16" uuid="a441c631-3752-43c7-b813-65e5af693a32">
					<printWhenExpression><![CDATA[$P{showAstrand}]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font size="10" isBold="false"/>
				</textElement>
				<text><![CDATA[VO2 Max:]]></text>
			</staticText>
		</band>
		<band height="105">
			<printWhenExpression><![CDATA[$P{showvo2max}]]></printWhenExpression>
			<staticText>
				<reportElement positionType="Float" x="3" y="0" width="240" height="26" uuid="e1c12806-2aa3-4ecf-8fe9-e7c708bae1a8">
					<printWhenExpression><![CDATA[$P{showventilometria}]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Ventilometria VO2 ]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" stretchType="ElementGroupHeight" x="4" y="25" width="47" height="16" uuid="d3cafacc-e38a-4d86-89cc-ef0097363a5f">
					<printWhenExpression><![CDATA[$P{showventilometria}]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font size="10" isBold="false"/>
				</textElement>
				<text><![CDATA[Vo2 Max:]]></text>
			</staticText>
			<textField>
				<reportElement positionType="Float" x="51" y="25" width="127" height="16" uuid="c471dba6-6ebc-4acf-a071-ff6d0a70b73a">
					<printWhenExpression><![CDATA[$P{showventilometria}]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{vo2}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="104" y="41" width="140" height="16" uuid="e02520e0-a293-4bce-a14b-0b1aeb8717c1">
					<printWhenExpression><![CDATA[$P{showventilometria}]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{limiar1}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" stretchType="ElementGroupHeight" x="4" y="41" width="100" height="16" uuid="97c2710c-0e31-4214-b2ff-cd58b608714b">
					<printWhenExpression><![CDATA[$P{showventilometria}]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font size="10" isBold="false"/>
				</textElement>
				<text><![CDATA[Limiar ventilatório I:]]></text>
			</staticText>
			<textField>
				<reportElement positionType="Float" x="104" y="57" width="140" height="16" uuid="2eb45c8f-b21f-4992-92e6-6b1f98e5c65d">
					<printWhenExpression><![CDATA[$P{showventilometria}]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{limiar2}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="4" y="57" width="100" height="16" uuid="31a23223-1a10-41be-8938-45675a045f78">
					<printWhenExpression><![CDATA[$P{showventilometria}]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font size="10" isBold="false"/>
				</textElement>
				<text><![CDATA[Limiar ventilatório II:]]></text>
			</staticText>
			<line>
				<reportElement positionType="Float" x="280" y="4" width="1" height="96" uuid="93f8c562-0dad-4e76-9d7d-cbd5d6e512a1">
					<printWhenExpression><![CDATA[$P{showAstrand}]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#EDEDED"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement positionType="Float" stretchType="ContainerBottom" x="286" y="0" width="260" height="26" uuid="cfc9a481-f97e-4051-a514-23db16c732a4">
					<printWhenExpression><![CDATA[$P{showAstrand}]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Protocolo Astrand em Cicloergômetro]]></text>
			</staticText>
			<textField>
				<reportElement x="387" y="26" width="100" height="16" uuid="fa4c7d42-51fc-44b8-968b-e2aea88094a7">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[$P{showAstrand}]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{frequenciaAstrand}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" stretchType="ElementGroupHeight" x="287" y="26" width="100" height="16" uuid="d48b901e-9254-4b8b-b063-369c75350cb7">
					<printWhenExpression><![CDATA[$P{showAstrand}]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font size="10" isBold="false"/>
				</textElement>
				<text><![CDATA[Frequência Cardiaca:]]></text>
			</staticText>
			<textField>
				<reportElement x="387" y="42" width="140" height="16" uuid="937b8ec6-3bc0-417a-b88b-4ba1bc0e9099">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[$P{showAstrand}]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{cargaAstrand}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" stretchType="ElementGroupHeight" x="287" y="42" width="100" height="16" uuid="8e82eae5-3407-43d1-9843-fede299d54cf">
					<printWhenExpression><![CDATA[$P{showAstrand}]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font size="10" isBold="false"/>
				</textElement>
				<text><![CDATA[Carga:]]></text>
			</staticText>
			<textField>
				<reportElement x="387" y="58" width="140" height="16" uuid="b66bfbfe-9627-4d47-b6bc-66ad379cdfce">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[$P{showAstrand}]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{vo2Astrand}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" stretchType="ElementGroupHeight" x="287" y="58" width="100" height="16" uuid="86677e76-783f-4ee5-bb62-bdbf2da3b0f2">
					<printWhenExpression><![CDATA[$P{showAstrand}]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font size="10" isBold="false"/>
				</textElement>
				<text><![CDATA[VO2:]]></text>
			</staticText>
			<textField>
				<reportElement x="387" y="74" width="140" height="16" uuid="812f7ff7-4fb7-4156-941b-fa479b0f085f">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[$P{showAstrand}]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{vo2MaxAstrand}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" stretchType="ElementGroupHeight" x="287" y="74" width="100" height="16" uuid="a441c631-3752-43c7-b813-65e5af693a32">
					<printWhenExpression><![CDATA[$P{showAstrand}]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font size="10" isBold="false"/>
				</textElement>
				<text><![CDATA[VO2 Max:]]></text>
			</staticText>
		</band>
		<band height="98">
			<staticText>
				<reportElement positionType="Float" stretchType="ContainerBottom" x="287" y="4" width="262" height="26" uuid="43fa2e9c-fae2-4756-9c8b-22e163116bd5">
					<printWhenExpression><![CDATA[$P{showQueens}]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Protocolo Queens College (teste submáximo)]]></text>
			</staticText>
			<textField>
				<reportElement positionType="Float" x="287" y="30" width="240" height="16" uuid="6812ca4a-8bfd-4d6c-b351-9cfeb87b5706">
					<printWhenExpression><![CDATA[$P{showQueens}]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{fcQueens}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="287" y="46" width="240" height="16" uuid="7e7fcc3f-a5b3-40d4-827d-e953881ac43f">
					<printWhenExpression><![CDATA[$P{showQueens}]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{vo2Queens}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="6" y="30" width="240" height="16" uuid="a80bce5e-98f1-4fae-8ec0-77a063fa9950">
					<printWhenExpression><![CDATA[$P{showvo2max}]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{testeCampo}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="6" y="78" width="240" height="16" uuid="bb801f74-9b60-4e3f-b3c5-c429d12d3c1f">
					<printWhenExpression><![CDATA[$P{showvo2max}]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{valor3TesteCampo}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="6" y="46" width="273" height="16" uuid="1b8df94b-7522-4406-9dfc-b5dc6f1485a6">
					<printWhenExpression><![CDATA[$P{showvo2max}]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{valor1TesteCampo}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="6" y="4" width="217" height="26" uuid="1661c771-2df1-4c37-bbb1-df67312736a0">
					<printWhenExpression><![CDATA[$P{showvo2max}]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Protocolos de Campo]]></text>
			</staticText>
			<textField>
				<reportElement positionType="Float" x="6" y="62" width="240" height="16" uuid="6913d1bb-0046-40f0-b89c-419e0dfa7a16">
					<printWhenExpression><![CDATA[$P{showvo2max}]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{valor2TesteCampo}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement positionType="Float" x="280" y="1" width="1" height="96" uuid="5cefe974-7988-496c-a911-9705de074939">
					<printWhenExpression><![CDATA[$P{showvo2max}]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#EDEDED"/>
				</graphicElement>
			</line>
		</band>
		<band height="141">
			<printWhenExpression><![CDATA[$P{somatotipia}]]></printWhenExpression>
			<rectangle>
				<reportElement positionType="Float" x="0" y="2" width="550" height="41" backcolor="#EDEDED" uuid="d5315252-143d-4d4e-a2b7-9451b9f3ab35"/>
				<graphicElement>
					<pen lineWidth="0.0" lineColor="#EDEDED"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement positionType="Float" x="0" y="2" width="550" height="41" uuid="53cd515e-1947-4b22-bd7f-79b741e9a20c"/>
				<box leftPadding="0">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[Somatotipia]]></text>
			</staticText>
			<line>
				<reportElement positionType="Float" x="180" y="53" width="1" height="80" uuid="395278b0-0d77-40a5-beba-19b72303c822"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#EDEDED"/>
				</graphicElement>
			</line>
			<line>
				<reportElement positionType="Float" x="380" y="53" width="1" height="80" uuid="30d7d742-d1ff-4abd-ab36-574ec36f96e0"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#EDEDED"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement positionType="Float" x="14" y="53" width="130" height="15" uuid="18195fa9-6c8b-4771-8e63-8b411694f27b"/>
				<box>
					<pen lineWidth="1.0" lineColor="#CCCCCC"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#EDEDED"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Endomorfia]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="215" y="53" width="130" height="15" uuid="a5965957-c1a8-4a2b-b6b1-a5f0daf32f3c"/>
				<box>
					<pen lineWidth="1.0" lineColor="#CCCCCC"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#EDEDED"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Mesomorfia]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="413" y="53" width="130" height="15" uuid="658cd5e4-74fc-4108-8e6d-31525e2e2f1a"/>
				<box>
					<pen lineWidth="1.0" lineColor="#CCCCCC"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#EDEDED"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Ectomorfia]]></text>
			</staticText>
			<textField evaluationTime="Page">
				<reportElement positionType="FixRelativeToBottom" x="14" y="74" width="130" height="45" uuid="4f669dfe-66ca-4ad7-b8fe-9218c26f8f80"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="28" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{endomorfia}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Page">
				<reportElement positionType="Float" x="215" y="74" width="130" height="45" uuid="ccbae4ce-8ce8-4bee-b67f-8e24ccdb9180"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="28" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{mesomorfia}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Page">
				<reportElement positionType="Float" x="413" y="74" width="130" height="45" uuid="870f7337-596a-46a6-9f0b-cd8f04ec2ef1"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="28" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{ectomorfia}]]></textFieldExpression>
			</textField>
		</band>
		<band height="79">
			<printWhenExpression><![CDATA[$P{showcomparacoes}]]></printWhenExpression>
			<subreport>
				<reportElement positionType="Float" x="0" y="54" width="550" height="16" uuid="f1b287ad-86df-42bf-be08-aeb052e18531"/>
				<dataSourceExpression><![CDATA[$P{comparativo}]]></dataSourceExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "comparativo.jasper"]]></subreportExpression>
			</subreport>
			<rectangle>
				<reportElement positionType="Float" x="0" y="9" width="550" height="41" backcolor="#EDEDED" uuid="f91dc326-df38-4b43-a1d6-e5786e0acea6"/>
				<graphicElement>
					<pen lineWidth="0.0" lineColor="#EDEDED"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement positionType="Float" x="1" y="9" width="550" height="41" uuid="a04a2269-3c6e-4294-b02f-802f52eeac6a"/>
				<box leftPadding="0">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[Comparativo com avaliações anteriores]]></text>
			</staticText>
			<break>
				<reportElement x="0" y="5" width="550" height="1" uuid="a37a905b-f930-4002-94a8-ed51ab8d984f"/>
			</break>
		</band>
	</detail>
	<pageFooter>
		<band height="14">
			<textField evaluationTime="Auto">
				<reportElement x="0" y="0" width="550" height="14" uuid="0b203b3d-97c6-428c-9ef3-ac8216929396"/>
				<box>
					<topPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[" Usuário: "+$P{usuario}+ " - Data: "+$P{horaEmissao} +" - Página " + $V{REPORT_PAGE} + " de " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
</jasperReport>
