<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.5.1.final using JasperReports Library version 6.5.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="Anamnese" pageWidth="550" pageHeight="842" columnWidth="550" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isSummaryNewPage="true" uuid="4e97cda0-7e4a-494e-b0d4-6ae08374f035">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="New Data Adapter "/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="valor1" class="java.lang.String"/>
	<field name="valor2" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="23" splitType="Stretch">
			<textField isStretchWithOverflow="true">
				<reportElement x="0" y="0" width="550" height="20" uuid="d7a9a23d-3965-41f7-bf07-5da3e5827264"/>
				<box>
					<bottomPen lineWidth="1.0" lineColor="#EDEDED"/>
				</box>
				<textFieldExpression><![CDATA["  "+$F{valor1} + ": "+$F{valor2}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
