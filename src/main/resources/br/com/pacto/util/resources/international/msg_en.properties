# To change this template, choose Tools | Templates
# and open the template in the editor.
comum.dadosIncorretos=Incorrect data!
comum.aguardeTente=Please wait... try again!
comum.dadosGravados=Data recorded successfully!

login.usuarioNaoPermitido=Access denied!
login.usuarioInvalido=Invalid user!
login.senhaInvalida=Invalid password!
login.sucesso=Login successfully!
login.aguarde=Please wait...
login.entrar=Enter
recarregue.ranking=Voc\u00EA alterou as configura\u00E7\u00F5es de indicadores, para que as novas configura\u00E7\u00F5es sejam aplicadas, recarregue o ranking
login.facaLogin=Login
login.meLembrar=Remember me
login.esqueciSenha=Forgot your password?
mobile.token.invalido=Invalid Token!
validacao.nome=The 'name' is mandatory!
mobile.programanaoencontrado=Training Program not found. Feedback teacher!
mobile.programatreinovencido=Training expired. Feedback Teacher!
mobile.programaalunoparcelavencido=It was not possible to search for the program for the reason: "Student does not have access authorization."

ficha.salva=Sheet saved successfully!
programa.salvo=Training Program saved successfully!
atividade.adicionada.ficha=Activity added to the sheet successfully!
serie.salva=Series saved successfully!
serie.alterada=Series changed successfully!
serie.removida=Series successfully removed!
atividade.ficha.salva=Activity saved successfully!
atividade.removida=Activity successfully removed!
ficha.predefinida.salva=Predefined sheet saved!
mobile.serienaoencontrada=Serie nof found. Try again!
cadastros.serie.adicionada=Series added successfully!
validacao.nome.tipo=The fields 'name' and 'type' are mandatory!
validacao.tipo=The 'type' is mandatory!
tabela.semregistros=No records
validacao.fichaPredefinida.nomeunico=This sheet can not be saved because it has the same name as another preset sheet!
validacao.ficha.nomeunico=This sheet can not be saved because it has the same name as another sheet this program!
obrigatorio.nome=The 'name' is mandatory!
obrigatorio.dataInicio='Start Date' is mandatory!
obrigatorio.diasPorSemana='Days of Week' is mandatory!
obrigatorio.diasSemanaMaior7='Days of Week' may not be greater than seven!
obrigatorio.dataProximaRevisao='Date of next revision' is mandatory!
obrigatorio.dataTermino='Completion Date' is mandatory!
concomitante.programa=This student already has a program in the period informed!
validacao.numeroFichas=The number of records can not be greater than the number of days a week!
validacao.fichaDiaSemana=There is already a record to this day of the week!
nivel.alterado=Level the student successfully changed!
serieGravada=Series held successfully saved!
validacao.ficha.programa=This record need relationshiped with Program
validacao.ficha.naoencontrada=Record not found!
mobile.usuarioinvalido=Invalid user!
alunoCadastrado=Student registered in Treino successfully!
validacao.professor=It is necessary to inform a teacher to the student!
valorBadge=Badge value
eventoalterado=Schedule changed successfully!
eventoalteradonovoseventos=Schedule changes, new events have been created successfully!
repetirAteObg=If you want to repeat this availability, enter the date.
validacao.agenda.concomitante=Already there is an event at this time.
validacao.agenda.disponibilidade=There is no availability of the teacher on the date notified.
validacao.agenda.tipo=O tipo do agendamento \u00E9 obrigat\u00F3rio!
validacao.agenda.inicio=Informe o in\u00EDcio do agendamento!
validacao.agenda.fim=Informe o fim do agendamento!
validacao.agenda.professor=Professor \u00E9 obrigat\u00F3rio!
validacao.agenda.cliente=Informe o cliente do agendamento!
mobile.semtreinos=No workout done yet!
validacao.tipoexecucao=O tipo de execu\u00E7\u00E3o da ficha \u00E9 obrigat\u00F3rio!
consultornaodisponibilidade=Consultant does not have permission to flag availabilities!
mobile.nenhumagendamento=No scheduling!
validacao.perfil.existeUsuario=This profile can not be deleted because there is already an associated user.
msg.comportamento=The behavior is mandatory!
msg.cor=The color is mandatory!
naoExisteDisponibilidade=There is no availability for the selected registered time!
naoTemPermissaoIncluirTipos=You do not have permission to add a schedule to the types available.
naoTemPermissaoIncluirDisponibilidade=You do not have permission to add an availability.
naoTemPermissaoEditarDisponibilidade=You do not have permission to edit an availability.
naoTemPermissaoEditarEvento=You do not have permission to edit the selected event type!
editarDisponibilidadeSemAgrupamento=To edit a disponibility remove the grouping.
obrigatorio.professorMontou=The teacher who set up the training program is mandatory!
mobile.solicitacaoReagenda=Request was successful!
ajusteVazio=The 'fit' can not be empty!
serieAlterada=Series changed successfully!
validacaoalunoagendamento=The student has reached the limit of %d appointments for this event type in the period of %d days. Appointments made: %d.
validacaoalunoagendamentofaltaemintervalodias=The student has %d absence(s) for this event type in the last %d days. It is not possible to schedule again until the restriction period ends.
validacao.perfilusuario=User profile is mandatory!
disponibilidadeRemovida=Availability successfully removed!
validacao.agendamento.horaFimMenorHoraInicio=End time of the schedule can not be before the start time
entidadePossuiRelacionamento=This record can not be deleted because it has relationship with 
lembrete.agendamento=Remenber: %s on %s
agendamento.confirmado=Student confirmed schedule %s on %s
mobile.agendamentoConfirmado=Scheduling confirmed!
mobile.agendamentoCancelado=Scheduling canceled!
agendamento.cancelado=Student canceled schedule %s on %s
programa.agendarRenovacao=Schedule the renovation of your workout with the teacher
filaImpressao.nadaEnfileirado=Nothing was queued
filaImpressao.fichaEnfileirada=Data sent to the printer!
cliente.naoencontrado=Customer not found. There was a problem loading the student by ZillyonWeb
cliente.naoencontrado.treino=Customer not found. Verify record.
permissaoIncluirPrograma=You are not allowed to include a training program.
permissaoEditarPrograma=You do not have permission to edit a training program.
permissaoIncluirFichaPredifinida=You are not allowed to include a predefined record.
permissaoEditarFichaPredifinida=You do not have permission to edit a predefined record.
habilitar=Enable
desabilitar=Disable
modoFullScreen=Full Screen Mode
addAluno=Add Student to Treino
novoTreino=Go to the newest plataform
wiki=Treino in Wiki Pacto
configuracoes=Settings
agendarAluno=Schedule Student
acompanharAluno=Monitor Student
historicoExecAluno=Plays
entrarModoAtendimento=Enter Service Mode
alternarModulos=Click to Navigate to other Module
permissao=You do not have permission for this action
permissaoIdentificada=You do not have the permission %s  for %s action
#Lembre do seu compromisso: %s vezes por semana. \u00DAltimo Treino: %s
programa.lembrarCompromisso=Remember: workout %s twice a week. His last training: %s
programa.comecarCompromisso=Remember: you have made a commitment to workout %s times a week


mobile.programaFichaNaoEncontrado=No record found for this workout. Log back!
mobile.programaemrevisao=Teacher is validating your training
programa.renovado=Program successfully renewed!
obrigatorio.metodo=The Trainning Method is required!
lembrete.agendamento.novo=New scheduling: %s on %s
lembrete.agendamento.alterado=Scheduling changed: %s to %s
programa.revisado=The program was marked as reviewed!
cadastros.confirmar.exclusao=Delete record?
obrigatorio.professorCarteira=The 'teacher' of the student portfolio is required!
obrigatorio.justificativa.revisao=Say what's the jusfity
obrigatorio.alterarProximaRevisao=Say what's when is the next revision or date is invalid!
obrigatorio.jaExisteOutraRevisaoMarcada=Already other revision scheduled for %s
obrigatorio.proximaRevisaoJaExiste=Revision already exists for this date
obrigatorio.alunoSemProfessorCarteira=You must add this to the student any teacher portfolio
entidadeNomeDuplicado=J\u00E1 existe um registro com este Nome
mensagem.validacao.duracaoPreDefinida=This type of event has a pre-defined duration and does not accept appointments with different duration.
mensagem.validacao.duracaoIntervalo=This type of event has a range of durations and does not accept appointments lasting outside this range.
login.empresaInvalida=Health club not found. Contact them.
obrigatorio.alunoSemSituacaoCompativel=Status for student denied
cadastros.atividade.existente=Already exists activity with same name
cadastros.atividade.naoexiste=Activity not found. Verify the record.
cadastros.existentes=Already exists object equal this
aluno_nao_ativo_cfg_somente_aluno_ativo=Student is not active!
atividadeEstaEmFicha=Can not delete this activity because it is already at least one record.
cancelarAgendamento=This schedule can not be deleted because it has already generated notifica\u00E7\u00F5es.Se want to remove it from your schedule, change the status to canceled.
checkinrealizado=Check-in completed!
checkinalterado=Classroom changed successfully!
obrigatorio.alunoObrigatorioCheckIn=It is mandatory to inform the student to check-in.
checkoutrealizado=Check-out completed!
PERSONAL_SEM_CREDITO=O personal usa cr\u00E9ditos pr\u00E9-pagos e o saldo est\u00E1 zerado.
PARCELA_EM_ABERTO=O personal possui parcela em aberto a mais dias do que o permitido.
conexaoZillyonWebDuracaoCredito=N\u00E3o foi poss\u00EDvel conectar com o ZillyonWeb para obter a dura\u00E7\u00E3o dos cr\u00E9ditos.
alunojaadicionadoaula=Voc\u00EA j\u00E1 selecionou este aluno!
conexaoZillyonWebCfgEmpresa=N\u00E3o foi poss\u00EDvel obter as configura\u00E7\u00F5es da empresa no ZillyonWeb!
semPermissaoForcarCheckIn=Usu\u00E1rio n\u00E3o possui permiss\u00E3o para for\u00E7ar um check-in!
semPermissaEditarAula=Voc\u00EA n\u00E3o tem permiss\u00E3o para editar uma aula!
checkOutObrigatorio=O check-out deve ser informado!
checkinmaiorcheckout=Check-in n\u00E3o pode ser maior do que o check-out!
diferencaDiasCheckinCheckOut=Check-Out n\u00E3o pode ter uma diferen\u00E7a de dois dias pro Check-in!
checkinobrigatorio=Data do check-in \u00E9 obrigat\u00F3ria!
msgPermissaoCheckOut=You are not allowed to perform chekc out.
validacao.horariosAgendamentos=Tell all times of the schedule!
cadastro.programa.naopodeserexcluido=This training program can not be deleted because it has associated notifications.
wikiGestao=Wiki: Management
mobile.reagendamentoRealizado=Reagendamento realizado com sucesso!
digiteHorario=O hor\u00E1rio deve ser no formato 'HH:mm - HH:mm'
presencaConfirmada=Presen\u00E7a confirmada!
aulaClonada=Sua aula foi clonada com sucesso!
fecharModulos=Clique para esconder os m\u00F3dulos
fullScreenF11=Para uma total experi\u00EAncia em Full Screen, aperte a tecla F11 do seu teclado.
sync.nadaASincronizar=N\u00E3o existe nada para sincronizar
modalidadeObrigatoria=A modalidade \u00E9 obrigat\u00F3ria!
professorObrigatorio=O professor \u00E9 obrigat\u00F3rio!
ambienteObrigatorio=O ambiente \u00E9 obrigat\u00F3rio!
dataInicioObrigatorio=A data de fim \u00E9 obrigat\u00F3ria!
capacidadeObrigatorio=A capacidade \u00E9 obrigat\u00F3ria
pontosObrigatorio=Os pontos de b\u00F4nus s\u00E3o obrigat\u00F3rios
bonificacaoObrigatorio=A bonifica\u00E7\u00E3o \u00E9 obrigat\u00F3ria
metaObrigatorio=A meta \u00E9 obrigat\u00F3ria!
aulaTemAlunoNaoExcluir=Esta aula j\u00E1 tem alunos confirmados e n\u00E3o pode ser exclu\u00EDda.
deletarAulas=Tem certeza que deseja excluir as aulas geradas ?
horariosObrigatorios=Pelo menos um hor\u00E1rio \u00E9 obrigat\u00F3rio! 
diaSemanaObrigatorios=Pelo menos um dia da semana \u00E9 obrigat\u00F3rio! 
naoPodeExcluirAulaComAlunos=Voc\u00EA n\u00E3o pode excluir uma aula que tenha alunos marcados.
naopodeaulaCheia=Esta aula est\u00E1 cheia!
alunoRemovido=Aluno removido da aula!
informeJustificativa=Informe a justificativa!
horarioTemAlunosNaoExcluir=Hor\u00E1rio tem aula com alunos associados e n\u00E3o pode ser removido.
horarioJaAdicionado=Hor\u00E1rio j\u00E1 foi adicionado
naoTemPermissaoParaAdicionarAlunoPassado=Voc\u00EA n\u00E3o tem permiss\u00E3o para adicionar aluno em aula iniciada/realizada.
observacaoSalva=Observa\u00E7\u00E3o salva com sucesso!
ficha.adicionada=Ficha adicionada ao programa de treino
selecioneUmaModalidade=Selecione pelo menos uma modalidade.
email.obrigatorio=Preencha o e-mail
email.teste.enviado=E-mail de teste enviado com sucesso!
nenhum.aluno.selecionado=Nenhum aluno selecionado!
selecione.professor.padrao=Selecione o professor padr\u00E3o!
importacao.finalizada=Importa\u00E7\u00E3o finalizada com sucesso!
informe.tipo.evento=Informe um tipo de evento
username.em.uso=Nome de usu\u00E1rio j\u00E1 est\u00E1 sendo usado.
valor.aplicado.serie=Valores da s\u00E9rie foram aplicados \u00E0s outras s\u00E9ries da atividade.
msg.professorAlterado=Professor alterado com sucesso!
msg.selecioneProfessor=Selecione o professor!
validacao.indicador=The 'Indicator' is mandatory!
validacao.peso=The 'Weight' must be greater than zero!
mensagem.nao.pode.editar.usuario=Voc\u00EA n\u00E3o tem acesso a academia a qual esse usu\u00E1rio pertence.
nao.possivel.obter.alunos.app=N\u00E3o foi poss\u00EDvel obter os alunos com o aplicativo instalado.
aluno.sem.professor=The "Teacher Training" link from this student has been removed through ZW, please select a teacher!
avaliacao.fisica.validar.aluno.idade=To create an assessment it is necessary for the student to have an informed age.
avaliacao.fisica.validar.aluno.sexo=To create an assessment it is necessary that the student has the informed sex.
verifique.o.peso.informado=Informed weight is invalid!
colaborador.nao.pode.ser.excluido=This record can not be deleted because it has relationship (s). Inactivate the collaborator instead of deleting it.
validacao.email=The 'e-mail' is mandatory!
validacao.timezone=The 'Time Zone' is mandatory!
PERMITIR_ALUNO_MARCAR_AULA_POR_TIPO_MODALIDADE=Allow the student to mark a class in another unit
validacao.professorProgamaInativo=It is necessary to inform a teacher to the student, because the current teacher is inactive!
