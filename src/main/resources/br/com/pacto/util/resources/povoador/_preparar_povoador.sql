--Atividades
select 
a.tipo,
a.nome,
(SELECT array(SELECT ca.nome FROM categoriaatividade ca inner join atividadecategoriaatividade aca on aca.atividade_codigo = a.codigo where a.codigo = aca.atividade_codigo and aca.categoriaatividade_codigo = ca.codigo)) as categoriaAtividade,
(SELECT array(SELECT anim.url FROM animacao anim inner join atividadeanimacao aan on aan.atividade_codigo = anim.codigo where a.codigo = aan.atividade_codigo)) as url,
(SELECT array(SELECT ap.nome FROM aparelho ap inner join atividadeaparelho atap on atap.aparelho_codigo = ap.codigo where a.codigo = atap.atividade_codigo)) as aparelhos,
(SELECT array(SELECT grupo.nome FROM grupomuscular grupo inner join atividadegrupomuscular atgrupo on atgrupo.grupomuscular_codigo = grupo.codigo where a.codigo = atgrupo.atividade_codigo)) as grupos
from atividade a
order by a.nome;

--Niveis
select nome,ordem from nivel order by nome;

--Categoria Ficha
select nome from categoriaficha order by nome;

--Objetivos Predefinidos
select nome from objetivopredefinido  order by nome;

--Tipos de Evento
select comportamento,cor,nome,dias,nragendamentos,apenasalunoscarteira,duracao,duracaominutosmax,duracaominutosmin,intervalominimofalta from tipoevento order by nome;
select * from tipoevento  

--Fichas Predefinidas
select f.nome,f.mensagemaluno, 
(SELECT array(SELECT categ.nome FROM CategoriaFicha categ where categ.codigo = f.categoria_codigo)) as categ_nome,
(SELECT array(SELECT categ.nome FROM CategoriaFicha categ where categ.codigo = f.categoria_codigo)) as categ_nome
from ficha f 
where f.usarcomopredefinida 
order by f.nome;


/*auxiliar*
select * from ficha where usarcomopredefinida order by nome
select * from atividade  order by nome
select * from categoriaatividade  
select * from atividadecategoriaatividade  where atividade_codigo = 175
select * from atividadeanimacao  
select * from animacao 
select * from aparelho 
select * from grupomuscular  
*/