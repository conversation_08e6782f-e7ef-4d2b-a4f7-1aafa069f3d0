# To change this template, choose Too<PERSON> | Templates
# and open the template in the editor.
comum.dadosIncorretos=Dados incorretos!
comum.aguardeTente=Por favor Aguarde... e tente novamente!
comum.dadosGravados=Dados gravados com Sucesso!
mobile.excecao.aluno.reagendar=A academia n\u00E3o permite que o aluno fa\u00E7a reagendamento, por favor contate seu professor.
login.usuarioNaoPermitido=Usu\u00E1rio n\u00E3o permitido aqui!
login.usuarioInvalido=Usu\u00E1rio inv\u00E1lido!
login.senhaInvalida=Senha inv\u00E1lida!
login.sucesso=Logado com sucesso!
login.aguarde=Aguarde...
login.entrar=Entrar
recarregue.ranking=Voc\u00EA alterou as configura\u00E7\u00F5es de indicadores, para que as novas configura\u00E7\u00F5es sejam aplicadas, recarregue o ranking
login.facaLogin=Fa\u00E7a seu login
login.meLembrar=Lembrar-se de mim
login.esqueciSenha=Esqueceu sua senha?
mobile.token.invalido=Token Inv\u00E1lido!
validacao.nome=O 'nome' \u00E9 obrigat\u00F3rio!
mobile.programanaoencontrado=Nenhum treino. Contate seu Professor!
mobile.programatreinovencido=Treino vencido. Contate seu Professor!
mobile.programaalunoparcelavencido=N\u00E3o foi possivel buscar o programa pelo motivo: "Aluno n\u00E3o possui autoriza\u00E7\u00E3o de acesso."
mobile.programaFichaNaoEncontrado=Nenhuma ficha encontrada para este treino. Efetue login novamente!
mobile.programaemrevisao=Professor está realizando a validação do seu treino

ficha.salva=Ficha salva com sucesso!
peso.adicionado.hoje=O peso desse aluno foi atualizado hoje, exclua o registro anterior para evitar duplicidades.
programa.salvo=Programa de treino salvo com sucesso!
atividade.adicionada.ficha=Atividade adicionada \u00E0 ficha com sucesso!
serie.salva=S\u00E9rie salva com sucesso!
serie.alterada=S\u00E9rie alterada com sucesso
serie.removida=S\u00E9rie removida com sucesso
atividade.ficha.salva=Atividade salva com sucesso!
atividade.ficha.regra.nome=Nome da atividade [{0}] \u00E9 inv\u00E1lido, possui apenas n\u00FAmeros!
atividade.ficha.regra.nome.lista=Os nomes das atividades [{0}] s\u00E3o inv\u00E1lidos, possui apenas n\u00FAmeros!
atividade.removida=Atividade removida com sucesso!
ficha.predefinida.salva=Ficha predefinida salva!
validacao.ficha.nomeunico=Essa ficha n\u00E3o pode ser salva pois tem o mesmo nome de outra ficha deste programa!
mobile.serienaoencontrada=S\u00E9rie n\u00E3o encontrada!
cadastros.serie.adicionada=S\u00E9rie adicionada com sucesso!
cadastros.confirmar.exclusao=Confirma exclus\u00E3o?
cadastros.confirmar.renovacao.programa=Confirma a renova\u00E7\u00E3o do programa de treino?
validacao.nome.tipo=Os campos 'nome' e 'tipo' s\u00E3o obrigat\u00F3rios!
validacao.tipo=O 'tipo' \u00E9 obrigat\u00F3rio!
tabela.semregistros=Nenhum registro
validacao.fichaPredefinida.nomeunico=Essa ficha n\u00E3o pode ser salva pois tem o mesmo nome de outra ficha predefinida!
obrigatorio.nome=O 'nome' \u00E9 obrigat\u00F3rio!
obrigatorio.dataInicio='Data de in\u00EDcio' \u00E9 obrigat\u00F3ria!
obrigatorio.diasPorSemana='Dias por semana' \u00E9 obrigat\u00F3rio!
obrigatorio.diasSemanaMaior7='Dias por semana' n\u00E3o pode ser maior do que sete!
obrigatorio.dataProximaRevisao='Data da pr\u00F3xima revis\u00E3o' \u00E9 obrigat\u00F3ria!
obrigatorio.dataTermino='Data de t\u00E9rmino' \u00E9 obrigat\u00F3ria!
concomitante.programa=Este aluno j\u00E1 possui um programa no per\u00EDodo informado!
validacao.numeroFichas=O n\u00FAmero de fichas n\u00E3o pode ser maior do que o n\u00FAmero de dias por semana!
validacao.fichaDiaSemana=J\u00E1 existe uma ficha para este dia da semana!
validacao.ficha.programa=Esta ficha precisa estar relacionada a algum Programa de Treino que j\u00E1 exista
validacao.ficha.naoencontrada=Ficha n\u00E3o encontrada!
nivel.alterado=N\u00EDvel do aluno alterado com sucesso!
serieGravada=S\u00E9rie realizada gravada com sucesso!
mobile.usuarioinvalido=Usu\u00E1rio inv\u00E1lido!
alunoCadastrado=Aluno cadastrado no Treino com sucesso!
validacao.professor=\u00C9 necess\u00E1rio informar um professor!
validacao.professorProgamaInativo=\u00C9 necess\u00E1rio informar um professor, pois professor atual est\u00E1 inativo!
valorBadge=Valor do badge
obrigatorio.metodo=O 'm\u00E9todo de treino' \u00E9 obrigat\u00F3rio!
eventoalterado=Agenda alterada com sucesso!
eventoalteradonovoseventos=Agenda alterada, novos eventos foram criados com sucesso!
repetirAteObg=Se voc\u00EA deseja repetir esta disponibilidade, informe a data limite.
validacao.agenda.concomitante=J\u00E1 existe um evento neste hor\u00E1rio.
validacao.agenda.disponibilidade=N\u00E3o existe disponibilidade do professor na data informada.
validacao.agenda.tipo=O 'tipo do agendamento' \u00E9 obrigat\u00F3rio!
validacao.agenda.inicio=Informe o in\u00EDcio do agendamento!
validacao.agenda.fim=Informe o fim do agendamento!
validacao.agenda.professor='Professor' \u00E9 obrigat\u00F3rio!
validacao.agenda.cliente=Informe o cliente do agendamento!
mobile.semtreinos=Nenhum treino realizado ainda!
validacao.tipoexecucao=O 'tipo de execu\u00E7\u00E3o' da ficha \u00E9 obrigat\u00F3rio!
consultornaodisponibilidade=Consultor n\u00E3o tem permiss\u00E3o para marcar disponibilidades!
mobile.nenhumagendamento=Nenhum agendamento!
validacao.perfil.existeUsuario=Este perfil n\u00E3o pode ser excluido pois j\u00E1 existe um usu\u00E1rio associado.
msg.comportamento=O 'comportamento' \u00E9 obrigat\u00F3rio!
msg.cor=A 'cor' \u00E9 obrigat\u00F3ria!
naoExisteDisponibilidade=N\u00E3o existe disponibilidade cadastrada para o hor\u00E1rio selecionado!
naoTemPermissaoIncluirTipos=Voc\u00EA n\u00E3o tem permiss\u00E3o para adicionar um agendamento com os tipos dispon\u00EDveis.
naoTemPermissaoIncluirDisponibilidade=Voc\u00EA n\u00E3o tem permiss\u00E3o para adicionar uma disponibilidade.
naoTemPermissaoEditarDisponibilidade=Voc\u00EA n\u00E3o tem permiss\u00E3o para editar uma disponibilidade.
naoTemPermissaoEditarEvento=Voc\u00EA n\u00E3o tem permiss\u00E3o para editar o tipo de evento selecionado!
editarDisponibilidadeSemAgrupamento=Para editar uma disponibilidade retire o agrupamento.
obrigatorio.professorMontou=O 'professor' que montou o programa de treino \u00E9 obrigat\u00F3rio!
mobile.solicitacaoReagenda=Solicita\u00E7\u00E3o realizada com sucesso!
ajusteVazio=O 'ajuste' n\u00E3o pode ser vazio!
serieAlterada=S\u00E9rie alterada com sucesso!
validacaoalunoagendamento=O aluno j\u00E1 atingiu o limite de %d agendamentos para este tipo de evento no per\u00EDodo de %d dias. Agendamentos realizados: %d.
validacaoalunoagendamentofaltaemintervalodias=O aluno possui %d falta(s) neste tipo de evento nos \u00FAltimos %d dias. N\u00E3o \u00E9 poss\u00EDvel agendar novamente at\u00E9 que o per\u00EDodo de restri\u00E7\u00E3o termine.
validacao.perfilusuario=Perfil do usu\u00E1rio \u00E9 obrigat\u00F3rio!
disponibilidadeRemovida=Disponibilidade removida com sucesso!
validacao.agendamento.horaFimMenorHoraInicio=Hora final do agendamento n\u00E3o pode ser antes da hora inicial.
entidadePossuiRelacionamento=Este registro n\u00E3o pode ser exclu\u00EDdo pois possui relacionamento com
lembrete.agendamento=Lembrar: %s em %s
lembrete.agendamento.novo=Novo compromisso: %s em %s
lembrete.agendamento.alterado=Compromisso Alterado: %s para %s
agendamento.confirmado=Aluno confirmou agendamento %s em %s
mobile.agendamentoConfirmado=Agendamento confirmado!
mobile.agendamentoCancelado=Agendamento cancelado!
agendamento.cancelado=Aluno cancelou agendamento %s em %s
programa.agendarRenovacao=Agende a renova\u00E7\u00E3o do seu Treino com o Professor
filaImpressao.nadaEnfileirado=Nada foi enfileirado
filaImpressao.fichaEnfileirada=Ficha enviada para impressora!
cliente.naoencontrado=Cliente n\u00E3o encontrado. Houve algum problema no carregamento do aluno pelo ZillyonWeb
cliente.naoencontrado.treino=Cliente n\u00E3o encontrado. Verifique cadastro.
permissaoIncluirPrograma=Voc\u00EA n\u00E3o tem permiss\u00E3o para incluir um programa de treino.
permissaoEditarPrograma=Voc\u00EA n\u00E3o tem permiss\u00E3o para editar um programa de treino.
permissaoRenovarTreinoPrograma=Voc\u00EA n\u00E3o tem permiss\u00E3o para renovar o programa de treino.
permissaoIncluirFichaPredifinida=Voc\u00EA n\u00E3o tem permiss\u00E3o para incluir uma ficha predefinida.
permissaoEditarFichaPredifinida=Voc\u00EA n\u00E3o tem permiss\u00E3o para editar uma ficha predefinida.
habilitar=Habilitar
desabilitar=Desabilitar
modoFullScreen=Modo Full Screen
addAluno=Adicionar Aluno ao Treino (Ctrl+Shift+A)
novoTreino=Ir para a nova plataforma
wiki=Treino no Wiki Pacto
wikiPacto=WikiPacto
configuracoes=Configura\u00E7\u00F5es
agendarAluno=Agendar Aluno
acompanharAluno=Acompanhar Aluno
historicoExecAluno=Execu\u00E7\u00F5es
entrarModoAtendimento=Entrar Modo Atendimento
suporte=Suporte
alternarModulos=Clique para Navegar a outro M\u00F3dulo
permissao=Voc\u00EA n\u00E3o tem permiss\u00E3o para essa a\u00E7\u00E3o
permissaoIdentificada=Voc\u00EA n\u00E3o tem permiss\u00E3o de %s para entidade %s
programa.lembrarCompromisso=Treinar %s X/semana. Seu \u00FAltimo Treino: %s
programa.comecarCompromisso=Lembre-se: voc\u00EA fez um compromisso de malhar %s vezes por semana
mensagem.validacao.duracaoPreDefinida=Este tipo de evento tem a dura\u00E7\u00E3o predefinida (%s minutos) e n\u00E3o aceita agendamentos com dura\u00E7\u00E3o diferente.
mensagem.validacao.duracaoIntervalo=Este tipo de evento tem um intervalo de dura\u00E7\u00E3o e n\u00E3o aceita agendamentos com dura\u00E7\u00E3o fora deste intervalo.
programa.renovado=Programa renovado com sucesso!
programa.revisado=O programa foi marcado como revisado!
obrigatorio.professorCarteira=O 'professor' da carteira do aluno \u00E9 obrigat\u00F3rio!
obrigatorio.justificativa.revisao=\u00C9 obrigat\u00F3rio especificar a justificativa dessa Revis\u00E3o
obrigatorio.alterarProximaRevisao=\u00C9 obrigat\u00F3rio informar a data da pr\u00F3xima revis\u00E3o ou a data \u00E9 inv\u00E1lida!
obrigatorio.jaExisteOutraRevisaoMarcada=J\u00E1 existe uma outra revis\u00E3o marcada para %s
obrigatorio.proximaRevisaoJaExiste=J\u00E1 existe uma previs\u00E3o de Revis\u00E3o nesta data
obrigatorio.alunoSemProfessorCarteira=\u00C9 preciso adicionar este aluno \u00E0 carteira de algum professor
entidadeNomeDuplicado=J\u00E1 existe um registro com este Nome
login.empresaInvalida=Academia n\u00E3o encontrada. Contacte sua Academia.
login.usuario_inativo=Usu\u00E1rio inativo
obrigatorio.alunoSemSituacaoCompativel=Situa\u00E7\u00E3o do aluno n\u00E3o permite essa opera\u00E7\u00E3o
cadastros.atividade.existente=J\u00E1 existe uma atividade com este nome
cadastros.atividade.naoexiste=Atividade n\u00E3o encontrada. Verifique o cadastro.
cadastros.existente=J\u00E1 existe um registro semelhante a este
aluno_nao_ativo_cfg_somente_aluno_ativo=Aluno n\u00E3o est\u00E1 ativo!
atividadeEstaEmFicha=N\u00E3o \u00E9 poss\u00EDvel excluir esta atividade pois ela j\u00E1 est\u00E1 em pelo menos uma ficha.
cancelarAgendamento=Este agendamento n\u00E3o pode ser exclu\u00EDdo pois j\u00E1 gerou notifica\u00E7\u00F5es. Se deseja retir\u00E1-lo da sua agenda, mude o status para cancelado.
checkinrealizado=Check-In realizado com sucesso!
checkinalterado=Aula alterada com sucesso!
obrigatorio.alunoObrigatorioCheckIn=\u00C9 obrigat\u00F3rio informar o aluno para fazer o check-in.
checkoutrealizado=Check-out realizado com sucesso!
PERSONAL_SEM_CREDITO=O personal usa cr\u00E9ditos pr\u00E9-pagos e o saldo est\u00E1 zerado.
PARCELA_EM_ABERTO=O personal possui parcela em aberto a mais dias do que o permitido.
conexaoZillyonWebDuracaoCredito=N\u00E3o foi poss\u00EDvel conectar com o ZillyonWeb para obter a dura\u00E7\u00E3o dos cr\u00E9ditos.
alunojaadicionadoaula=Voc\u00EA j\u00E1 selecionou este aluno!
conexaoZillyonWebCfgEmpresa=N\u00E3o foi poss\u00EDvel obter as configura\u00E7\u00F5es da empresa no ZillyonWeb!
semPermissaoForcarCheckIn=Usu\u00E1rio n\u00E3o possui permiss\u00E3o para for\u00E7ar um check-in!
semPermissaEditarAula=Voc\u00EA n\u00E3o tem permiss\u00E3o para editar uma aula!
checkOutObrigatorio=O check-out deve ser informado!
checkinmaiorcheckout=Check-in n\u00E3o pode ser maior do que o check-out!
diferencaDiasCheckinCheckOut=Check-Out n\u00E3o pode ter uma diferen\u00E7a de mais de um dia pro Check-in!
checkinobrigatorio=Data do check-in \u00E9 obrigat\u00F3ria!
msgPermissaoCheckOut=Voc\u00EA n\u00E3o tem permiss\u00E3o para realizar chekc-out.
validacao.horariosAgendamentos=Informe todos os hor\u00E1rios do agendamento!
cadastro.programa.naopodeserexcluido=Este programa de treino n\u00E3o pode ser exclu\u00EDdo pois possui notifica\u00E7\u00F5es associadas.
wikiGestao=Wiki: Gest\u00E3o
mobile.reagendamentoRealizado=Reagendamento realizado com sucesso!
digiteHorario=O hor\u00E1rio deve ser no formato 'HH:mm - HH:mm'
presencaConfirmada=Presen\u00E7a confirmada!
aulaClonada=Sua aula foi clonada com sucesso!
nivel.alterado=N\u00EDvel do aluno alterado com sucesso!
modalidadeObrigatoria=A modalidade \u00E9 obrigat\u00F3ria!
professorObrigatorio=O professor \u00E9 obrigat\u00F3rio!
ambienteObrigatorio=O ambiente \u00E9 obrigat\u00F3rio!
dataInicioObrigatorio=A data de in\u00EDcio \u00E9 obrigat\u00F3ria!
dataInicioObrigatorio=A data de fim \u00E9 obrigat\u00F3ria!
capacidadeObrigatorio=A capacidade \u00E9 obrigat\u00F3ria!
pontosObrigatorio=Os pontos de b\u00F4nus s\u00E3o obrigat\u00F3rios!
bonificacaoObrigatorio=A bonifica\u00E7\u00E3o \u00E9 obrigat\u00F3ria!
metaObrigatorio=A meta \u00E9 obrigat\u00F3ria!
aulaTemAlunoNaoExcluir=Esta aula j\u00E1 tem alunos confirmados e n\u00E3o pode ser exclu\u00EDda.
deletarAulas=Tem certeza que deseja excluir as aulas geradas ?
horariosObrigatorios=Pelo menos um hor\u00E1rio \u00E9 obrigat\u00F3rio!
diaSemanaObrigatorios=Pelo menos um dia da semana \u00E9 obrigat\u00F3rio!
naoPodeExcluirAulaComAlunos=Voc\u00EA n\u00E3o pode excluir uma aula que tenha alunos marcados.
naopodeaulaCheia=Esta aula est\u00E1 cheia!
alunoRemovido=Aluno removido da aula!
informeJustificativa=Informe a justificativa!
horarioTemAlunosNaoExcluir=Hor\u00E1rio tem aula com alunos associados e n\u00E3o pode ser removido.
horarioJaAdicionado=Hor\u00E1rio j\u00E1 foi adicionado!
naoTemPermissaoParaAdicionarAlunoPassado=Voc\u00EA n\u00E3o tem permiss\u00E3o para adicionar aluno em aula iniciada/realizada.
fecharModulos=Clique para esconder os m\u00F3dulos
fullScreenF11=Para uma total experi\u00EAncia em Full Screen, aperte a tecla F11 do seu teclado.
sync.nadaASincronizar=N\u00E3o existe nada para sincronizar
sync.empresaNaoInformada=Empresa n\u00E3o informada.
sync.semSincronizacaoParaEmpresaInformada=N\u00E3o existe sincroniza\u00E7\u00E3o para a empresa informada.
observacaoSalva=Observa\u00E7\u00E3o salva com sucesso!
ficha.adicionada=Ficha adicionada ao programa de treino
selecioneUmaModalidade=Selecione pelo menos uma modalidade.
email.obrigatorio=Preencha o e-mail
nome.obrigatorio=Preencha o nome
telefone.obrigatorio =Preencha o telefone
perfil.usuario =Selecione um perfil de usu\u00E1rio
nome.usuario =Preencha um usu\u00E1rio
senha.usuario =Preencha uma senha
tipo.usuario =Selecione um tipo de usu\u00E1rio
email.teste.enviado=E-mail de teste enviado com sucesso!
nenhum.aluno.selecionado=Nenhum aluno selecionado!
selecione.professor.padrao=Selecione o professor padr\u00E3o!
importacao.finalizada=Importa\u00E7\u00E3o finalizada com sucesso!
informe.tipo.evento=Informe um tipo de evento
datainicio.maior.datafim=A data de in\u00EDcio do programa n\u00E3o pode ser maior do que a data prevista de t\u00E9rmino!
username.em.uso=Nome de usu\u00E1rio j\u00E1 est\u00E1 sendo usado.
valor.aplicado.serie=Valores da s\u00E9rie foram aplicados \u00E0s outras s\u00E9ries da atividade.
username.obrigatorio=Preencha o nome de usu\u00E1rio
email.usuario.valido=Informe um e-mail v\u00E1lido.
email.invalido=E-mail inv\u00E1lido.
aluno.criado.email.nao.enviado=O aluno foi criado, por\u00E9m ouve um problema ao enviar o e-mail com as informa\u00E7\u00F5es, reenvie o email pela tela do aluno.
agenda.visualizar.agenda.professores=Voc\u00EA n\u00E3o tem permiss\u00E3o para visualizar agenda de outros professores.
msg.professorAlterado=Professor alterado com sucesso!
msg.selecioneProfessor=Selecione o professor!
alunodesmarcadocomsucesso=Aluno desmarcado com sucesso!
repor=Reposi\u00E7\u00E3o marcada com sucesso!
marcar=Aula marcada com sucesso!
alunocommesmonome=J\u00E1 existe um aluno com esse nome. Matr\u00EDcula:
todasexecucoes=Voc\u00EA j\u00E1 executou todas as aulas previstas para esse programa, por favor contate seu professor.
nomesalvargrupografico=Voc\u00EA tem que informar um nome para o gr\u00E1fico pr\u00E9-definido!
informenomematricula=Informe um nome ou uma matr\u00EDcula para buscar o aluno
horarioinicialagendamenor=Hor\u00E1rio inicial da agenda deve ser menor do que 23hr
validacao.indicador=O 'Indicador' \u00E9 obrigat\u00F3rio!
validacao.peso=O 'Peso' deve ser maior que zero!
mensagem.treinoRapido.carga = Valor informado no campo (Carga) deve ser n\u00FAmerico. Separadores aceitos (,)(/)(-).
mensagem.treinoRapido.repeticao = Valor informado no campo (repeti\u00E7\u00E3o) deve ser n\u00FAmerico. Separadores aceitos (,)(/)(-).
mensagem.nao.pode.editar.usuario=Voc\u00EA n\u00E3o tem acesso a academia a qual esse usu\u00E1rio pertence.
validacao.aluno.outra.carteira=Voc\u00EA n\u00E3o possui permiss\u00E3o para visualizar alunos de outras carteiras.
nao.possivel.obter.alunos.app=N\u00E3o foi poss\u00EDvel obter os alunos com o aplicativo instalado.
aluno.sem.professor=O v\u00EDnculo de "Professor TreinoWeb" deste aluno foi removido atrav\u00E9s do ZW, por favor selecione um professor!
validacao.tipobenchmark='Tipo de Benchmark' \u00E9 obrigat\u00F3rio.
validacao.exercicios='Exerc\u00EDcios' \u00E9 obrigat\u00F3rio.
validacao.tipowod='Tipo Wod' \u00E9 obrigat\u00F3rio.
validacao.diawod='Dia' \u00E9 obrigat\u00F3rio.
validacao.informeDiaImportar=Informe o dia para importar corretamente.
avaliacao.fisica.validar.aluno.idade=Para criar uma avalia\u00E7\u00E3o \u00E9 necess\u00E1rio que o aluno tenha a idade informada.
avaliacao.fisica.validar.aluno.sexo=Para criar uma avalia\u00E7\u00E3o \u00E9 necess\u00E1rio que o aluno tenha o sexo informado.
word.exclusao.registro.ranking=N\u00E3o \u00E9 poss\u00EDvel excluir (Wod) quando o mesmo possui registro de ranking.
anamnese.nao.pode.excluir=Esta anamnese n\u00E3o pode ser exclu\u00EDda pois j\u00E1 foi respondida. Voc\u00EA pode desativ\u00E1-la.
perguntas.nao.pode.editar=As perguntas que j\u00E1 foram respondidas por algum aluno n\u00E3o podem ser editadas ou alteradas.
aluno.sem.avaliacao=Aluno sem avalia\u00E7\u00E3o f\u00EDsica.
apenas.uma.avaliacao=Uma avalia\u00E7\u00E3o f\u00EDsica at\u00E9 o momento
avaliacoes.dias=avalia\u00E7\u00F5es em um per\u00EDodo de %s dias
avaliacoes.meses=avalia\u00E7\u00F5es em um per\u00EDodo de %s meses
queda.percentual.gordura=Queda no percentual de gordura
aumento.percentual.gordura=Aumento no percentual de gordura
queda.percentual.magra=Queda no percentual de massa magra
aumento.percentual.magra=Aumento no percentual de massa magra
massa.gordura.atual=massa gorda atual 
massa.gordura.inicial=massa gorda inicial 
massa.magra.atual=massa magra atual 
massa.magra.inicial=massa magra inicial 
media.treinos.semana=M\u00E9dia de %s vezes por semana
treinos.executados.periodo=Treinos executados no per\u00EDodo
presenca.marcada.sucesso=Presen\u00E7a confirmada com sucesso!
presenca.desmarcada.sucesso=Presen\u00E7a desmarcada com sucesso!
avaliacao.agendada=Avalia\u00E7\u00E3o f\u00EDsica agendada com sucesso.
aluno.nao.produto.vigente.avaliacao=O aluno n\u00E3o tem um produto de Avalia\u00E7\u00E3o F\u00EDsica vigente.
produto.vigente.usado=O produto vigente j\u00E1 foi usado.
aviso.protocolo.adolescente.nao.indicado=Este protocolo n\u00E3o \u00E9 recomendado para maiores de 18 anos.
aviso.protocolo.adolescente.indicado=Para menores de 18 anos, \u00E9 altamente recomend\u00E1vel usar o protocolo Pollock - Adolescente
tipowod.nao.pode.excluir=N\u00E3o \u00E9 possivel excluir (Tipo Wod).
mensagem.qrcode.tv.gestor=O c\u00F3digo abaixo permite que voc\u00EA acompanhe em tempo real o TV GESTOR atrav\u00E9s do seu celular. Basta voc\u00EA escane\u00E1-lo e abrir o link com o navegador do seu celular.
validacao.periodizacao='Periodiza\u00E7\u00E3o' \u00E9 obrigatorio.
semPermissaoExcluirAnexo=Voc\u00EA n\u00E3o tem permiss\u00E3o para excluir anexo.
avaliacao.enviada.sucesso=Avalia\u00E7\u00E3o enviada com sucesso!
aluno.possui.compromisso=O aluno j\u00E1 possui um compromissos nesse hor\u00E1rio:
horario.turma.invalido=Horario turma n\u00E3o encontrado!
voce.possui.compromisso=Voc\u00EA j\u00E1 possui um compromisso nesse hor\u00E1rio:
data.proxima.vazia=O dia da pr\u00F3xima avalia\u00E7\u00E3o n\u00E3o foi informado, insira a data desejada para realizar o agendamento.
informe.peso.astrand=Para o c\u00E1lculo \u00E9 necess\u00E1rio que voc\u00EA informe o peso do avaliado.
verifique.o.peso.informado=Peso informado e inv\u00E1lido!
HABILITAR_CROSSFIT=Habilitar m\u00F3dulo Crossfit
MODULO_PRINCIPAL_APLICATIVO=M\u00F3dulo Principal App do Aluno
colaborador.nao.pode.ser.excluido=Este registro n\u00E3o pode ser exclu\u00EDdo pois possui relacionamento(s). Inative o colaborador ao inv\u00E9s de exclu\u00ED-lo.
validacao.email=O 'e-mail' \u00E9 obrigat\u00F3rio!
validacao.timezone=O 'Fuso hor\u00E1rio' \u00E9 obrigat\u00F3rio!
PERMITIR_ALUNO_MARCAR_AULA_POR_TIPO_MODALIDADE=Permitir que o aluno marque aula pelo tipo de modalidade
data.avaliacao.vazia=\u00E9 necess\u00E1rio salvar a avalia\u00E7\u00E3o antes de agendar a pr\u00F3xima.
carte
validacaoalunoagendamento.carteira=O aluno n\u00E3o faz parte da carteira do professor.
