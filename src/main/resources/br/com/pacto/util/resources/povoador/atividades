{"return": [{"cod": 1, "codigoAtividade": null, "nome": "ABDOMINAL CRUNCH", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/25.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/25.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/25.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 2, "codigoAtividade": null, "nome": "ABDOMINAL (solo)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/78.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/78.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/78.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 3, "codigoAtividade": null, "nome": "ABDUÇÃO DE QUADRIL", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/6.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/6.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/6.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 4, "codigoAtividade": null, "nome": "ABDUÇÃO DE QUADRIL (mult hip)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/20.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/20.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/20.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 5, "codigoAtividade": null, "nome": "ABDUÇÃO DE QUADRIL (solo)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/35.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/35.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/35.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 6, "codigoAtividade": null, "nome": "ABDUCTOR", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/28.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/28.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/28.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 7, "codigoAtividade": null, "nome": "ABS CRUZADO", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/178.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/178.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/178.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 8, "codigoAtividade": null, "nome": "ABS INFRA NO BANCO", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/227.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/227.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/227.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 9, "codigoAtividade": null, "nome": "ABS LATERAL NO BANCO ROMANO", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/228.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/228.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/228.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 10, "codigoAtividade": null, "nome": "ABS NA MÁQ. COM CABOS", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/245.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/245.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/245.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 11, "codigoAtividade": null, "nome": "ABS NO BANCO DECLINADO", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/225.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/225.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/225.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 12, "codigoAtividade": null, "nome": "ABS VERTICAL", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/188.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/188.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/188.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 13, "codigoAtividade": null, "nome": "ADDUCTOR", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/158.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/158.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/158.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 14, "codigoAtividade": null, "nome": "ADUÇÃO DE COXA (polia)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/10.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/10.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/10.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 15, "codigoAtividade": null, "nome": "ADUÇÃO DE QUADRIL (mult hip)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/21.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/21.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/21.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 16, "codigoAtividade": null, "nome": "AFUNDO (barra)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/165.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/165.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/165.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 17, "codigoAtividade": null, "nome": "AFUNDO (halter)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/42.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/42.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/42.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 18, "codigoAtividade": null, "nome": "AGACHAMENTO (barra)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/166.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/166.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/166.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 19, "codigoAtividade": null, "nome": "AGACHAMENTO (halter)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/40.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/40.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/40.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 20, "codigoAtividade": null, "nome": "AGACHAMENTO (halter pés abduzidos)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/39.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/39.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/39.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 21, "codigoAtividade": null, "nome": "ALONGAMENTNO (dorsal)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/67.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/67.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/67.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 22, "codigoAtividade": null, "nome": "ALONGAMENTO (anterior de coxa)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/66.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/66.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/66.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 23, "codigoAtividade": null, "nome": "ALONGAMENTO (bíceps)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/75.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/75.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/75.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 24, "codigoAtividade": null, "nome": "ALONGAMENTO (cervical)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/52.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/52.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/52.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 25, "codigoAtividade": null, "nome": "ALONGAMENTO (COLUNA) ", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/71.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/71.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/71.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 26, "codigoAtividade": null, "nome": "ALONGAMENTO (deltoide)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/51.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/51.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/51.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 27, "codigoAtividade": null, "nome": "ALONGAMENTO (panturrilha)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/62.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/62.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/62.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 28, "codigoAtividade": null, "nome": "ALONGAMENTO (peitoral)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/72.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/72.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/72.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 29, "codigoAtividade": null, "nome": "ALONGAMENTO (posterior de coxa)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/48.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/48.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/48.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 30, "codigoAtividade": null, "nome": "ALONGAMENTO (trapézio)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/50.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/50.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/50.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 31, "codigoAtividade": null, "nome": "ALONGAMENTO (tríceps)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/54.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/54.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/54.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 32, "codigoAtividade": null, "nome": "ALONGAMENTO UNIL (posterior de coxa)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/63.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/63.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/63.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 33, "codigoAtividade": null, "nome": "APOIO NO SOLO", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/129.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/129.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/129.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 34, "codigoAtividade": null, "nome": "ARM CURL", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/29.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/29.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/29.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 35, "codigoAtividade": null, "nome": "ARM EXTENSION", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/27.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/27.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/27.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 36, "codigoAtividade": null, "nome": "BARRA FIXA (pegada neutra)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/157.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/157.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/157.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 37, "codigoAtividade": null, "nome": "BARRA FIXA (pegada pronada)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/155.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/155.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/155.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 38, "codigoAtividade": null, "nome": "BARRA FIXA (pegada supinada)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/156.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/156.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/156.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 39, "codigoAtividade": null, "nome": "BICEPS BANCO 45º", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/106.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/106.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/106.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 40, "codigoAtividade": null, "nome": "BICEPS BANCO 45º (martelo)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/107.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/107.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/107.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 41, "codigoAtividade": null, "nome": "BICEPS BANCO 90° (halter)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/98.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/98.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/98.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 42, "codigoAtividade": null, "nome": "BICEPS BANCO 90° (halter martelo)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/99.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/99.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/99.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 43, "codigoAtividade": null, "nome": "BICEPS BARRA (reta)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/87.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/87.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/87.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 44, "codigoAtividade": null, "nome": "BÍCEPS CONCENTRADO", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/161.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/161.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/161.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 45, "codigoAtividade": null, "nome": "BÍCEPS DIRETO BANCO DECLINADO", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/105.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/105.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/105.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 46, "codigoAtividade": null, "nome": "BÍCEPS DIRETO NA BARRA W", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/203.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/203.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/203.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 47, "codigoAtividade": null, "nome": "BÍCEPS DIRETO NA POLIA", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/145.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/145.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/145.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 48, "codigoAtividade": null, "nome": "BICEPS HALTER (alternado)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/167.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/167.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/167.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 49, "codigoAtividade": null, "nome": "BICEPS HALTER (martelo)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/86.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/86.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/86.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 50, "codigoAtividade": null, "nome": "BICEPS HALTER (simultâneo)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/85.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/85.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/85.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 51, "codigoAtividade": null, "nome": "BÍCEPS NA POLIA AGACHADO", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/143.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/143.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/143.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 52, "codigoAtividade": null, "nome": "BICEPS POLIA", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/144.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/144.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/144.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 53, "codigoAtividade": null, "nome": "BICEPS POLIA ALTA", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/147.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/147.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/147.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 54, "codigoAtividade": null, "nome": "BICEPS POLIA BAIXA (barra reta)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/33.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/33.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/33.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 55, "codigoAtividade": null, "nome": "BICEPS POLIA BAIXA (sentado)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/34.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/34.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/34.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 56, "codigoAtividade": null, "nome": "BICEPS POLIA (unilateral)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/146.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/146.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/146.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 57, "codigoAtividade": null, "nome": "BICEPS ROSCA INVERTIDA", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/88.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/88.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/88.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 58, "codigoAtividade": null, "nome": "BICEPS SCOTT (barra reta)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/83.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/83.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/83.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 59, "codigoAtividade": null, "nome": "BICEPS SCOTT (barra w)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/82.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/82.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/82.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 60, "codigoAtividade": null, "nome": "BICEPS SCOTT (halter)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/84.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/84.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/84.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 61, "codigoAtividade": null, "nome": "BÍCEPS SCOTT (PEG. ABERTA)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/133.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/133.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/133.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 62, "codigoAtividade": null, "nome": "BÍCEPS SCOTT (PEG. FECHADA)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/131.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/131.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/131.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 63, "codigoAtividade": null, "nome": "BÍCEPS SCOTT (PEG. NEUTRA)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/134.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/134.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/134.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 64, "codigoAtividade": null, "nome": "BÍCEPS UNILATERAL NA POLIA", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/199.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/199.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/199.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 65, "codigoAtividade": null, "nome": "BIKE DE SPINING", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/210.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/210.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/210.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 66, "codigoAtividade": null, "nome": "BIKE HORIZONTAL", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/211.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/211.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/211.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 67, "codigoAtividade": null, "nome": "BIKE VERTICAL", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/212.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/212.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/212.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 68, "codigoAtividade": null, "nome": "CALF PS", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/49.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/49.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/49.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 69, "codigoAtividade": null, "nome": "CHEST INCLINE", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/1.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/1.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/1.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 70, "codigoAtividade": null, "nome": "CHEST PRESS", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/0.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/0.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/0.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 71, "codigoAtividade": null, "nome": "CORE TRAINER", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/244.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/244.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/244.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 72, "codigoAtividade": null, "nome": "CRUCIFIXO (cross over)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/148.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/148.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/148.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 73, "codigoAtividade": null, "nome": "CRUCIFIXO DECLINADO", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/216.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/216.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/216.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 74, "codigoAtividade": null, "nome": "CRUCIFIXO INCLINADO (halter)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/168.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/168.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/168.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 75, "codigoAtividade": null, "nome": "CRUCIFIXO INVERSO BANCO(halter)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/96.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/96.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/96.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 76, "codigoAtividade": null, "nome": "CRUCIFIXO INVERSO (cross over)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/153.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/153.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/153.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 77, "codigoAtividade": null, "nome": "CRUCIFIXO INVERSO (halter)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/64.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/64.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/64.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 78, "codigoAtividade": null, "nome": "CRUCIFIXO INVERSO NA POLIA", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/201.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/201.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/201.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 79, "codigoAtividade": null, "nome": "CRUCIFIXO NA MÁQUINA", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/233.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/233.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/233.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 80, "codigoAtividade": null, "nome": "CRUCIFIXO NO CABO", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/241.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/241.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/241.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 81, "codigoAtividade": null, "nome": "CRUCIFIXO POLIA ALTA (CROSS OVER)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/149.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/149.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/149.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 82, "codigoAtividade": null, "nome": "CRUCIFIXO POLIA BAIXA (cross over)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/150.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/150.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/150.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 83, "codigoAtividade": null, "nome": "CRUCIFIXO POLIA MÉDIA (CROSS OVER)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/200.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/200.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/200.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 84, "codigoAtividade": null, "nome": "CRUCIFIXO RETO (halter)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/109.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/109.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/109.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 85, "codigoAtividade": null, "nome": "DELTS MACHINE", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/13.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/13.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/13.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 86, "codigoAtividade": null, "nome": "DESENVOLVIMENTO ARNOLD (halter)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/60.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/60.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/60.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 87, "codigoAtividade": null, "nome": "DESENVOLVIMENTO BANCO 90° (halter)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/95.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/95.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/95.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 88, "codigoAtividade": null, "nome": "DESENVOLVIMENTO FRENTE  (barra)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/120.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/120.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/120.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 89, "codigoAtividade": null, "nome": "DESENVOLVIMENTO FRENTE  (halter)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/55.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/55.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/55.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 90, "codigoAtividade": null, "nome": "DESENVOLVIMENTO FRENTE (halter pegada neutra)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/91.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/91.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/91.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 91, "codigoAtividade": null, "nome": "DESENVOLVIMENTO NA MÁQUINA", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/236.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/236.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/236.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 92, "codigoAtividade": null, "nome": "ELEVAÇÃO FRONTAL", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/169.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/169.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/169.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 93, "codigoAtividade": null, "nome": "ELEVAÇÃO FRONTAL BANCO 90°(halter)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/97.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/97.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/97.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 94, "codigoAtividade": null, "nome": "ELEVAÇÃO FRONTAL (barra reta)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/90.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/90.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/90.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 95, "codigoAtividade": null, "nome": "ELEVAÇÃO FRONTAL (halter)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/56.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/56.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/56.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 96, "codigoAtividade": null, "nome": "ELEVAÇÃO FRONTAL (polia)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/152.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/152.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/152.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 97, "codigoAtividade": null, "nome": "ELEVAÇÃO LATERAL", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/89.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/89.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/89.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 98, "codigoAtividade": null, "nome": "ELEVAÇÃO LATERAL (halter)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/58.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/58.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/58.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 99, "codigoAtividade": null, "nome": "ELEVAÇÃO LATERAL (polia)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/151.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/151.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/151.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 100, "codigoAtividade": null, "nome": "ELEVAÇÃO PÉLVICA COM CANELEIRA", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/223.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/223.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/223.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 101, "codigoAtividade": null, "nome": "ELEVAÇÃO PÉLVICA COM HALTERES", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/224.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/224.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/224.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 102, "codigoAtividade": null, "nome": "ENCOLHIMENTO (barra)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/170.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/170.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/170.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 103, "codigoAtividade": null, "nome": "ENCOLHIMENTO DE OMBRO C/ HALTERES", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/92.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/92.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/92.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 104, "codigoAtividade": null, "nome": "ENCOLHIMENTO DE OMBRO  (HALTERES)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/125.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/125.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/125.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 105, "codigoAtividade": null, "nome": "ENCOLHIMENTO (halter)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/93.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/93.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/93.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 106, "codigoAtividade": null, "nome": "ENCOLHIMENTO (polia)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/185.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/185.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/185.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 107, "codigoAtividade": null, "nome": "ESCADA", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/163.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/163.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/163.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 108, "codigoAtividade": null, "nome": "ESTEIRA", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/209.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/209.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/209.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 109, "codigoAtividade": null, "nome": "EXTENSÃO DE QUADRIL (caneleira)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/115.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/115.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/115.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 110, "codigoAtividade": null, "nome": "EXTENSÃO DE QUADRIL  (CANELEIRA)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/118.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/118.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/118.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 111, "codigoAtividade": null, "nome": "EXTENSÃO DE QUADRIL (mult hip)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/19.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/19.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/19.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 112, "codigoAtividade": null, "nome": "FLEXABILITY ANTERIOR", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/160.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/160.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/160.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 113, "codigoAtividade": null, "nome": "FLEXABILITY POSTERIOR", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/159.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/159.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/159.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 114, "codigoAtividade": null, "nome": "FLEXÃO DE BRAÇO (aberto)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/114.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/114.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/114.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 115, "codigoAtividade": null, "nome": "FLEXÃO DE BRAÇO (fechado)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/119.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/119.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/119.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 116, "codigoAtividade": null, "nome": "FLEXÃO DE JOELHO C/ CANELEIRA", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/231.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/231.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/231.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 117, "codigoAtividade": null, "nome": "FLEXÃO DE PERNA (caneleira)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/38.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/38.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/38.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 118, "codigoAtividade": null, "nome": "FLEXÃO DE PUNHO (barra)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/172.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/172.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/172.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 119, "codigoAtividade": null, "nome": "FLEXÃO DE PUNHO (halter)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/171.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/171.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/171.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 120, "codigoAtividade": null, "nome": "FLEXÃO DE PUNHO INVERSA", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/206.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/206.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/206.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 121, "codigoAtividade": null, "nome": "FLEXÃO DE PUNHO NA POLIA", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/204.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/204.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/204.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 122, "codigoAtividade": null, "nome": "FLEXÃO DE PUNHO NA POLIA ", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/205.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/205.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/205.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 123, "codigoAtividade": null, "nome": "FLEXÃO DE QUADRIL (leg raise)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/187.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/187.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/187.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 124, "codigoAtividade": null, "nome": "FLEXÃO DE QUADRIL (mult hip)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/18.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/18.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/18.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 125, "codigoAtividade": null, "nome": "FLEXÃO DE QUADRIL (na bola)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/57.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/57.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/57.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 126, "codigoAtividade": null, "nome": "FLEXÃO DE QUADRIL NO GRAVITON", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/191.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/191.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/191.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 127, "codigoAtividade": null, "nome": "FLEXÃO DE QUADRIL (solo)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/79.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/79.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/79.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 128, "codigoAtividade": null, "nome": "FLEXÃO DE TRONCO", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/81.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/81.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/81.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 129, "codigoAtividade": null, "nome": "FLEXÃO DE TRONCO LATERAL", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/80.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/80.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/80.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 130, "codigoAtividade": null, "nome": "FLEXÃO PLANTAR", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/173.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/173.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/173.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 131, "codigoAtividade": null, "nome": "FLEXÃO PLANTAR LEG PRESS", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/186.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/186.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/186.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 132, "codigoAtividade": null, "nome": "FLEXORA SENTADO", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/219.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/219.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/219.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 133, "codigoAtividade": null, "nome": "GLUTE", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/22.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/22.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/22.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 134, "codigoAtividade": null, "nome": "HIGH PULL", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/242.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/242.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/242.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 135, "codigoAtividade": null, "nome": "HIPEREXTENSÃO DE TRONCO NO BANCO ROMANO", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/222.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/222.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/222.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 136, "codigoAtividade": null, "nome": "KICK BACK (halter)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/76.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/76.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/76.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 137, "codigoAtividade": null, "nome": "KICK BACK (polia)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/139.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/139.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/139.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 138, "codigoAtividade": null, "nome": "LAT MACHINE (puxada aberta)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/5.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/5.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/5.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 139, "codigoAtividade": null, "nome": "LAT MACHINE (puxada supinada)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/7.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/7.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/7.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 140, "codigoAtividade": null, "nome": "LAT MACHINE (triangulo)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/8.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/8.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/8.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 141, "codigoAtividade": null, "nome": "LEG EXTENSION", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/14.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/14.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/14.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 142, "codigoAtividade": null, "nome": "LEG PRESS", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/15.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/15.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/15.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 143, "codigoAtividade": null, "nome": "LEG PRESS ARTICULADO", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/41.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/41.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/41.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 144, "codigoAtividade": null, "nome": "LEG PRESS (pés abduzidos)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/17.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/17.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/17.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 145, "codigoAtividade": null, "nome": "LEG PRESS PS", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/36.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/36.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/36.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 146, "codigoAtividade": null, "nome": "LEG PRESS PS (pés abduzidos)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/43.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/43.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/43.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 147, "codigoAtividade": null, "nome": "LEG PRESS PS (unilateral)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/37.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/37.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/37.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 148, "codigoAtividade": null, "nome": "LEG PRESS (unilateral)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/16.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/16.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/16.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 149, "codigoAtividade": null, "nome": "LEVANTAMENTO TERRA (barra)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/46.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/46.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/46.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 150, "codigoAtividade": null, "nome": "LEVANTAMENTO TERRA (halter)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/174.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/174.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/174.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 151, "codigoAtividade": null, "nome": "LEVANTAMENTO TERRA (polia)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/180.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/180.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/180.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 152, "codigoAtividade": null, "nome": "LOWER BACK", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/26.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/26.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/26.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 153, "codigoAtividade": null, "nome": "LOW PULL", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/239.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/239.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/239.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 154, "codigoAtividade": null, "nome": "LOW ROW ", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/9.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/9.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/9.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 155, "codigoAtividade": null, "nome": "LOW ROW PS (fechado)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/117.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/117.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/117.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 156, "codigoAtividade": null, "nome": "MERGULHO", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/77.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/77.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/77.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 157, "codigoAtividade": null, "nome": "MESA FLEXORA LIFE FITNESS", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/23.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/23.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/23.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 158, "codigoAtividade": null, "nome": "OVERHEAD PRESS", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/237.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/237.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/237.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 159, "codigoAtividade": null, "nome": "PANTURILHA NA MÁQUINA", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/198.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/198.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/198.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 160, "codigoAtividade": null, "nome": "PANTURRILHA  NO HACK", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/194.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/194.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/194.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 161, "codigoAtividade": null, "nome": "PANTURRILHA SENTADA", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/196.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/196.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/196.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 162, "codigoAtividade": null, "nome": "PECTORAL MACHINE", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/2.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/2.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/2.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 163, "codigoAtividade": null, "nome": "PLATE PRESS", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/218.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/218.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/218.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 164, "codigoAtividade": null, "nome": "PONTE LATERAL", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/122.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/122.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/122.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 165, "codigoAtividade": null, "nome": "PONTE LATERAL + ROTAÇÃO", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/189.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/189.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/189.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 166, "codigoAtividade": null, "nome": "PONTE VENTRAL", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/130.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/130.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/130.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 167, "codigoAtividade": null, "nome": "PONTE VENTRAL (bola)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/61.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/61.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/61.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 168, "codigoAtividade": null, "nome": "PRESS STATION", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/235.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/235.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/235.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 169, "codigoAtividade": null, "nome": "PULL DOWN PS", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/116.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/116.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/116.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 170, "codigoAtividade": null, "nome": "PULL OVER (halter)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/162.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/162.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/162.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 171, "codigoAtividade": null, "nome": "PUXADA ALTA DE JOELHOS", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/197.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/197.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/197.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 172, "codigoAtividade": null, "nome": "PUXADA ALTA NO CABO", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/240.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/240.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/240.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 173, "codigoAtividade": null, "nome": "REAR KICK", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/53.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/53.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/53.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 174, "codigoAtividade": null, "nome": "REMADA ALTA (barra)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/94.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/94.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/94.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 175, "codigoAtividade": null, "nome": "REMADA ALTA COM BARRA", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/126.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/126.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/126.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 176, "codigoAtividade": null, "nome": "REMADA ALTA (halter)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/59.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/59.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/59.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 177, "codigoAtividade": null, "nome": "REMADA ALTA NA POLIA", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/154.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/154.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/154.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 178, "codigoAtividade": null, "nome": "REMADA ALTA (polia)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/3.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/3.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/3.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 179, "codigoAtividade": null, "nome": "REMADA CURVADA (barra)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/47.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/47.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/47.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 180, "codigoAtividade": null, "nome": "REMADA CURVADA (halter)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/175.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/175.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/175.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 181, "codigoAtividade": null, "nome": "REMADA CURVADA (polia)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/181.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/181.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/181.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 182, "codigoAtividade": null, "nome": "REMADA NO CABO SENTADO ", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/238.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/238.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/238.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 183, "codigoAtividade": null, "nome": "REMADA SENTADA", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/192.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/192.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/192.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 184, "codigoAtividade": null, "nome": "REMADA SERROTE", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/104.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/104.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/104.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 185, "codigoAtividade": null, "nome": "REMO", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/208.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/208.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/208.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 186, "codigoAtividade": null, "nome": "ROLLING", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/132.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/132.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/132.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 187, "codigoAtividade": null, "nome": "ROTAÇÃO DE OMBRO EM PÊNDULO", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/229.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/229.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/229.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 188, "codigoAtividade": null, "nome": "ROTAÇÃO DE TRONCO NO CABO", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/243.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/243.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/243.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 189, "codigoAtividade": null, "nome": "ROTAÇÃO EXTERNA", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/183.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/183.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/183.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 190, "codigoAtividade": null, "nome": "ROTAÇÃO EXTERNA DEITADO", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/190.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/190.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/190.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 191, "codigoAtividade": null, "nome": "ROTAÇÃO INTERNA", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/184.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/184.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/184.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 192, "codigoAtividade": null, "nome": "ROW PS (aberto)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/113.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/113.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/113.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 193, "codigoAtividade": null, "nome": "ROW PS (fechado)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/110.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/110.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/110.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 194, "codigoAtividade": null, "nome": "SERROTE", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/65.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/65.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/65.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 195, "codigoAtividade": null, "nome": "SHOULDER PRESS (aberto)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/11.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/11.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/11.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 196, "codigoAtividade": null, "nome": "SHOULDER PRESS (fechado)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/12.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/12.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/12.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 197, "codigoAtividade": null, "nome": "STIFF (barra)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/44.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/44.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/44.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 198, "codigoAtividade": null, "nome": "STIFF C/ HALTERES", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/176.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/176.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/176.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 199, "codigoAtividade": null, "nome": "STIFF (polia)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/179.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/179.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/179.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 200, "codigoAtividade": null, "nome": "SUPERMAN ALTERNADO", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/193.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/193.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/193.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 201, "codigoAtividade": null, "nome": "SUPINO DECLINADO (barra)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/123.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/123.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/123.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 202, "codigoAtividade": null, "nome": "SUPINO DECLINADO (halter)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/124.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/124.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/124.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 203, "codigoAtividade": null, "nome": "SUPINO INCLINADO (barra)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/121.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/121.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/121.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 204, "codigoAtividade": null, "nome": "SUPINO INCLINADO (halter)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/177.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/177.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/177.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 205, "codigoAtividade": null, "nome": "SUPINO RETO ARTICULAR", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/232.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/232.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/232.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 206, "codigoAtividade": null, "nome": "SUPINO RETO (barra)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/111.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/111.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/111.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 207, "codigoAtividade": null, "nome": "SUPINO RETO C/ HALTERES", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/127.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/127.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/127.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 208, "codigoAtividade": null, "nome": "SUPINO RETO FECHADO (barra)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/128.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/128.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/128.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 209, "codigoAtividade": null, "nome": "SUPINO RETO (halter)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/108.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/108.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/108.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 210, "codigoAtividade": null, "nome": "TIBIAL HUMMER", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/164.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/164.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/164.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 211, "codigoAtividade": null, "nome": "TIBIAL NO APARELHO EM PÉ", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/214.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/214.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/214.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 212, "codigoAtividade": null, "nome": "TOTAL ABDOMINAL", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/24.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/24.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/24.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 213, "codigoAtividade": null, "nome": "TRANSPORT", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/213.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/213.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/213.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 214, "codigoAtividade": null, "nome": "TRICEPS COICE (halter)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/103.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/103.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/103.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 215, "codigoAtividade": null, "nome": "TRÍCEPS COICE NO PULLEY", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/207.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/207.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/207.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 216, "codigoAtividade": null, "nome": "TRICEPS FRANCES BANCO 90°(barra)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/102.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/102.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/102.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 217, "codigoAtividade": null, "nome": "TRICEPS FRANCES BANCO 90°(halter)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/73.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/73.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/73.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 218, "codigoAtividade": null, "nome": "TRICEPS FRANCÊS BANCO 90°(halter bilateral)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/100.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/100.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/100.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 219, "codigoAtividade": null, "nome": "TRICEPS FRANCES (polia)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/182.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/182.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/182.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 220, "codigoAtividade": null, "nome": "TRÍCEPS FRANCÊS SENTADO", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/101.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/101.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/101.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 221, "codigoAtividade": null, "nome": "TRICEPS MERGULHO", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/112.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/112.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/112.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 222, "codigoAtividade": null, "nome": "TRÍCEPS PEG. INVERSA", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/141.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/141.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/141.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 223, "codigoAtividade": null, "nome": "TRICEPS POLIA ALTA", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/30.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/30.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/30.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 224, "codigoAtividade": null, "nome": "TRICEPS POLIA ALTA (corda)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/135.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/135.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/135.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 225, "codigoAtividade": null, "nome": "TRICEPS POLIA ALTA (supinado)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/32.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/32.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/32.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 226, "codigoAtividade": null, "nome": "TRICEPS POLIA ALTA (unilateral)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/138.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/138.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/138.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 227, "codigoAtividade": null, "nome": "TRÍCEPS PULLEY", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/140.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/140.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/140.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 228, "codigoAtividade": null, "nome": "TRICEPS TESTA (barra)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/70.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/70.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/70.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 229, "codigoAtividade": null, "nome": "TRICEPS TESTA COM HALTERES", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/68.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/68.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/68.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 230, "codigoAtividade": null, "nome": "TRICEPS TESTA (halter)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/69.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/69.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/69.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 231, "codigoAtividade": null, "nome": "TRÍCEPS TESTA NO PULLEY", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/142.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/142.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/142.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 232, "codigoAtividade": null, "nome": "TRICEPS TESTA POLIA ALTA", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/31.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/31.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/31.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 233, "codigoAtividade": null, "nome": "TRICEPS TESTA POLIA ALTA (corda)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/136.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/136.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/136.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 234, "codigoAtividade": null, "nome": "TRICEPS TESTA POLIA ALTA (unilateral)", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/137.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/137.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/137.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 235, "codigoAtividade": null, "nome": "VERTICAL TRACTION", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/4.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/4.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/4.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 236, "codigoAtividade": null, "nome": "WIDE CHEST PRESS PS", "descricao": null, "versao": "", "tipo": 0, "thumb": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Square/45.jpg", "img": "http://app.pactosolucoes.com.br/midias/DEFAULT/Mobile/Wide/45.jpg", "ordem": 0, "imgMedium": "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/45.jpg", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "NEUROMUSCULAR|", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 237, "codigoAtividade": null, "nome": "PUXADA POR TRAS", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 238, "codigoAtividade": null, "nome": "PUXADA COM TRIANGULO", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 239, "codigoAtividade": null, "nome": "REMADA UNILATERAL COM HALTERES", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 240, "codigoAtividade": null, "nome": "BICEPS ALTERNADO COM HALTERES", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 241, "codigoAtividade": null, "nome": "BICEPS BARRA ", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 242, "codigoAtividade": null, "nome": "BICEPS CONCENTRADO", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 243, "codigoAtividade": null, "nome": "FLEXÃO DOS ANTEBRAÇOS COM BARRA", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 244, "codigoAtividade": null, "nome": "SUPINO BARRA OLIMPICA", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 245, "codigoAtividade": null, "nome": "CRUCIFIXO INCLINADO COM HALTERES", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 246, "codigoAtividade": null, "nome": "CRUCIFIXO COM HALTERES", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 247, "codigoAtividade": null, "nome": "TRICEPS NO PULLEY", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 248, "codigoAtividade": null, "nome": "TRICEPS NO PULLEY COM CORDA", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 249, "codigoAtividade": null, "nome": "TRICEPS NO PULLEY UNILATERAL", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 250, "codigoAtividade": null, "nome": "ENCOLHIMENTO COM HALTERES", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 251, "codigoAtividade": null, "nome": "LEG PRESS INCLINADO 45º", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 252, "codigoAtividade": null, "nome": "FLEXOR DE JOELHOS", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 253, "codigoAtividade": null, "nome": "EXTENSOR DE JOELHOS", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 254, "codigoAtividade": null, "nome": "ABDUTOR NO APARELHO", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 255, "codigoAtividade": null, "nome": "ADUTOR NO APARELHO", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 256, "codigoAtividade": null, "nome": "PANTURRILHA NO APAR. EM PE", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 257, "codigoAtividade": null, "nome": "LEVANTAMENTO LATERAL COM HALTER", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 258, "codigoAtividade": null, "nome": "LEVANTAMENTO FRONTAL COM HALTER", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 259, "codigoAtividade": null, "nome": "DESENVOLVIMENTO C/ BARRA SENTADO", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 260, "codigoAtividade": null, "nome": "Hang Clean", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 261, "codigoAtividade": null, "nome": "<PERSON><PERSON><PERSON> Swing", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 262, "codigoAtividade": null, "nome": "<PERSON><PERSON><PERSON><PERSON>", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 263, "codigoAtividade": null, "nome": "Turkish Get Up", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 264, "codigoAtividade": null, "nome": "Clean & jerk ", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 265, "codigoAtividade": null, "nome": "Clean pull", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 266, "codigoAtividade": null, "nome": "Cluster", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 267, "codigoAtividade": null, "nome": "Deadlift", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 268, "codigoAtividade": null, "nome": "Front Squat", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 269, "codigoAtividade": null, "nome": "Hang Power Clean", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 270, "codigoAtividade": null, "nome": "Hang Power Snatch", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 271, "codigoAtividade": null, "nome": "Hang Squat Snatch", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 272, "codigoAtividade": null, "nome": "Muscle Clean", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 273, "codigoAtividade": null, "nome": "Muscle Snatch", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 274, "codigoAtividade": null, "nome": "Overhead Lunge", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 275, "codigoAtividade": null, "nome": "Overhead Squat", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 276, "codigoAtividade": null, "nome": "Power Clean", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 277, "codigoAtividade": null, "nome": "Power Snatch", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 278, "codigoAtividade": null, "nome": "<PERSON><PERSON> Jerk", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 279, "codigoAtividade": null, "nome": "Push Press", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 280, "codigoAtividade": null, "nome": "Shoulder Press", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 281, "codigoAtividade": null, "nome": "Snatch", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 282, "codigoAtividade": null, "nome": "Snatch Balance", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 283, "codigoAtividade": null, "nome": "Snatch deadlift", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 284, "codigoAtividade": null, "nome": "Snatch pull", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 285, "codigoAtividade": null, "nome": "Split jerk", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 286, "codigoAtividade": null, "nome": "Squat Clean", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 287, "codigoAtividade": null, "nome": "S<PERSON>t  Je<PERSON>", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 288, "codigoAtividade": null, "nome": "Squat Snatch", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 289, "codigoAtividade": null, "nome": "<PERSON><PERSON> Deadlift", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 290, "codigoAtividade": null, "nome": "Sumo deadlift high pull", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 291, "codigoAtividade": null, "nome": "Butterfly Pull-Up", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 292, "codigoAtividade": null, "nome": "Air Bike (100 Cal)", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 293, "codigoAtividade": null, "nome": "Air Bike (50 Cal)", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 294, "codigoAtividade": null, "nome": "Row 1km", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 295, "codigoAtividade": null, "nome": "Row 10km", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 296, "codigoAtividade": null, "nome": "Row 100m", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 297, "codigoAtividade": null, "nome": "Row 5km", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 298, "codigoAtividade": null, "nome": "The Turkish Get-Up", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 299, "codigoAtividade": null, "nome": "<PERSON>t", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 300, "codigoAtividade": null, "nome": "Bench Press", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 301, "codigoAtividade": null, "nome": "Clean", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 302, "codigoAtividade": null, "nome": "S<PERSON>t Je<PERSON>", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 303, "codigoAtividade": null, "nome": "<PERSON><PERSON><PERSON>", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 304, "codigoAtividade": null, "nome": "Air Bike (Máx Cal 1')", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 305, "codigoAtividade": null, "nome": "Row 2km", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 306, "codigoAtividade": null, "nome": "Row 21km", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 307, "codigoAtividade": null, "nome": "Row 500m", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 308, "codigoAtividade": null, "nome": "Run 1 mile", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 309, "codigoAtividade": null, "nome": "Run 1.200m", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 310, "codigoAtividade": null, "nome": "Run 10 km", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 311, "codigoAtividade": null, "nome": "Run 100m", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 312, "codigoAtividade": null, "nome": "Run 15km", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 313, "codigoAtividade": null, "nome": "Run 200m", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 314, "codigoAtividade": null, "nome": "Run 400m", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 315, "codigoAtividade": null, "nome": "Run 5 km", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 316, "codigoAtividade": null, "nome": "Run 800m", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 317, "codigoAtividade": null, "nome": "Abmat: <PERSON>", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 318, "codigoAtividade": null, "nome": "Bar Muscle-Ups: <PERSON>", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 319, "codigoAtividade": null, "nome": "Double Unders: <PERSON>", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 320, "codigoAtividade": null, "nome": "Box Jump: Max Height", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 321, "codigoAtividade": null, "nome": "Handstand Push-Ups: <PERSON> Reps", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 322, "codigoAtividade": null, "nome": "Handstand Walk: Max Distance", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 323, "codigoAtividade": null, "nome": "L-Sit: <PERSON>", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 324, "codigoAtividade": null, "nome": "Muscle-ups: 30 Reps for Time", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 325, "codigoAtividade": null, "nome": "Pull-Up (Weighted): 1 Rep Max", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 326, "codigoAtividade": null, "nome": "Pull-Ups (Chest To Bar): <PERSON>s", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 327, "codigoAtividade": null, "nome": "Pull-Ups (Strict Chest To Bar): <PERSON>s", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 328, "codigoAtividade": null, "nome": "Pull-Ups: <PERSON>", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 329, "codigoAtividade": null, "nome": "Push-Ups: <PERSON>", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 330, "codigoAtividade": null, "nome": "Ring Dips: <PERSON>", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 331, "codigoAtividade": null, "nome": "Ring Muscle-Ups: <PERSON>", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 332, "codigoAtividade": null, "nome": "Single Unders: <PERSON>s", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 333, "codigoAtividade": null, "nome": "<PERSON><PERSON>tand Push-Ups: <PERSON> Reps", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 334, "codigoAtividade": null, "nome": "<PERSON><PERSON> Muscle-Ups: <PERSON> Reps", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 335, "codigoAtividade": null, "nome": "<PERSON><PERSON><PERSON>", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 336, "codigoAtividade": null, "nome": "Toes To Bar: <PERSON>", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 337, "codigoAtividade": null, "nome": "WallBall: <PERSON>", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 338, "codigoAtividade": null, "nome": "<PERSON><PERSON>", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 339, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 340, "codigoAtividade": null, "nome": "Bear Complex", "descricao": "5 rounds for max load of 7 sets:\n1 Power Clean\n1 Front Squat\n1 Push Press\n1 Back Squat\n1 Back rack push press\nYou may not drop the bar between rounds but may rest as needed between sets", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 341, "codigoAtividade": null, "nome": "Broomstick Mile", "descricao": "For Time\n25 Back Squats\n25 Front Squats\n25 Overhead Squats\n400 meter Run\n25 Shoulder Presses\n25 Push Presses\n25 Push Jerks\n400 meter Run\n50 Hang Cleans\n400 meter Run\n50 Snatches\n400 meter Run", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 342, "codigoAtividade": null, "nome": "Crossfit Total", "descricao": "Three attempts of each lift:\n. Back Squat\n. Shoulder Press\n. Deadlift\nScore the total of your best successful lifts", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 343, "codigoAtividade": null, "nome": "300", "descricao": "For Time\n25 Pull-Ups\n50 Deadlifts (135/95 lb)\n50 Push-Ups\n50 Box Jumps (24/20 in)\n50 Floor Wipers (135/95 lb) (one count)\n50 Kettlebell Clean-and-Press (1/.75 Pood) (alternating)\n25 Pull-Ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 344, "codigoAtividade": null, "nome": "<PERSON>", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 345, "codigoAtividade": null, "nome": "Open 14.2", "descricao": null, "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 346, "codigoAtividade": null, "nome": "Games 11.02", "descricao": "Max L-sit for time (1 attempt)\nMax distance Softball throw (2 attempts)\nMax distance Handstand walk (1 attempt with 1 mulligan it less than 5\nyards)\nAthletes will be scored for this event in the same system as the Regional\nscoring And they will be ranked in each test with the sum of their 3 ranking\ndetermining their final score for the event. ow score wins and will receive\nthe identical points as the other events", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 347, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "For time:\n- 100 PuIl-ups\n- 100 Push-ups\n- 100 Sit-ups\n- 100 Squats", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 348, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "5 rounds for time\n- 20 Pull-ups\n- 30 Push-ups\n- 40 Sit-ups\n- 50 Squats", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 349, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "3 rounds for time:\n- 500m Row\n- 12 Body Weight Deadlift\n- 21 Box jumps", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 350, "codigoAtividade": null, "nome": "C<PERSON><PERSON>", "descricao": "AMRAP in 20 minutes:\n- 5 PuIl-ups\n- 10 Push-ups\n- 15 Squats", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 351, "codigoAtividade": null, "nome": "Open 14.5", "descricao": "21-18-15-12-9-6-3 <PERSON><PERSON>, For <PERSON>\nThrusters (95/65 lb)\nBar Facing B<PERSON>es", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 352, "codigoAtividade": null, "nome": "Open 14.1", "descricao": "For As Long As Possible\nFrom 0:00-3:00, 2 rounds of:\n10 Overhead Squats (95/65 lb)\n10 Chest-to-Bar Pull-Ups\nFrom 3:00-6:00, 2 rounds of:\n12 Overhead Squats (95/65 lb)\n12 Chest-to-Bar Pull-Ups\nFrom 6:00-9:00, 2 rounds of:\n14 Overhead Squats (95/65 lb)\n14 Chest-to-Bar Pull-Ups\nFollow the pattern until you fail to complete both rounds.", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 353, "codigoAtividade": null, "nome": "Open 14.3", "descricao": "AMRAP in 8 minutes\n10 Deadlifts (135/95 lb)\n15 Box Jumps (24/20 in)\n15 Deadlifts (185/135 lb)\n15 Box Jumps (24/20 in)\n20 Deadlifts (225/155 lb)\n15 Box Jumps (24/20 in)\n25 Deadlifts (275/185 lb)\n15 Box Jumps (24/20 in)\n30 Deadlifts (315/205 lb)\n15 Box Jumps (24/20 in)\n35 Deadlifts (365/225 lb)\n15 Box Jumps (24/20 in)\nFor the box jumps athlete may jump or step up or down as long as both feet start on the ground and both feet end on the box in control.", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 354, "codigoAtividade": null, "nome": "Open 14.4", "descricao": "AMRAP in 14 minutes\n60 calorie Row\n50 Toes-to-Bars\n40 Wall Ball Shots (20/14 lb, 10/9 ft)\n30 Cleans (135 lb)\n20 Muscle-Ups\nIf athlete completes one round, start again on the rower and continue the sequence until time is up. Score is total repetitions completed (on the rower 1 calorie = 1 rep).", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 355, "codigoAtividade": null, "nome": "Open 15.1", "descricao": "Complete as many rounds and reps as possible in 9 minutes of:\n15 Toes-to-Bars\n10 Deadlifts (115/75 lb)\n5 Snatches (115/75 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 356, "codigoAtividade": null, "nome": "Open 15.1a", "descricao": "1 rep max. <PERSON> and <PERSON><PERSON>\n6-minute time cap", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 357, "codigoAtividade": null, "nome": "Open 15.2", "descricao": "For As Long As Possible\nFrom 0:00-3:00, 2 rounds of:\n10 Overhead Squats (95/65 lb)\n10 Chest-to-Bar Pull-Ups\nFrom 3:00-6:00, 2 rounds of:\n12 Overhead Squats (95/65 lb)\n12 Chest-to-Bar Pull-Ups\nFrom 6:00-9:00, 2 rounds of:\n14 Overhead Squats (95/65 lb)\n14 Chest-to-Bar Pull-Ups\nFollow the pattern until you fail to complete both rounds.", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 358, "codigoAtividade": null, "nome": "Open 15.3", "descricao": "AMRAP in 14 minutes\n7 Muscle-Ups\n50 Wall Balls (20/14 lb)\n100 Double-Unders\nAfter the last double-under, the athlete will move back to the rings and begin the next round. Score will be the total number of repetitions completed before the 14-minute time cap.", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 359, "codigoAtividade": null, "nome": "Open 15.5", "descricao": "27-21-15-9 Reps for Time\nRow (calories)\nThrusters (95/65 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 360, "codigoAtividade": null, "nome": "Open 16.1", "descricao": "AMRAP in 20 minutes\n25 ft Overhead Walking Lunges (95/65 lb)\n8 Bar-Facing Burpees\n25 ft Overhead Walking Lunges (95/65 lb)\n8 Chest-to-Bar Pull-Ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 361, "codigoAtividade": null, "nome": "Open 16.3", "descricao": "AMRAP in 7 minutes\n10 Power Snatches (75/55 lb)\n3 Bar Muscle-Ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 362, "codigoAtividade": null, "nome": "Open 16.5", "descricao": "21-18-15-12-9-6-3 Reps For Time\nThrusters (95/65 lb)\nBar-Facing <PERSON><PERSON><PERSON>", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 363, "codigoAtividade": null, "nome": "Open 17.5", "descricao": "10 Rounds for Time\n9 Thrusters (95/65 lb)\n35 Double-Unders\nTime Cap: 40 minutes\n\nRight after this workout was announced we live streamed on YouTube as two athletes from NorCal gave it a go. Subscribe on YouTube to be notified when we publish WOD highlights with their commentary.", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 364, "codigoAtividade": null, "nome": "Open 18.2 % 18.2a", "descricao": "For Time\n1-2-3-4-5-6-7-8-9-10 Reps of:\n<PERSON><PERSON><PERSON> (2 x 50/35 lb)\nBar-Facing B<PERSON><PERSON>es\nThen, \"18.2 A\"\n1 rep max Clean\nTime Cap: 12 minutes", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 365, "codigoAtividade": null, "nome": "Open 18.3", "descricao": "2 Rounds for Time\n100 Double-Unders\n20 Overhead Squats (115/80 lb)\n100 Double-Unders\n12 Ring Muscle-Ups\n100 Double-Unders\n20 Dumbbell Snatches (50/35 lb)\n100 Double-Unders\n12 Bar Muscle-Ups\nTime cap: 14 minutes", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 366, "codigoAtividade": null, "nome": "Open 18.4", "descricao": "For Time\n21-15-9 Reps of:\nDeadlift (225/155 lb)\nHandstand Push-Ups\nThen, 21-15-9 Reps of:\nDeadlift (315/205 lb)\n50 ft Handstand Walk\nTime Cap: 9 minutes", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 367, "codigoAtividade": null, "nome": "Open 18.5", "descricao": "AMRAP in 7 minutes\n3 Thrusters (100/65 lb)\n3 Chest-to-Bar Pull-Ups\n6 Thrusters (100/65 lb)\n6 Chest-to-Bar Pull-Ups\n9 Thrusters (100/65 lb)\n9 Chest-to-Bar Pull-Ups\nIf you complete the round of 9, complete a round of 12, then go on to 15, etc.", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 368, "codigoAtividade": null, "nome": "Games 11.03", "descricao": "For time:\n15 foot Rope climb, 5 ascents\n145 pound Clean and jerk, 5 reps\n15 fool Rope climb, 4 ascents\n165 pound Clean and jerk, 4 reps\n15 fool Rope climb, 3 ascents\n185 pound Clean and jerk, 3 reps\n15 foot Rope climb, 2 ascents\n205 pound clean and jerk.2 reps\n15 foot Rope climb, 1 ascent\n225 pound Clean and jerk. 1 rep", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 369, "codigoAtividade": null, "nome": "Games 11.04", "descricao": "For time:\n5 Muscle-ups\n245 pound Deadlift. 10 reps\n15 GHD Sit-ups\nSprint 50 yards\n5 Muscle-ups\n245 pound Deadlift. 10 reps\n15 GHD Situps\nSprint l00 yards\n5 Muscle-ups\n245 pound Deadlift. 10 reps\n15 GHD Sit-ups\nSprint 150 yards\n5 Muscle-ups\n245 pound Deadlift. 10 reps\n15 GHD Sit-ups\nSprint 200 yards", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 370, "codigoAtividade": null, "nome": "Games 11.5", "descricao": "1 rep max weighted Chest-to-bar pull-vp for load\n1 rep max Snatch for load\nJug carry for distance in 60 seconds\nEach athlete has 2 minutes tu establish al rep max chest-to-bar pul[ups, 2\nminutes to establish al rep max snatch, and then will have 60 seconds to\ncarry 2 weighted water jugs. as tar as possible.\nAthletes will be ranked in each test with the sum of their 3 rankings.\ndetermining their final score for the event", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 371, "codigoAtividade": null, "nome": "Games 11.06", "descricao": "Three rounds for time of:\n225 pound Front squat, 7 reps\nBike 700 meters\nloo toot Monkey bar traverse", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 372, "codigoAtividade": null, "nome": "Games 11.07", "descricao": "For time:\nThree rounds of\n30 Double-unders\n135 pound Overhead squat, 10 reps\nthen, Three rounds of\n10 Handstand push-ups\n40 foot Sled push, Sled + 385 pounds\n", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 373, "codigoAtividade": null, "nome": "Fight Gone Bad", "descricao": "3 Rounds For Total Reps in 17 minutes\n1 minute Wall Balls (20/14 lb)\n1 minute <PERSON><PERSON> Deadlift High-Pulls (75/55 lb)\n1 minute Box Jumps (20 in)\n1 minute Push Press (75/55 lb)\n1 minute Row (calories)\n1 minute Rest", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 374, "codigoAtividade": null, "nome": "Filthy Fifty", "descricao": "For Time\n50 Box Jumps (24/20 in)\n50 Jumping Pull-Ups\n50 Kettlebell Swings (1/.75 pood)\n50 Walking Lunges\n50 Knees-to-Elbows\n50 Push Press (45/35 lb)\n50 Back Extensions\n50 Wall Balls (20/14 lb)\n50 Burpees\n50 Double-Unders", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 375, "codigoAtividade": null, "nome": "Hope", "descricao": "3 Rounds For Total Reps in 17 minutes\n1 minute <PERSON><PERSON><PERSON><PERSON>\n1 minute Power Snatch (75/55 lb)\n1 minute Box Jump (24/20 in)\n1 minute Thruster (75/55 lb)\n1 minute Chest-to-Bar Pull-Ups\n1 minute Rest", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 376, "codigoAtividade": null, "nome": "Games 11.08", "descricao": "Complete as many reps as possible in 3 minutes of:\n20 calorie Row\n30 WalIball Shots, 20 pound ball\n20 Toes to bar\n30 Box jumps. 24\" box\n20 Sumo-deadlit high-pull. 108 pound kettlebell\n30 Burpees\n20 Shoulder to overhead, 135 pounds\nSled pull\nThere will bel minute of rest before Event 9 begins.", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 377, "codigoAtividade": null, "nome": "Games 11.09", "descricao": "Complete as many reps as possible in 6 minutes of:\n20 calorie Row\n30 WaIlball Shots, 20 pound ball\n20 Toes to bar\n30 Box jumps, 24” box\n20 Sumo-deadlift high-puIl, 108 pound kettlebell\n30 Burpees\n20 Shoulder Lo overhead, 135 pounds\nSled pull\nThere will be 2 minutes of rest before Event 10 begins", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 378, "codigoAtividade": null, "nome": "DT", "descricao": "5 Rounds For Time\n12 Deadlifts (155/105 lb)\n9 Hang Power Cleans (155/105 lb)\n6 Push Jerks (155/105 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 379, "codigoAtividade": null, "nome": "Games 11.10", "descricao": "For time:\n20 calorie Row\n30 WalIball Shots. 20 pound ball\n20 Toes to bar\n30 Box jumps. 24” box\n20 Sumo-deadlift high-pull, 108 pound kettlebell\n20 Toes to bar\n30 Box jumps. 24 box\n20 Sumo-deadlit high.pull, 108 pound kettlebell\n30 Burpees\n20 Shoulder to overhead, 135 pounds\nSled pull\n", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 380, "codigoAtividade": null, "nome": "Games 12.01", "descricao": "Far time:\n50 Double-unders\nLow banger\n50 Double-unders\nDown banger\n50 Double-unders\nMid Banger\n\nTime Cap: 9 minutes", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 381, "codigoAtividade": null, "nome": "Iron Triathlon", "descricao": "For time, 1-20 reps of:\nDeadlift 1.5xBW\nBench Press 1xBW\nSquat clean 0.75xBW", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 382, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "For time:\n21- 15 - 9 reps\n. <PERSON><PERSON> Squat (95/65lbs)\n. <PERSON><PERSON><PERSON><PERSON>", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 383, "codigoAtividade": null, "nome": "King Kong", "descricao": "3 rounds for time\n1 Deadlilt 455lbs\n2 Muscle Ups\n3 Cleans 250lbs\n4 Handstand Push Ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 384, "codigoAtividade": null, "nome": "<PERSON>sty <PERSON>", "descricao": "3 rounds for time\n- 50 Squats\n- 7 Muscle-ups\n- 10 Hang Power Clean (135/95lbs)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 385, "codigoAtividade": null, "nome": "Tabata Something Else", "descricao": "8 rounds, 20 seconds workout and 10 seconds rest:\n- Pull-ups\n- Push-ups\n- Sit-up\n- Squats", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 386, "codigoAtividade": null, "nome": "Tabata This", "descricao": "8 rounds, 20 seconds workout and 10 seconds rest:\n- Squats\n- Row\n- PuIl-ups\n- Sit-ups\n- Push-ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 387, "codigoAtividade": null, "nome": "The Chief", "descricao": "Max rounds in 3 minutes:\n- 3 Power Clean (135/95lbs)\n- 6 Push-ups\n- 9 Squats\nRest 1 minute. Repeat for a total of 5 cycles", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 388, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "9 - 7 - 5 reps:\n- Mu<PERSON>cle-up\n- <PERSON><PERSON><PERSON> Snatch (135/95lbs)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 389, "codigoAtividade": null, "nome": "Chelsea", "descricao": "EMOM 30 minutes:\n- 5 PuIl-ups\n- l0 Push-ups\n- 15 Squats", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 390, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "21 - 15 - 9 reps for time:\n- DeadLift (225/155lbs)\n- Handstand Push-ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 391, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "21 - 15 - 9 reps for time:\n- Clean (135/85lbs)\n- <PERSON>", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 392, "codigoAtividade": null, "nome": "Eva", "descricao": "5 rounds for time\n- 800m Run\n- 30 KettIebell Swing (2115 pood)\n- 30 PuII-ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 393, "codigoAtividade": null, "nome": "<PERSON><PERSON>", "descricao": "21 -15 - 9 reps for time:\n- <PERSON><PERSON><PERSON> (95/65Ibs)\n- PuIl-ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 394, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "For time:\n- 30 Clean & Jerk (135/95lbs)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 395, "codigoAtividade": null, "nome": "Games 12.03", "descricao": "Standing broad jump for distance\nAthletes will have three attempts to complete a standi,ig broad jump for\ndistance Athletes will be ranked by distance oÍ the furthest of their three\nattempts.", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 396, "codigoAtividade": null, "nome": "Games 12.05", "descricao": "Athletes wdl begin this event just after completing the Ball Toss.\nThree rounds for time Of:\n8 Split snatch, alternating legs (115 I l5lbs)\n1 Bar muscle-ups\nRun 400 meters\n\nAthletes will be ranked by time.\n\nTime Cap: 13 minutes", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 397, "codigoAtividade": null, "nome": "Games 12.11", "descricao": "For time:\n50 Double-unders\nLow banger\n50 Double-unders\nDown banger\n50 Double-unders\n\nTime Cap: 9 minutes", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 398, "codigoAtividade": null, "nome": "Games 13.01", "descricao": "10 rounds for time of:\n<PERSON>wim 25 yards\n3 Bar muscle-ups\nSwim 25 yards\ntime Cap: 25 minutes", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 399, "codigoAtividade": null, "nome": "Games 13.05", "descricao": "Far time:\n27 Thrusters (95/65 lbs)\n4 Legless rope climbs\n21 Thrusters (95/65 lbs)\n3 Legless rope climbs\n15 Thrusters (95 /65 lbs)\n2 Legless rope climbs\n9 Thrusters (95/65 lbs)\n1 Legless rope climbs\nTime Cap: 10 minutes", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 400, "codigoAtividade": null, "nome": "Games 13.08", "descricao": "For time:\nRow 1000 meters\nthen. five rounds Of:\n25 Pull-ups\n1 Push jerks (135 / 85 lbs)\n\nTime Cap: 15 minutes", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 401, "codigoAtividade": null, "nome": "Games 14.01", "descricao": "For time:\nSwim 250 yards\n50 kettlebell thrusters (35 / 24 Ib]\n30 burpees\nSwim 500 yards\n30 burpees\n50 kettlebell thrusters (35 / 24 lb)\nSwim 250 yards", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 402, "codigoAtividade": null, "nome": "Games 14.03", "descricao": "For time:\nRow 3,000 meters\n300 double-unders\nRun 3 miles", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 403, "codigoAtividade": null, "nome": "Games 14.08", "descricao": "For time:\nSprint 100 yards\n100-yard carry (100/60 Ib. cylinder)\nSprint 100 yards\n100-yard carry (120/80 Ib sandbag)\nSprint 100 yards\n100-yard carry (150 / 100lb. cylinder)\n", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 404, "codigoAtividade": null, "nome": "Games 14.10", "descricao": "3 rounds for time Of:\n25 GHD sit-ups\n50-ft handstand walk\n50-ft. overhead walking lunge (155 / 115 lb.)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 405, "codigoAtividade": null, "nome": "Games 15.01", "descricao": "For lime:\nSwim 500 meters\nPaddle 2 miles\nSwim 500 meters", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 406, "codigoAtividade": null, "nome": "Games 15.10", "descricao": "15-10-6 reps for time of:\nThruster<PERSON> (165 /115 Ib)\nBar muscle-ups\nTime Cap: 10 minutes", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 407, "codigoAtividade": null, "nome": "Games 15.12", "descricao": "For time:\n3 peg board ascents\n24-calorie row\n16-calorie bike\n8 dumbbell squat snatches (100 / 70 lb.)\n\nTime Cap: 6 minutes", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 408, "codigoAtividade": null, "nome": "Games 16.03", "descricao": "For time:\n50 waIl-ball shots\n25 med-balI GHD sit-ups\nHill sprint with med ball\n\nMen use 30-Ib. ball\nWomen use 20-lb. ball", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 409, "codigoAtividade": null, "nome": "Games 16.10", "descricao": "For time:\n40 box jumps (30/ 24-inch)\n20 D-ball cleans (150 / 100 Ib.)\nTime Cap: 5 minutes", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 410, "codigoAtividade": null, "nome": "Games 16.11", "descricao": "280-ft handstand walk for time", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 411, "codigoAtividade": null, "nome": "Games 16.15", "descricao": "For time:\n3 pegboard ascents\n21 thrusters\n2 pegboard ascents\n15 thrusters\n1 pegboard ascent\n9 thrusters \n\nMen 135lb. Women 85 lb. \n\nTime cap: 10 minutes", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 412, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "15 - 12 - 9 reps:\n- Clean Jerk\nTouch and go at floor only. Even a regrjp off the floor is a foul. No dumping.\nUse same load for each set. Rest as needed between sets.", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 413, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "3 rounds for time\n- 400m Run\n- 21 Kettlebell Swings (15/1 pood)\n.12 Pull-ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 414, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "For time:\n- 30 Snatch (135/95lbs)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 415, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "Por tempo:\n- l000m Remo\n- 50 Thruster #45/35\n- 30 PuIl-Ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 416, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "Por tempo:\n- 150 WaIl Ball #20/l4Ibs", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 417, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "5 rounds por tempo:\n- 400m Corrida\n- 30 Box Jump #24/20”\n- 30 WaIl Ball", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 418, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "10 - 9 - 8 - 1 - 6 - 5 - 4 - 3 - 2 - 1 reps por tempo:\n- Deadlift #1.5 x peso corporal\n- Benchpress #1 x peso corporal\n- Clean #3/4 x peso corporal", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 419, "codigoAtividade": null, "nome": "<PERSON><PERSON>", "descricao": "5 rounds por reps:\n- Max. reps Benchpress #peso corporal\n- Max. reps PulI-Ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 420, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "AMRAP 20 minutos:\n- 5 Handstand Push-Up\n- 10 Pistol Squats\n- 15 Pull-Ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 421, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "21 - 15 - 9 reps for time:\n- <PERSON><PERSON><PERSON><PERSON>\n- <PERSON><PERSON><PERSON> (53/351bs)\n- Double-Unders", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 422, "codigoAtividade": null, "nome": "Nancy", "descricao": "5 rounds por tempo:\n- 400m Corrida\n- 15 Overhead Squat #95/65Ibs", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 423, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "AMRAP 20 minutos:\n- 400m Corrida\n- Max. reps PuI-Ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 424, "codigoAtividade": null, "nome": "Open 12.1", "descricao": "AMRAP 7 minutos:\n- <PERSON><PERSON><PERSON><PERSON>", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 425, "codigoAtividade": null, "nome": "MICHAEL", "descricao": "3 Rounds For Time\n800 meter Run\n50 Back Extensions\n50 Sit-Ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 426, "codigoAtividade": null, "nome": "Open 12.2", "descricao": "Proceed through sequence completing as many reps as possible in 10\nminutes:\n-30 Snatch #75/45lbs\n- 30 Snatch #135/75lbs\n- 30 Snatch #165/100lbs\nAMRAP Snatch #210/120lbs", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 427, "codigoAtividade": null, "nome": "Open 12.3", "descricao": "Complete as many rounds and reps as possible in 18 minutes:\n- 15 Box Jump #24/20\"\"\n- 12 Push Presses #115/75lbs\n- 9 <PERSON><PERSON> to <PERSON>", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 428, "codigoAtividade": null, "nome": "Open 12.4", "descricao": "Complete as many rounds and reps as possible in 12 minutes:\n- 150 Wall Ball #20/l4Ibs\n- 90 Double Unders\n- 30 Muscle Ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 429, "codigoAtividade": null, "nome": "AdamBrown", "descricao": "2 Rounds For Time\n24 Deadlifts (295/205 lb)\n24 Box Jumps (24/20 in)\n24 Wall Ball Shots (20/14 lb)\n24 Bench Press (195/125 lb)\n24 Box Jumps (24/20 in)\n24 Wall Ball shots (20/14 lb)\n24 Cleans (145/100 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 430, "codigoAtividade": null, "nome": "<PERSON><PERSON><PERSON>", "descricao": "For time:\n-  mile Run\n- 21 <PERSON> & Jerk (155lbs)\n- 800m Run\n- 21 Clean & Jerk (l55lbs)\n-1 mile Run", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 431, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "7 Rounds For Time\n3 Forward Rolls\n5 Wall Climbs\n7 Toes-to-Bar\n9 Box Jumps (30 in)\n", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 432, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "5 Rounds for Time\n31 Back Squats (135/95 lb)\n12 Power Cleans (185/135 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 433, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "For Time\n25 Thrusters (115/85 lb)\n50 Box Jumps (24/20 in)\n75 Deadlifts (115/85 lb)\n1.5 mile Run\n75 Deadlifts, (115/85 lb)\n50 Box Jumps (24/20 in)\n25 Thrusters (115/85 lb)\nWear a weight vest (20/14 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 434, "codigoAtividade": null, "nome": "<PERSON><PERSON><PERSON>", "descricao": "For Time\n21 Turkish Get-Ups, Right Arm\n50 Ke<PERSON>bell Swings\n21 Overhead Squats, Left Arm\n50 Kettlebell Swings\n21 Overhead Squats, Right Arm\n50 Kettlebell Swings\n21 Turkish Get-Ups, Left Arm\nUse a single Kettlebell (2/1.5 pood)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 435, "codigoAtividade": null, "nome": "<PERSON><PERSON>", "descricao": "AMRAP in 20 minutes\n5 Pull-Ups\n10 Push-Ups\n15 Squats\n5 Pull-Ups\n10 Thrusters (95/65 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 436, "codigoAtividade": null, "nome": "Badger", "descricao": "3 Round For Time\n30 Squat Cleans (95/65 lb)\n30 Pull-Ups\n800 meter Run", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 437, "codigoAtividade": null, "nome": "Barraza", "descricao": "AMRAP in 18 minutes\n200 meter Run\n9 Deadlift (275/185 lb)\n6 Burpee Bar Muscle-Ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 438, "codigoAtividade": null, "nome": "Bell", "descricao": "3 Rounds for Time\n21 Deadlifts (185/135 lb)\n15 Pull-Ups\n9 Front Squats (185/135 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 439, "codigoAtividade": null, "nome": "Big Sexy", "descricao": "5 Rounds For Time\n6 Deadlifts (315/205 lb)\n6 Bur<PERSON>es\n5 Cleans (225/155 lb)\n5 Chest-to-Bar Pull-Ups\n4 Thrusters (155/115 lb)\n4 Muscle-Ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 440, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "4 Rounds For Time\n100 ft Overhead Walking Lunge (45/35 lb plate)\n30 Box Jumps (24/20 in)\n20 Wall Balls Shots (20/14 lb, 10/9 ft)\n10 Handstand Push-Ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 441, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "3 Rounds For Time\n800 meter Run\n7 Deadlifts (275/185 lb)\n10 Burpee Pull-Ups\n14 Single Arm Kettlebell Thrusters (1.5/1 pood)\n20 Box Jumps (24/20 in)\nFor the kettlebell thrusters, do 7 per arm (14 total) per round.", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 442, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "10 Rounds For TIme\n100 meter Sprint\n10 Pull-Ups\n100 meter Sprint\n10 Burpees\n30 seconds Rest", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 443, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "10 Rounds for Time\n3 Handstand Push-Ups\n6 Deadlift (225/155 lb)\n12 Pull-Ups\n24 Double-Unders", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 444, "codigoAtividade": null, "nome": "<PERSON><PERSON><PERSON>", "descricao": "For Time\n10 Rope Climbs (15 ft)\n20 Back Squats (225 lb)\n30 Handstand Push-Ups\n40 calorie Row", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 445, "codigoAtividade": null, "nome": "<PERSON><PERSON>", "descricao": "5 Rounds For Time\nBear Crawl (100 ft)\nStanding Broad-Jumps (100 ft)\nPerform 3 Burpees after every 5 Broad-Jumps\nWear a Weight Vest (20/14 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 446, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "3 Rounds For Time\n5 Rope Climbs (15 ft)\n25 Back Squats (185/135 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 447, "codigoAtividade": null, "nome": "<PERSON><PERSON><PERSON>", "descricao": "4 Rounds For Time\n400 meter Run\n24 Back Squats (185/135 lb)\n24 Jerks (135/95 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 448, "codigoAtividade": null, "nome": "Bulger", "descricao": "10 Rounds For Time\n150 Meter Run\n7 Chest-to-Bar Pull-Ups\n7 Front Squats (135/95 lbs)\n7 Handstand Push-Ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 449, "codigoAtividade": null, "nome": "Bull", "descricao": "2 Rounds For Time\n200 Double-Unders\n50 Overhead Squats (135/95 lb)\n50 Pull-Ups\n1 mile Run", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 450, "codigoAtividade": null, "nome": "DG", "descricao": "AMRAP in 10 minutes\n8 Toes-to-Bar\n8 Dumbbell Thrusters (35/25 lb)\n12 Dumbbell Walking Lunges (35/25 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 451, "codigoAtividade": null, "nome": "Open 13.4", "descricao": "AMRAP in 7 minutes\n3 Clean-and-Jerks (135/95 lb)\n3 Toes-to-Bars\n6 Clean-and-Jerks (135/95 lb)\n6 Toes-to-Bars\n9 Clean-and-Jerks (135/95 lb)\n9 Toes-to-Bars\n12 Clean-and-Jerks (135/95 lb)\n12 Toes-to-Bars\nIf athlete completes the round of 12, go on to 15. Complete 15, go on the 18, etc.", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 452, "codigoAtividade": null, "nome": "Open 13.5", "descricao": "AMRAP For As Long As Possible\n15 Thrusters (100/65 lb)\n15 Chest-to-Bar Pull-Ups\nIf 90 reps (3 rounds) are completed in under 4 minutes, time extends to 8 minutes.\nIf 180 reps (6 rounds) are completed in under 8 minutes, time extends to 12 minutes. Etc.", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 453, "codigoAtividade": null, "nome": "Open 13.2", "descricao": "AMRAP in 10 minutes\n5 Shoulder-to-Overheads (115/75 lb)\n10 Deadlifts (115/75 lb)\n15 Box Jumps (24/20 in)\n“Shoulder-to-Overhead” allows the athlete to start with a barbell on the shoulders and move the bar overhead with the arms any way (ie: strict press, push press, push jerk, etc.)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 454, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "For Time\n50 Walking Lunges\n25 Chest-to-Bar Pull-Ups\n50 Box Jumps (24/20 in)\n25 Triple-Unders\n50 Back Extensions\n25 Ring Dips\n50 Knees-to-Elbows\n25 Wall Ball \"2-for-1's\" (20/14 lb)\n50 Sit-Ups\n5 Rope Climb (15 ft)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 455, "codigoAtividade": null, "nome": "<PERSON><PERSON>", "descricao": "For Time\n100 Push-Ups\n800 meter Run\n75 Push-Ups\n1,200 meter Run\n50 Push-Ups\n1,600 meter Run\n25 Push-Ups\n2,000 meter Run", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 456, "codigoAtividade": null, "nome": "<PERSON><PERSON>", "descricao": "21-18-15-12-9-6-3 Reps for Time\nSquat Clean (95/65 lb)\nDouble-Unders\nDeadlift (185/135 lb)\nBox Jump (24/20 in)\nStart each round with a 50 meter Bear crawl", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 457, "codigoAtividade": null, "nome": "<PERSON><PERSON><PERSON>", "descricao": "For Time\n10 mile Run\n150 Burpee Pull-Ups\nPartition the Run and Burpee Pull-Ups as needed.", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 458, "codigoAtividade": null, "nome": "<PERSON>e", "descricao": "10 Rounds For Time\n10 Thrusters (95/65 lb)\n10 Ring Push-Ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 459, "codigoAtividade": null, "nome": "<PERSON><PERSON><PERSON>", "descricao": "For Time\n800 meter Run\n50 Back Squats (135/95 lb)\n50 Bench Press (135/95 lb)\n800 meter Run\n35 Back Squat (135/95 lb)\n35 Bench Press (135/95 lb)\n800 meter Run\n20 Back Squat (135/95 lb)\n20 Bench Press (135/95 lb)\n800 meter Run\n1 Muscle-Up", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 460, "codigoAtividade": null, "nome": "Coff<PERSON>", "descricao": "For Time\n6 minute Hang Hold (cumulative)\nEach time you drop from the bar, perform:\n800 meter Run\n30 Push-Ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 461, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "6 Rounds For TIme\n400 meter Sandbag Carry (50/40 lb)\n12 Push Press (115/75 lbs)\n12 Box Jumps (24/20 in)\n12 Sumo Deadlift High-Pull (95/65 lbs)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 462, "codigoAtividade": null, "nome": "DVB", "descricao": "For Time\n1 mile Run with medicine ball (20/14 lb)\nThen 8 Rounds of:\n10 Wall Ball Shots (20/14 lb)\n1 Rope Climb (15 ft)\nThen, 800 meter Run with medicine ball (20/14 lb)\nThen 4 Rounds of:\n10 Wall Ball Shots (20/14 lb)\n1 Rope Climb (15 ft)\nThen, 400 meter Run with medicine ball (20/14 lb)\nThen 2 Rounds of:\n10 Wall Ball Shots (20/14 lb)\n1 Rope Climb (15 ft)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 463, "codigoAtividade": null, "nome": "Open 12.5", "descricao": "AMRAP in 7 minutes\n3 Thrusters (100/65 lb)\n3 Chest-to-Bar Pull-Ups\n6 Thrusters (100/65 lb)\n6 Chest-to-Bar Pull-Ups\n9 Thrusters (100/65 lb)\n9 Chest-to-Bar Pull-Ups\nIf you complete the round of 9, complete a round of 12, then go on to 15, etc.", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 464, "codigoAtividade": null, "nome": "<PERSON><PERSON>", "descricao": "3 Rounds For Time\n800 meter Run (with 45/35 lb barbell)\n3 Rope Climbs (15 ft)\n12 Thrusters (135/95 lb)\n", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 465, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "For Time\n50 Pull-Ups\n400 meter Run\n21 Thrusters (95/65 lb)\n800 meter Run\n21 Thrusters (95/65 lb)\n400 meter Run\n50 Pull-Ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 466, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "AMRAP in 20 minutes\n30 Box Jumps (24/20 in)\n20 Push Press (115/75 lb)\n30 Pull-Ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 467, "codigoAtividade": null, "nome": "Desforges", "descricao": "5 Rounds For Time\n12 Deadlifts (225/155 lb)\n20 Pull-Ups\n12 Clean-and-Jerks (135/95 lb)\n20 Knees-to-Elbows", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 468, "codigoAtividade": null, "nome": "DEL", "descricao": "For Time\n25 Burpees\n400 meter Run (20/14 lb medicine ball)\n25 Weighted Pull-Ups (20/15 lb dumbbell)\n400 meter Run (20/14 lb medicine ball)\n25 Handstand Push-Ups\n400 meter Run (20/14 lb medicine ball)\n25 Chest-to-Bar Pull-Ups\n400 meter Run (20/14 lb medicine ball)\n25 Burpees", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 469, "codigoAtividade": null, "nome": "Dobogai", "descricao": "7 Rounds For Time\n8 Muscle-Ups\n22 Yard <PERSON> (50/40 lb dumbells)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 470, "codigoAtividade": null, "nome": "<PERSON> (The Don)", "descricao": "For Time\n66 Deadlifts (110/75 lb)\n66 Box Jump (24/20 in)\n66 Kettlebell swings (1.5/1 pood)\n66 Knees-to-Elbows\n66 Sit-Ups\n66 Pull-Ups\n66 Thrusters (55/35 lb)\n66 Wall Ball Shots (20/14 lb)\n66 <PERSON><PERSON><PERSON><PERSON>\n66 Double-Unders", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 471, "codigoAtividade": null, "nome": "<PERSON><PERSON>", "descricao": "21-15-9-9-15-21 Reps, For Time\nDeadlifts (225/155 lb)\n<PERSON><PERSON><PERSON><PERSON>", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 472, "codigoAtividade": null, "nome": "Dragon", "descricao": "For Time\n5k Run\n4 minutes to find 4 rep max Deadlift\n5k Run\n4 minutes to find 4 rep max Push Jerk", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 473, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "AMRAP in 19 minutes\n3 Muscle-Ups\nShuttle Sprint (5, 10, 15 yards)\n6 Burpee Box Jump Overs (20 in)\nOn the burpees, jump over the box without touching it.", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 474, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "10 Rounds for Time\n30 Double-Unders\n15 Pull-Ups\n30 Air Squats\n100 meter Sprint\n2 minute Rest", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 475, "codigoAtividade": null, "nome": "Erin", "descricao": "5 Rounds For Time\n15 Dumbbell Split Cleans (40/30 lb)\n21 Pull-Ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 476, "codigoAtividade": null, "nome": "<PERSON><PERSON><PERSON>", "descricao": "AMRAP in 25 minutes\n8 Handstand Push-Ups\n8 Box Jump (30/24 in)\n1 Rope Climb (15 ft)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 477, "codigoAtividade": null, "nome": "<PERSON><PERSON><PERSON>", "descricao": "2-4-6-8-10-12-14-16 Reps For Time\n100 meter Shuttle Sprint\nDumbbell Clusters (65/45 lb)\nStart with 2 shuttle sprints and 2 dumbbell “clusters” (squat clean into a thruster), then do 4 of each, then 6, etc. until the round of 16 is completed.", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 478, "codigoAtividade": null, "nome": "Foo", "descricao": "AMRAP in 20 minutes\n7 Chest-To-Bar Pull-Ups\n77 Double-Unders\n2 Squat Clean Thrusters (170/125 lb)\n28 Sit-Ups\nBuy in:\n13 Bench Presses (170/125 lb)\nAfter the clock starts, before beginning the AMRAP portion of the workout, athlete must complete 13 Bench Presses. Each round is 114 reps.", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 479, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "3 Rounds For Time\n20 L-Pull-Ups\n30 Toes-to-Bar\n40 Burpees\n800 meter Run\n", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 480, "codigoAtividade": null, "nome": "<PERSON><PERSON><PERSON>", "descricao": "For Time\n100 Burpee Pull-Ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 481, "codigoAtividade": null, "nome": "<PERSON><PERSON><PERSON>", "descricao": "For Time\n1 mile Medicine Ball Run (20/14 lb)\n60 Burpee Pull-Ups\n800 meter Medicine Ball Run (20/14 lb)\n30 Burpee Pull-Ups\n400 meter Medicine Ball Run (20/14 lb)\n15 Burpee Pull-Ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 482, "codigoAtividade": null, "nome": "<PERSON><PERSON><PERSON>", "descricao": "3 Rounds For Time\n75 Air Squats\n25 Ring Handstand Push-Ups\n25 L-Pull-Ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 483, "codigoAtividade": null, "nome": "Gator", "descricao": "8 Rounds For Time\n5 Front Squat (185 lb)\n26 Ring Push-Ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 484, "codigoAtividade": null, "nome": "Gaza", "descricao": "5 Rounds for Time\n35 Kettlebell Swings (1.5/1 pood)\n30 Push-Ups\n25 Pull-Ups\n20 Box Jumps (30/24 in)\n1 mile Run", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 485, "codigoAtividade": null, "nome": "Glen", "descricao": "For Time\n30 Clean-and-Jerks (135/95 lb)\n1 mile Run\n10 Rope Climb (15 ft)\n1 mile Run\n100 Burpees", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 486, "codigoAtividade": null, "nome": "Griff", "descricao": "For Time\n800 meter Run\n400 meter Run (backwards)\n800 meter Run\n400 meter Run (backwards)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 487, "codigoAtividade": null, "nome": "<PERSON><PERSON>", "descricao": "For Time\n1 mile Weighted Run\n50 Push-Ups\n50 Sit-Ups\n1 mile Weighted Run\n50 Push-Ups\n50 Sit-Ups\n1 mile Weighted Run\nWear a weight vest, body armor, loaded pack or whatever is needed to load yourself with 50 lb. for the runs.", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 488, "codigoAtividade": null, "nome": "Hall", "descricao": "5 Rounds for Time\n3 Cleans (225/155 lb)\n200 meter Sprint\n20 Kettlebell Snatches (1.5/1 pood) (10 each arm)\n2 minutes Rest", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 489, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "3 Rounds For Time\n1000 meter Row\n50 Push-Ups\n1000 meter Run\n50 Pull-Ups\n", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 490, "codigoAtividade": null, "nome": "Hammer", "descricao": "5 Rounds For Time\n5 Power Cleans (135/95 lb)\n10 Front Squats (135/95 lb)\n5 Jerks (135/95 lb)\n20 Pull-Ups\n90 seconds Rest", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 491, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "5 Rounds For Time\n30 Kettlebell Swings (2/1.5 pood)\n30 Burpees\n30 GHD Sit-Ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 492, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "AMRAP in 23 minutes\n9 Chest-to-Bar Pull-Ups\n15 Power Cleans (135/95 lb)\n21 Air Squats\n400 meter Run with a Plate (45/35 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 493, "codigoAtividade": null, "nome": "Havana", "descricao": "AMRAP in 25 minutes\n150 Double-Unders\n50 Push-Ups\n15 Power Cleans (185/125 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 494, "codigoAtividade": null, "nome": "<PERSON><PERSON>", "descricao": "3 Rounds For Time\n800 meter Run\n30 Dumbbell Squat Cleans (50/35 lb)\n30 Burpees", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 495, "codigoAtividade": null, "nome": "Hidalgo", "descricao": "For Time\n2 Mile Run\n2 Minutes Rest\n20 Squat Cleans (135/95 lb)\n20 Box Jumps (24/20 in)\n20 Overhead Walking Lunges (45/25 lb plate)\n20 Box Jumps (24/20 in)\n20 Squat Cleans (135/95 lb)\n2 minutes Rest\n2 mile Run\nWear a weight vest (20/14 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 496, "codigoAtividade": null, "nome": "<PERSON><PERSON><PERSON>", "descricao": "For Time\n100 calorie Row\n75 Thrusters (45/35 lb barbell)\n50 Pull-Ups\n75 Wall Ball Shots (20/14 lb)\n100 calorie Row\nWith a weight vest (20/14 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 497, "codigoAtividade": null, "nome": "<PERSON><PERSON><PERSON>", "descricao": "10 Rounds For Time\n5 Thrusters (115/85 lb)\n10 Pull-Ups\n100 meter Sprint\n1 minute Rest", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 498, "codigoAtividade": null, "nome": "<PERSON><PERSON><PERSON>", "descricao": "30 Rounds For Time\n5 Wall Balls (20/14 lb)\n3 Handstand Push-Ups\n1 Power Clean (225/155 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 499, "codigoAtividade": null, "nome": "Hollywood", "descricao": "For Time\n2 km Run\n22 Wall Ball Shots (30/20 lb)\n22 Muscle-Ups\n22 Wall Ball Shots (30/20 lb)\n22 Power Cleans (185/135 lb)\n22 Wall Ball Shots (30/20 lb)\n2 km Run", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 500, "codigoAtividade": null, "nome": "<PERSON><PERSON><PERSON>", "descricao": "AMRAP in 45 minutes\n800 meter Run\n80 Air Squats\n8 Muscle-Ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 501, "codigoAtividade": null, "nome": "<PERSON>cG<PERSON>", "descricao": "AMRAP in 30 minutes\n5 Deadlifts (275/185 lb)\n13 Push-Ups\n9 Box Jumps (24/20 in)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 502, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "9 Rounds for Time (with a Partner)\n9 Bar Muscle-Ups\n11 Clean-and-Jerks (155/115 lb)\n50 yard Buddy <PERSON><PERSON>\nShare the work with your partner however you choose with only one person working at a time.", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 503, "codigoAtividade": null, "nome": "Miron", "descricao": "5 Rounds for Time:\n800 meter Run\n23 Back Squats (¾ Body Weight)\n13 Deadlifts (1 ½ Body Weight)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 504, "codigoAtividade": null, "nome": "<PERSON><PERSON>", "descricao": "For Time\n1 Squat Clean (185/135 lb)\n10 Parallette Handstand Push-Ups (6\" Deficit)\n2 Squat Cleans (185/135 lb)\n9 Parallette Handstand Push-Ups (6\" Deficit)\n3 Squat Cleans (185/135 lb)\n8 Parallette Handstand Push-Ups (6\" Deficit)\n4 Squat Cleans (185/135 lb)\n7 Parallette Handstand Push-Ups (6\" Deficit)\n5 Squat Cleans (185/135 lb)\n6 Parallette Handstand Push-Ups (6\" Deficit)\n6 Squat Cleans (185/135 lb)\n5 Parallette Handstand Push-Ups (6\" Deficit)\n7 Squat Cleans (185/135 lb)\n4 Parallette Handstand Push-Ups (6\" Deficit)\n8 Squat Cleans (185/135 lb)\n3 Parallette Handstand Push-Ups (6\" Deficit)\n9 Squat Cleans (185/135 lb)\n2 Parallette Handstand Push-Ups (6\" Deficit)\n10 Squat Cleans (185/135 lb)\n1 Parallette Handstand Push-Up (6\" Deficit)\n", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 505, "codigoAtividade": null, "nome": "JBO", "descricao": "AMRAP in 28 minutes\n9 Overhead Squats (115/75 lb)\n1 Legless Rope Climb (15 ft rope, from seated position)\n12 Bench Presses (115/75 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 506, "codigoAtividade": null, "nome": "JT", "descricao": "21-15-9 Reps For Time\nHandstand Push-Ups\nRing Dips\nPush-Ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 507, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "AMRAP in 20 minutes\n10 Push Presses (115/85 lb)\n10 Kettlebell Swings (1.5/1 pood)\n10 Box Jumps (24/20 in)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 508, "codigoAtividade": null, "nome": "JAG 28", "descricao": "For Time\n800 meter Run\n28 Kettlebell Swings (2/1.5 pood)\n28 Strict Pull-Ups\n28 Kettlebell Clean-and-Jerk (2 x 2/1.5 pood)\n28 Strict Pull-Ups\n800 meter Run\nUse a single kettlebell for the swings; two kettlebells for the clean-and-jerks.", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 509, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "4 Rounds For Time\n800 meter Run\n40 Pull-Ups\n70 Push-Ups\n", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 510, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "For Time\n100 Air Squats\n5 Muscle-Ups\n75 Air Squats\n10 Muscle-Ups\n50 Air Squats\n15 Muscle-Ups\n25 Air Squats\n20 Muscle-Ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 511, "codigoAtividade": null, "nome": "<PERSON><PERSON><PERSON>", "descricao": "AMRAP in 26 minutes\n10 Pull-Ups\n15 Kettlebell Swings (1.5/1 Pood)\n20 Box Jumps (24/20 in)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 512, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "AMRAP in 20 minutes\n20 Overhead Squats (45/35 lb bar)\n20 Back Squats (45/35 lb bar)\n400 meter Run", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 513, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "For Time\n1 mile Run\n2,000 meter Row\n1 mile Run", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 514, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "AMRAP in 20 minutes\n9 Deadlifts (245/165 lb)\n8 Muscle-Ups\n9 Squat Clean (155/105 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 515, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "For Time\n30 GHD Sit-Ups\n15 Squat Cleans (155/105 lb)\n24 GHD Sit-Ups\n12 Squat Cleans (155/105 lb)\n18 GHD Sit-Ups\n9 Squat Cleans (155/105 lb)\n12 GHD Sit-Ups\n6 Squat Cleans (155/105 lb)\n6 GHD Sit-Ups\n3 Squat Cleans (155/105 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 516, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "For Time\n21 Overhead Squats (95/65 lb)\n42 Pull-Ups\n15 Overhead Squats (95/65 lb)\n30 Pull-Ups\n9 Overhead Squats (95/65 lb)\n18 Pull-Ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 517, "codigoAtividade": null, "nome": "<PERSON><PERSON>", "descricao": "3 Rounds for Time\n21 <PERSON><PERSON><PERSON> Squat Snatches, Right Arm (40/25 lb)\n21 L Pull-Ups\n21 Dumbbell Squat Snatches, Left Arm (40/25 lb)\n21 L Pull-Ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 518, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "For Time\n1 mile Run\nThen, 3 Rounds of:\n30 Burpees\n4 Power Cleans (155/105 lb)\n6 Front Squats (155/105 lb)\nThen, 1 mile Run\nWear a Weight Vest (20/14 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 519, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "30-20-10 Reps for Time\nBack <PERSON> (bodyweight)\n<PERSON><PERSON> (bodyweight)\nStrict Pull-Ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 520, "codigoAtividade": null, "nome": "<PERSON>v", "descricao": "AMRAP (with a Partner) in 26 minutes\n6 Deadlifts (315/205 lb), each\n9 Bar-Facing Burpees, synchronized\n9 Bar Muscle-Ups, each\n55 ft Partner <PERSON><PERSON> (315/205 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 521, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "3 Rounds for Time\n32 Deadlifts (185/135 lbs)\n32 Hanging <PERSON> Touches (alternating arms)\n800m Running Farmer <PERSON> (15 lb dumbbells)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 522, "codigoAtividade": null, "nome": "Klept<PERSON>", "descricao": "4 Rounds For Time\n27 Box Jumps (24/20 in)\n20 Burpees\n11 Squat Cleans (145/100 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 523, "codigoAtividade": null, "nome": "Kutschbach", "descricao": "7 Rounds For Time\n11 Back Squats (185/135 lb)\n10 Jerks (135/95 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 524, "codigoAtividade": null, "nome": "<PERSON><PERSON><PERSON>", "descricao": "AMRAP in 20 minutes\n5 Parallette Handstand Push-Ups (6\" Deficit)\n10 Toes Through Rings\n15 Medicine Ball Cleans (20/14 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 525, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "5 Rounds For Time\n400 meter Run\n1 Deadlift (345/225 lb)\n3 Squat Cleans (185/135 lb)\n5 Push Jerks (185/135 lb)\n3 Muscle-Ups\n1 Rope Climb (15 ft)\n", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 526, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "For Time\n800 meter Run (with 45 lb Plate)\n100 Toes-to-Bars\n50 Front Squats (155/105 lb)\n10 Rope Climbs (15 ft)\n800 meter Run (with 45 lb Plate)\nPartition the toes-to-bars, front squats and rope climbs as needed. Start and finish with the run.", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 527, "codigoAtividade": null, "nome": "<PERSON><PERSON>", "descricao": "6 Rounds For Time\n24 Air Squats\n24 Push-Ups\n24 Walking Lunges\n400 meter Run", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 528, "codigoAtividade": null, "nome": "Luce", "descricao": "3 Rounds For Time\n1000 meter Run\n10 Muscle-Ups\n100 Air Squats\nWear a weight vest (20/14 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 529, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "For Time\n400 meter Run\n15 Clean-and-Jerks (155/105 lb)\n400 meter Run\n30 Toes-to-Bars\n400 meter Run\n45 Wall Ball Shots (20/14 lb)\n400 meter Run\n45 Kettlebell Swings (1.5/1 pood)\n400 meter Run\n30 Ring Dips\n400 meter Run\n15 Weighted Lunge Steps (155/105 lb)\n400 meter Run", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 530, "codigoAtividade": null, "nome": "Lumberjack 20", "descricao": "For Time\n20 Deadlifts (275/185 lb)\n400 meter Run\n20 Kettlebell Swings (2/1.5 pood)\n400 meter Run\n20 Overhead Squats (115/75 lb)\n400 meter Run\n20 Burpees\n400 meter Run\n20 Chest-to-Bar Pull-Ups\n400 meter Run\n20 Box Jumps (24/20 in)\n400 meter Run\n20 Dumbbell Squat Cleans (45/35 lb)\n400 meter Run", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 531, "codigoAtividade": null, "nome": "Manion", "descricao": "7 Rounds For Time\n400 meter Run\n29 Back Squats (135/95 lb)\nTake the back squats from a rack.", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 532, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "5 Rounds for Rep<PERSON> in 50 minutes\n3 minutes of Max Rope Climbs\n2 minutes of Max Air Squats\n2 minutes of Max Push-Ups\n3 minutes to Run 400 meters\nWear a weight vest (20/14 lb)\nAfter the run, rest for the remainder of the 3 minutes before beginning the next round.", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 533, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "3 Rounds for Time\n21 Pull-Ups\n15 Handstand Push-Ups\n9 Thrusters (135/95 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 534, "codigoAtividade": null, "nome": "<PERSON><PERSON>", "descricao": "AMRAP in 20 minutes\n1 Deadlift (405/285 lb)\n10 Toes-to-Bar\n15 Bar Facing Burpees", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 535, "codigoAtividade": null, "nome": "<PERSON> 16", "descricao": "For Time\n16 Deadlifts (275 lb)\n16 Hang Power Cleans (185 lb)\n16 Push Presses (135 lb)\n800 meter Run\n16 Deadlifts (275 lb)\n16 Hang Power Cleans (185 lb)\n16 Push Presses (135 lb)\n800 meter Run\n16 Deadlifts (275 lb)\n16 Hang Power Cleans (185 lb)\n16 Push Presses (135 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 536, "codigoAtividade": null, "nome": "<PERSON><PERSON><PERSON>", "descricao": "4 Rounds for Time\n800 meter Run\n49 Push-Ups\n49 Sit-Ups\n49 Air Squats", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 537, "codigoAtividade": null, "nome": "<PERSON><PERSON>", "descricao": "3 Rounds For TIme\n9 Muscle-Ups\n15 Burpee Pull-Ups\n21 Pull-Ups\n800 meter Run\nWear a weight vest (20/14 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 538, "codigoAtividade": null, "nome": "MEADOWS", "descricao": "For Time\n20 Muscle-Ups\n25 Lowers from In<PERSON> Hang on the Rings\n30 Ring Handstand Push-Ups\n35 Ring Rows\n40 Ring Push-Ups\nFor the ‘Lowers’ on the ring, control the movement, moving slowly, with straight body and arms.", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 539, "codigoAtividade": null, "nome": "<PERSON><PERSON>", "descricao": "5 Rounds for Time\n50 Box Step-Ups (45/35 lb barbell, 20\" box)\n15 Cleans (135/95 lb)\n50 Box Step-Ups (45/35 lb barbell, 20\" box)\n10 Snatches (135/95 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 540, "codigoAtividade": null, "nome": "Moon", "descricao": "7 Rounds For Time\n10 Hang Split Snatch, Right Arm (40/30 lb)\n1 Rope Climb (15 ft)\n10 Hang Split Snatch, Left Arm (40/30 lb)\n1 Rope Climb (15 ft)\nAlternate feet in the split snatch sets", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 541, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "AMRAP in 20 minutes\n1 Rope Climb (15 ft)\n400 meter Run\nMax Reps Handstand Push-Ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 542, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "50-40-30-20-10 Reps For Time\nWall Ball Shots (20/14 lb)\nBox Jumps (24/20 in)\nKettlebell Swings (1.5/1 pood)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 543, "codigoAtividade": null, "nome": "Mr. <PERSON>", "descricao": "Five Rounds for Time\n400 Meter Run\n30 GHD Sit-Ups\n15 Deadifts (250/165 lbs)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 544, "codigoAtividade": null, "nome": "Murph", "descricao": "For Time\n1 mile Run\n100 Pull-Ups\n200 Push-Ups\n300 Air Squats\n1 mile Run\nAll with a Weight Vest (20/14 lb)\nPartition the Pull-Ups, Push-Ups, and Squats as needed. Start and finish with a mile run.", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 545, "codigoAtividade": null, "nome": "Nate", "descricao": "AMRAP in 20 minutes\n2 Muscle-Ups\n4 Handstand Push-Ups\n8 Kettlebell Swings (2/1.5 pood)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 546, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "7 Rounds For Time\n11 Back Squats (bodyweight)\n1,000 meter Row", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 547, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "12 Rounds For Time\n10 Dumbbell Hang Squat Clean (45/35 lb)\n6 Handstand Push-Ups on Dumbbells", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 548, "codigoAtividade": null, "nome": "<PERSON><PERSON>", "descricao": "10 Rounds For Time\n200 meter Farmers Carry (55/35 lb)\n10 Weighted Pull-Ups (35/25 lb)\n20 Dumbbell Power Snatches (55/35 lb), alternating", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 549, "codigoAtividade": null, "nome": "Nukes", "descricao": "AMRAP in 30 minutes\nFrom 0:00-8:00\n1 mile Run\nMax Deadlifts (315/205 lb)\nFrom 8:00-18:00\n1 mile Run\nMax Power Cleans (225/155 lb)\nFrom 18:00-30:00\n1 mile Run\nMax Overhead Squats (135/95 lb)\nPerform all sections with a running clock (no rest between sections). Score is total reps from each of the three barbell movements.", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 550, "codigoAtividade": null, "nome": "<PERSON><PERSON><PERSON>", "descricao": "For Time\n10 Handstand Push-Ups\n15 Deadlifts (250/175 lb)\n25 Box Jumps (30/24 in)\n50 Pull-Ups\n100 Wall Ball Shots (20/14 lb)\n200 Double-Unders\n400 meter Run (with 45/35 lb plate)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 551, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "For Time\n10 Thrusters (95/65 lb)\n15 Bar-Facing Burpees\n20 Thrusters (95/65 lb)\n25 Bar-Facing Burpees\n30 Thrusters (95/65 lb)\n35 Bar-Facing Burpees", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 552, "codigoAtividade": null, "nome": "<PERSON><PERSON>", "descricao": "7 Rounds For Time\n11 Deficit Handstand Push-Ups (6/4 in)\n1,000 meter Run", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 553, "codigoAtividade": null, "nome": "PK", "descricao": "5 Rounds for Time\n10 Back Squats (225/155 lbs)\n10 Deadlifts (275/185 lbs)\n400 meter Sprint\n2 minutes Rest", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 554, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "6 Rounds for Time\n25 Pull-Ups\n50 ft Front-Rack Lunges (75/55 lb)\n25 Push-Ups\n50 ft Front-Rack Lunges (75/55 lb)\nAll while wearing a Weight Vest (20/14 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 555, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "5 Rounds For Time\n50 Double-<PERSON><PERSON>\n35 Knees-to-El<PERSON>s\n20 yard Overhead Walk (185/135 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 556, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "7 Rounds for Time\n100 meter Sprint\n19 Kettlebell Swings (2/1.5 pood)\n10 Burpee Box Jumps (24/20 in)\n3 minutes Rest\nTime each round. Score is total time for all seven rounds, including rest.", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 557, "codigoAtividade": null, "nome": "<PERSON><PERSON><PERSON>", "descricao": "3 Rounds For Time\n5 Front Squats (165/105 lb)\n18 Pull-Ups\n5 Deadlifts (225/155 lb)\n18 Toes-to-Bar\n5 Push Jerks (165/105 lb)\n18 Hand-Release Push-Ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 558, "codigoAtividade": null, "nome": "Pike", "descricao": "\n5 Rounds for Time\n20 Thrusters (75/55 lb)\n10 Strict Ring Dips\n20 Push-Ups\n10 Strict Handstand Push-Ups\n50 meter Bear Crawl", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 559, "codigoAtividade": null, "nome": "RJ", "descricao": "5 Rounds For Time\n800 meter Run\n5 Rope Climb (15 ft)\n50 Push-Ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 560, "codigoAtividade": null, "nome": "<PERSON><PERSON><PERSON>", "descricao": "AMRAP in 12 minutes\n12 Box Jumps (24 in/20 in)\n6 Thrusters (95 lbs/65 lb)\n6 Bar-Facing <PERSON><PERSON><PERSON>es", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 561, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "4 Rounds For Time\n8 Deadlifts (250/175 lb)\n16 Burpees\n3 Rope Climbs (15 ft)\n600 meter Run", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 562, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "For Time\n75 Power Snatches (75/55 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 563, "codigoAtividade": null, "nome": "<PERSON><PERSON>", "descricao": "AMRAP in 20 minutes\n6 Deadlifts (225/155 lb)\n7 Burpee Pull-Ups\n10 Kettlebell Swings (2/1.5 pood)\n200 meter Run", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 564, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "7 Rounds for Time\n400 meter Run\n21 Walking Lunges\n15 Pull-Ups\n9 Bur<PERSON><PERSON>\nWear a Weight Vest (20/14 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 565, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "For Time\n13 Squat Snatches (155/105 lb)\nThen, 10 Rounds of:\n10 Pull-Ups\n100 meter Sprint\nThen, 13 Squat Cleans (155/105 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 566, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "AMRAP in 20 minutes\n10 Pull-Ups\n5 Dumbbell Deadlifts (75/55 lb)\n8 Push-Press (135/95 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 567, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "For Time\n1.5 mile Run\n150 Burpees\n1.5 mile Run\nWear a weight vest (20/14 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 568, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "AMRAP in 25 minutes\n8 Freestanding Handstand Push-Ups\n1 L-Sit Rope Climb (15 foot)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 569, "codigoAtividade": null, "nome": "Rocket", "descricao": "AMRAP in 30 minutes\n50 meter Swim\n10 Push-Ups\n15 Air Squats", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 570, "codigoAtividade": null, "nome": "<PERSON><PERSON>", "descricao": "4 Rounds For Time\n200 meter Run\n11 Thrusters (135/95 lb)\n200 meter Run\n11 Push Presses (135/95 lb)\n200 meter Run\n11 Bench Presses (135/95 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 571, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "5 Rounds For Time\n15 Deadlift (225/155 lb)\n20 Box Jumps (24/20 in)\n25 Pull-Ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 572, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "5 Rounds For Time\n7 Muscle-Ups\n21 Target Burpees\nEach <PERSON><PERSON><PERSON><PERSON> terminates with a jump to touch a target 1 foot above max standing reach.", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 573, "codigoAtividade": null, "nome": "Santiago", "descricao": "7 Rounds For Time\n18 <PERSON>mbbell Hang Squat Clean (35/25 lb)\n18 Pull-Ups\n10 Power Clean (135/95 lb)\n10 Handstand Push-Ups\n", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 574, "codigoAtividade": null, "nome": "Santora", "descricao": "3 Rounds For Max Reps in 17 minutes\n1 minute Squat Cleans (155/105 lb)\n1 minute Shuttle Sprints (20 ft forward, 20 ft backwards)\n1 minute Deadlifts (245/165 lb)\n1 minute B<PERSON>pees\n1 minute Jerks (155/105 lb)\n1 minute Rest between rounds\nFor the shuttle sprints, count 20 ft forward and 20 ft backwards as one repetition.", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 575, "codigoAtividade": null, "nome": "<PERSON>ooter", "descricao": "With a Running Clock in 35 minutes\nFirst, AMRAP in 30 minutes (with a Partner)\n30 Double-Unders\n15 Pull-Ups\n15 Push-Ups\n100 meter Sprint\nPartners alternate rounds\nThen, 5 minutes to find a 1-rep-max Partner Deadlift\nFor the AMRAP, one partner work while the other rests, switching after each full round is completed.", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 576, "codigoAtividade": null, "nome": "<PERSON><PERSON>", "descricao": "AMRAP in 11 minutes\n5 Deadlifts (315/205 lb)\n18 Wall Ball Shots (20/14 lb)\n17 Bar Over Burpees", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 577, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "10 Rounds For Time\n11 Chest-to-Bar Pull-Ups\n22 Front Squat (75/55 lb", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 578, "codigoAtividade": null, "nome": "<PERSON><PERSON><PERSON>", "descricao": "For Time\n50 Strict Pull-Ups\n100 Hand-Release Push-Ups\n5k Run\nWear a weight vest (20/14 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 579, "codigoAtividade": null, "nome": "Sham", "descricao": "7 Rounds For Time\n11 Deadlifts (bodyweight)\n100 meter Sprint", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 580, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "For Time\n5 mile Run\nAfter each 5 minute run interval:\n50 Air Squats\n50 Push-Ups\nRun in 5-minute intervals, stopping after each to perform 50 Air Squats and 50 Push-Ups before beginning the next 5-minute run interval.\n", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 581, "codigoAtividade": null, "nome": "Ship", "descricao": "9 Rounds For Time\n7 Squat Clean (185/135 lb)\n8 Burpee Box Jump (36/30 in)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 582, "codigoAtividade": null, "nome": "<PERSON><PERSON>", "descricao": "AMRAP in 20 minutes\n1 Rope Climb (15 ft)\n5 Burpees\n200 meter Run\nWear a weight vest (20/14 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 583, "codigoAtividade": null, "nome": "Small", "descricao": "3 Rounds For Time\n1000 meter Row\n50 Burpees\n50 Box Jumps (24/20 in)\n800 meter Run", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 584, "codigoAtividade": null, "nome": "<PERSON><PERSON><PERSON>", "descricao": "For Time\n6 km Run\n60 Burpee Pull-Ups\nWear a weight vest (30/20 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 585, "codigoAtividade": null, "nome": "Spehar", "descricao": "For Time\n100 Thrusters (135/95 lb)\n100 Chest-to-Bar Pull-Ups\n6 mile Run\nPartition the thrusters, pull-ups and run as needed.", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 586, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "30-25-20-15-10-5 Reps, For Time\nGHD Sit-Ups\nBack Extensions\nKnees-to-Elbows\nRomanian Deadlifts (95/65 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 587, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "8 Rounds For Time\n600 meter Run\n11 Weighted Pull-Ups (1.5/1 pood)\n11 Walking Lunges (1.5/1 pood)\n11 Thrusters (1.5/1 pood)\n", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 588, "codigoAtividade": null, "nome": "T", "descricao": "5 rounds for time of:\n100-meter sprint\n10 squat clean thrusters\n15 kettlebell swings\n100-meter sprint\n\nRest 2 minutes\n\nMen: 115 lb, and e pood\nWomen: 75 lb, and 15 pood\n", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 589, "codigoAtividade": null, "nome": "T<PERSON><PERSON>.", "descricao": "For Time\n10 Bench Presses (185/135 lb)\n10 Strict Pull-Ups\nMax Thrusters (135/95 lb)\nRepeat until you have completed 100 Thrusters.", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 590, "codigoAtividade": null, "nome": "T.U.P", "descricao": "15-12-9-6-3 Reps For Time\nPower Cleans (135/95 lb)\nPull-Ups\nFront Squats (135/95 lb)\nPull-Up", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 591, "codigoAtividade": null, "nome": "TK", "descricao": "AMRAP in 20 minutes\n8 Strict Pull-Ups\n8 Box Jumps (36/30 in)\n12 Kettlebell Swings (2/1.5 pood)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 592, "codigoAtividade": null, "nome": "<PERSON><PERSON>", "descricao": "For Time\n800 meter Single-Arm Barbell Farmers Carry (45/35 lb)\n31 Toes-to-Bars\n31 Push-Ups\n31 Front Squats (95/65 lb)\n400 meter Single-Arm Barbell Farmers Carry (95/65 lb)\n31 Toes-to-Bars\n31 Push-Ups\n31 Hang Power Cleans (135/95 lb)\n200 meter Single-Arm Barbell Farmers Carry (135/95 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 593, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "For Time\n1 mile Run\n100 Push-Ups\n100 meter Bear Crawl\n1 mile Run\n100 meter Bear Crawl\n100 Push-Ups\n1 mile Run", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 594, "codigoAtividade": null, "nome": "The Lyon", "descricao": "5 Rounds for Time\n7 Squat Cleans (165/115 lb)\n7 Shoulder-to-Overheads (165/115 lb)\n7 Burpee Chest-to-Bar Pull-Ups\n2 minutes Rest between rounds\nIdeally, use a pull-up bar that is 6 inches above your max reach when standing.", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 595, "codigoAtividade": null, "nome": "The Seven", "descricao": "7 Rounds For Time\n7 Handstand Push-Ups\n7 Thrusters (135/95 lb)\n7 Knees-to-Elbows\n7 Deadlifts (245/165 lb)\n7 Burpees\n7 Kettlebell Swings (2/1.5 pood)\n7 Pull-Ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 596, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "10 Rounds For Time\n1 Rope Climb (15 ft) (from seated)\n29 Back Squats (95/65 lb)\n10 meter Barbell <PERSON> (135/95 lb)\nBegin the Rope Climbs seated on the floor", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 597, "codigoAtividade": null, "nome": "Tiff", "descricao": "With a running clock in 25 minutes\n1.5 mile Run\nThen AMRAP in remaining time:\n11 Chest-to-Bar Pull-Ups\n7 Hang Squat Cleans (155/105 lb)\n7 Push Presses (155/105 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 598, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "AMRAP in 25 minutes\n7 Muscle-Ups\n11 Thrusters (155/105 lb)\n14 Toes-to-Bar", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 599, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "For Time\n21 Thrusters (115/75 lb)\n12 Rope Climbs (15 ft)\n15 Thrusters (115/75 lb)\n9 Rope Climbs (15 ft)\n9 Thrusters (115/75 lb)\n6 Rope Climbs (15 ft)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 600, "codigoAtividade": null, "nome": "<PERSON><PERSON>", "descricao": "4 Rounds For Time\n200 meter Swim\n23 <PERSON><PERSON><PERSON> Squat Cleans (2 x 40/30 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 601, "codigoAtividade": null, "nome": "<PERSON><PERSON><PERSON>", "descricao": "8 Rounds For Time\n200 meter Run\n11 Dumbbell Burpee Deadlifts (60/40 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 602, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "5 Rounds For Time\n7 Muscle-Ups\n21 Sumo-Deadlift High-Pulls (95/65 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 603, "codigoAtividade": null, "nome": "Viola 1", "descricao": "AMRAP in 25 minutes\n3, 6, 9, 12, 15, 18 Reps and so on\nThrusters (95/65 lbs)\nPull-ups\nOver-the-Bar Burpees", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 604, "codigoAtividade": null, "nome": "Viola 2", "descricao": "AMRAP in 20 minutes\n400 meter Run\n11 Power Snatches (95/65 lb)\n17 Pull-Ups\n13 Power Cleans (95/65 lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 605, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "4 Rounds For Time\n22 B<PERSON><PERSON><PERSON> Pull-Ups\n22 Back Squats (185/135 lb)\n200 meter Run (45/35 lb plate overhead)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 606, "codigoAtividade": null, "nome": "War Frank", "descricao": "3 Rounds For Time\n25 Muscle-Ups\n100 Air Squats\n35 GHD Sit-Ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 607, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "4 Round For Time\n10 L-Pull-Ups\n15 Push-Ups\n15 Chest-to-Bar Pull-Ups\n15 Push-Ups\n20 Pull-Ups\n15 Push-Ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 608, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "For Time\n800 meter Run (with 25/15 lb Plate)\nThen:\n14 Rounds of:\n5 Strict Pull-Ups\n4 Burpee Box Jumps (24/20 in)\n3 Cleans (185/135 lb)\nThen:\n800 meter Run (with 25/15 lb Plate)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 609, "codigoAtividade": null, "nome": "Weston", "descricao": "5 Rounds For Time\n1000 meter Row\n200 meter Farmer <PERSON> (45 lb dumbbells)\n50 meter Waiter Walk, Right Arm (45 lb dumbbell)\n50 meter Waiter Walk, Left Arm (45 lb dumbbell)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 610, "codigoAtividade": null, "nome": "White", "descricao": "5 Rounds For Time\n3 Rope Climbs (15 ft)\n10 Toes-to-Bars\n21 Overhead Walking Lunges (45/35 lb plate)\n400 meter Run", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 611, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "3 Rounds For TIme\n800 meter Run\n5 Front Squats (225/155 lb)\n200 meter Run\n11 Chest-to-Bar Pull-Ups\n400 meter Run\n12 Kettlebell Swings (2/1.5 pood)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 612, "codigoAtividade": null, "nome": "<PERSON><PERSON><PERSON>", "descricao": "6 Rounds For Time\n50 Air Squats\n25 Ring Dips", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 613, "codigoAtividade": null, "nome": "<PERSON><PERSON><PERSON>", "descricao": "7 Rounds For Time\n15 Kettlebell Swings (1.5/1 pood)\n15 Power Cleans (95/65 lb)\n15 Box Jumps (24/20 in)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 614, "codigoAtividade": null, "nome": "<PERSON><PERSON><PERSON><PERSON>", "descricao": "3 Rounds, Each for Time\n4 Jerks (185/135 lb)\n5 Front Squats (185/135 lb)\n6 Power Cleans (185/135 lb)\n40 Pull-Ups\n50 Push-Ups\n60 Sit-Ups\n3 minutes Rest between rounds", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 615, "codigoAtividade": null, "nome": "<PERSON>", "descricao": "5 Rounds For Time\n400 meter Run\n10 Burpee Box Jumps (24/20 in)\n10 Sumo-Deadlift High-pull (95/65 lbs)\n10 Thruster (95/65 lbs)\n1 minute Rest", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 616, "codigoAtividade": null, "nome": "<PERSON><PERSON>", "descricao": "For Time\n25 Pull-Ups\n10 Muscle-Ups\n1.5 mile Run\n10 Muscle-Ups\n25 Pull-Ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 617, "codigoAtividade": null, "nome": "Zembiec", "descricao": "5 Rounds for Time\n11 Back Squats (185/135 lb)\n7 Burpee Pull-Ups (Strict)\n400 meter Run\nDuring each burpee pull-up perform a strict push-up, jump to a bar that is ideally 12 inches above your max standing reach, and perform a strict pull-up.", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 618, "codigoAtividade": null, "nome": "Zeus", "descricao": "3 Rounds For Time\n30 Wall Ball Shots (20/14 lb)\n30 Sumo Deadlift High-Pull (75/55 lb)\n30 Box Jump (20 in)\n30 Push Presses (75/55 lb)\n30 calorie Row\n30 Push-Ups\n10 Back Squat (Bodyweight)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 619, "codigoAtividade": null, "nome": "<PERSON><PERSON><PERSON>", "descricao": "AMRAP in 25 minutes\n11 Chest-to-Bar Pull-Ups\n2 Deadlifts (315/205 lb)\n10 Handstand Push-Ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 620, "codigoAtividade": null, "nome": "Open 13.1", "descricao": "AMRAP in 17 minutes\n40 Burpees\n30 Snatches (75/45 lb)\n30 Burpees\n30 Snatches (135/75 lb)\n20 Burpees\n30 Snatches (165/100 lb)\n10 Burpees\nMax Snatches (210/120 lb)\nIn the case of a tie, tie breaker is the time it takes to complete the last complete set of Snatches.lb)", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 621, "codigoAtividade": null, "nome": "Open 13.3", "descricao": "AMRAP in 12 minutes\n150 Wall Balls (20/14 lb)\n90 Double-Unders\n30 Muscle-Ups\nIf time permits, after finishing the muscle-ups, start over from wall balls.", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 622, "codigoAtividade": null, "nome": "Open 15.4", "descricao": "AMRAP in 8 minutes\n3 Handstand Push-Ups\n3 Cleans (185/125 lb)\n6 Handstand Push-Ups\n3 Cleans (185/125 lb)\n9 Handstand Push-Ups\n3 Cleans (185/125 lb)\n12 Handstand Push-Ups\n6 Cleans (185/125 lb)\n15 Handstand Push-Ups\n6 Cleans (185/125 lb)\n18 Handstand Push-Ups\n6 Cleans (185/125 lb)\n21 Handstand Push-Ups\n9 Cleans (185/125 lb)\nEtc., following same pattern until time is up", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 623, "codigoAtividade": null, "nome": "Open 16.2", "descricao": "AMRAP in 20 minutes\nContinue until 4 minutes:\n25 Toes-to-Bars\n50 Double-Unders\n15 Squat Cleans (135/85 lb)\nIf completed before 4 minutes, continue until 8 minutes:\n25 Toes-to-Bars\n50 Double-Unders\n13 Squat Cleans (185/115 lb)\nIf completed before 8 minutes, continue until 12 minutes:\n25 Toes-to-Bars\n50 Double-Unders\n11 Squat Cleans (225/145 lb)\nIf completed before 12 minutes, continue until 16 minutes:\n25 Toes-to-Bars\n50 Double-Unders\n9 Squat Cleans (275/175 lb)\nIf completed before 16 minutes, continue until 20 minutes:\n25 Toes-to-Bars\n50 Double-Unders\n7 Squat Cleans (315/205 lb)\nAthlete’s score will be the number of reps completed. Tiebreak is the elapsed time at which the athlete completed their last set of double-unders.", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 624, "codigoAtividade": null, "nome": "Open 16.4", "descricao": "AMRAP in 13 minutes\n55 Deadlifts (225/155 lb)\n55 Wall-Balls (20/14 lb)\n55 calorie Row\n55 Handstand Push-Ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 625, "codigoAtividade": null, "nome": "Open 17.1", "descricao": "For Time\n10 Dumbbell Snatches (50/35 lb)\n15 Burpee Box Jump Overs\n20 Dumbbell Snatches (50/35 lb)\n15 Burpee Box Jump Overs\n30 Dumbbell Snatches (50/35 lb)\n15 Burpee Box Jump Overs\n40 Dumbbell Snatches (50/35 lb)\n15 Burpee Box Jump Overs\n50 Dumbbell Snatches (50/35 lb)\n15 Burpee Box Jump Overs\nTime Cap: 20 minutes\nAthlete must alternate hands for each dumbbell snatch and face the box for the Burpee Box Jump Overs.", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 626, "codigoAtividade": null, "nome": "Open 17.2", "descricao": "AMRAP in 12 minutes\n2 Rounds of:\n50 ft Dumbbell Walking Lunges (50/35 lb)\n16 Toe-to-Bars\n8 Dumbbell Power Cleans (50/35 lb)\nThen, 2 Rounds of:\n50 ft Dumbbell Walking Lunges (50/35 lb)\n16 Bar Muscle-Ups\n8 Dumbbell Power Cleans (50/35 lb)\nFor the dumbbell walking lunges, dumbbells must be in front rack position. After 25 ft, athlete will walk back to the starting line.\n\nRight after this workout is announced we live streamed on YouTube two athletes from the NorCal region who gave it a go (video is below). Subscribe on YouTube to be notified when we publish a version with the athletes giving their thoughts on the workout.", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 627, "codigoAtividade": null, "nome": "Open 17.3", "descricao": "For Reps\nPrior to 8 minutes, 3 rounds of:\n6 Chest-to-Bar Pull-Ups\n6 Squat Snatches (95/65 lb)\nThen 3 rounds of:\n7 Chest-to-Bar Pull-Ups\n5 Squat Snatches (135/95 lb)\n* Prior to 12 minutes, 3 rounds of:\n8 Chest-to-Bar Pull-Ups\n4 Squat Snatches (185/135 lb)\n* Prior to 16 minutes, 3 rounds of:\n9 Chest-to-Bar Pull-Ups\n3 Squat Snatches (225/155 lb)\n* Prior to 20 minutes, 3 rounds of:\n10 Chest-to-Bar Pull-Ups\n2 Squat Snatches (245/175 lb)\n* Prior to 24 minutes, 3 rounds of:\n11 Chest-to-Bar Pull-Ups\n1 Squat Snatch (265/185 lb)\n* If all reps are completed, time cap extends by 4 minutes.\n\nThree rounds for the two movements should be done before moving into the next section of the workout. If the athlete completed the first two rounds in the 8-minute time cap, the athlete will be given four more minutes to finish the next round. Same as the next round until the whole workout is completed within the time cap of 24 minutes.", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 628, "codigoAtividade": null, "nome": "Open 17.4", "descricao": "AMRAP in 13 minutes\n55 Deadlifts (225/155 lb)\n55 Wall Ball Shots (20/14 lb, 10/9 ft)\n55 calorie Row\n55 Handstand Push-Ups", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 629, "codigoAtividade": null, "nome": "Open 18.1", "descricao": "AMRAP in 20 minutes\n8 Toes-to-Bars\n10 Dumbbell Hang Clean-and-Jerks (50/35 lb)\n14/12 calorie Row\nScaling: The Open offers a prescribed and scaled version of each workout for any age group. If you are unable to complete the prescribed version, try the scaled version for your age.\n\nVariations:\nRx’d: (Ages 16-54)\nMen use 50-lb. dumbbell\nWomen use 35-lb. dumbbell\n\nScaled: (Ages 16-54)\nMen perform hanging knee-raises, use 35-lb. dumbbell\nWomen perform hanging knee-raises, use 20-lb. dumbbell\n\nTeenagers 14-15:\nBoys use 35-lb. dumbbell\nGirls use 20-lb. dumbbell\n\nScaled Teenagers 14-15:\nBoys perform hanging knee-raises, use 20-lb. dumbbell\nGirls perform hanging knee-raises, use 10-lb. dumbbell\n\nMasters 55+:\nMen use 35-lb. dumbbell\nWomen use 20-lb. dumbbell\n\nScaled Masters 55+:\nMen perform sit-ups, use 20-lb. dumbbell\nWomen perform sit-ups, use 10-lb. dumbbell", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 630, "codigoAtividade": null, "nome": "Games 11.01", "descricao": "For time: \n210 meter Ocean swin\n1,500 meter Soft-sand run\n50 Chest-to-bar pull-ups\n100 Hand-release push-ups\n200 Squats", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 631, "codigoAtividade": null, "nome": "Games 11.05", "descricao": "1 rep max weighted Chest-to-bar pull-up for load\n1 rep max Snatch for load\nJug carry for distance in 60 seconds\nEach athlete has 2 minutes to establish a 1 rep max chest-to-bar pull-tips, 2\nminutes to establish al rep max snatch, and then will have 60 seconds to \ncarry 2 weighted water jugs, as tar as possible.\nAthletes will be ranked in each test with the sum of their 3 rankings,\ndetermining their final score for the event", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 632, "codigoAtividade": null, "nome": "Games 12.04", "descricao": "Sitting in the CHO, athletes will throw as many medicine bals as far as\npossible\nMen will throw 4 pound medicine balls.\nWomen will throw 2 pound medicine balls.\n\nImmediately after the event, athletes will reset together for the Track Triplet Event", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 633, "codigoAtividade": null, "nome": "Games 12.06", "descricao": "Three rounds for time of:\n8 Medicine ball cleans (150 I 80 lbs)\nloo toot Medicine ball carry\n7 Parallette handstand PUSh-Lips\n100 foot Medicine ball carry\n\nAthletes will be ranked by time\n\nTime Cap: 10 minutes", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 634, "codigoAtividade": null, "nome": "Games 12.07", "descricao": "300-yard Shuttle sprint for time:\nRun to the 50-yard ljne and back\n<PERSON> to the 100-yard line and back\nAthletes will be ranked by time.\n\nImmediately after the sprint, athletes will reset together for the Rope-Sled Event", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 635, "codigoAtividade": null, "nome": "Games 12.08", "descricao": "Athletes will begin this event just afta completing the Spnnt Event\nFive rounds for time of:\n20 toot Rope climb, 1 ascent\n20 yard Sled drive\nThe athletes will drive the padded sled loo yards in 20.yard increments. The\nmeos sleds have 90 pounds added.\nAthletes will be ranked by time.\nTime Cap: 13 minutes\n", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 636, "codigoAtividade": null, "nome": "Games 12.10", "descricao": "For time:\n10 Overhead squats (155 /105 lbs)\n10 Box junip overs (24 I 20 box)\n10 Fat bar thrusters, (135/95 lbs)\n10 Por cleans (205 / 125 lbs)\n10 Toes to bar\n10 Burpee muscle-ups\n10 Toes to bar\n10 Power cleans (205 / 125 lbs)\n10 Fat bar thrusters. (135/95 lbs)\n10 Box jump overs (24 / 20 box)\n10 Overhead squats (155/105 lbs)\n\nTime Cap: 15 minutes", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 637, "codigoAtividade": null, "nome": "Games 13.02", "descricao": "For time:\nRow 21,097 meters\nRow 1 will end at the 2,000 meter checkpoint. Athletes will receive points\nbased on their times at this point There is no scheduled rest at the\ncheckpoint as the race continues.\nRow 2 will be scored by the total time to finish the entire 21.091 meters.\nFinishing order at the 2K checkpoint (Row1) will have no bearing on the\nscoring for Row 2.\nTime: 2 hours for Men. 2 hours 10 minutes for Women", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 638, "codigoAtividade": null, "nome": "Games 13.06", "descricao": "Four rounds for time of\nRun 600 meters up and over berm\n25 Overhead squats (140 / 95 lbs)\nTime Cap: 20 minutes", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 639, "codigoAtividade": null, "nome": "Games 13.09 ", "descricao": "21 MB GHD sit-ups\n15 Snatches (165 / 100 lbs)\n09 Wall burpees", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 640, "codigoAtividade": null, "nome": "Games 13.10", "descricao": "The Cinco\nThree rounds of:\n5 Deadlifts (405 I 265 lbs)\n5 weighted One-legged squats, left leg (53 / 35 lb KB)\n5 weighted One-legged squats, right leg (53135 Ii KB)\nThen.\n80 Handstand walk\n\nTime Cap: 7 minutes, The Cinco 2 begins precisely 1 minute afta the time\ncap.", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 641, "codigoAtividade": null, "nome": "Games 13.11", "descricao": "The Cinco 2\nThree rounds of:\n5 Muscle-ups\n5 Delicit handstand push-ups\nThen,\n90 Overhead walking lunge (160 / 100 lb axle bar)\nThis event will begin 1 minute after the end of the time cap for The Cinco 1.\nThere is a 7:00 time cap for this event\nTime Cap: 7:00", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 642, "codigoAtividade": null, "nome": "Games 14.06", "descricao": "For time:\n8 deadlifts (155 / 115 Ib)\n1 cleans (155 / 115 Ib\n6 snatches (155 / 115 Ib)\n8 pull-ups\n1 chest-to-bar pull-ups\n6 bar muscle-ups\n6 deadlifts (155 / 115 Ib )\n5 cleans (155 / 115 Ib)\n4 snatches (155 / 115 Ib)\n6 pull-ups\n5 chest-to-bar pull-ups\n4 bar muscle-ups\n4 deadlifts (155 /115 Ib)\n3 cleans (155 / 115 Ib)\n2 snatches (155 / 115 Ib)\n4 pull-ups\n3 chest-to-bar pull-ups\n2 bar muscle-ups\n\nTime Cap: 7 minutes", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 643, "codigoAtividade": null, "nome": "Games 14.07", "descricao": "For time:\n400-meter run\n18 muscle-ups\n400-meter run\n15 muscle-ups\n400-meter run\n12 muscle-ups\nEach time the athlete breaks a set at muscle-ups they must run a 200.meter\nlap.\nTime Cap:  18 minutes", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 644, "codigoAtividade": null, "nome": "Games 14.09", "descricao": "For time of:\n1 handstand pushups (deficit for men)\n50 foot sled pul\n8 deficit handstand push-ups\n50 - foot sled pull\n9 deficit handstand push-ups\n50-foot sled pull\n10 deficit handstand push-ups\n50-foot sled pull\nEach round the deficit for the strict handstand push-ups increases. No\nkipping.\n\nTime Cap: 11 minutes", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 645, "codigoAtividade": null, "nome": "Games 14.11", "descricao": "For time:\n4 rope climbs\n3 overhead squats (245 / 165 lb)\n\nTime Cap: 4 minutes", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 646, "codigoAtividade": null, "nome": "Games 14.12", "descricao": "For time:\n60 clean and jerks (135 /95 Ib)\n\nTime Cap: 7 minutes\nThe event begins 2 minutes after the time cap of Thick´n Quick", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 647, "codigoAtividade": null, "nome": "Games 15.05", "descricao": "HEAVY DT\n5 rounds for time of:\n12 deadlitts\n9 hang power cleans\n6 push jerks\n\nM 205 lb. F 145 lb.\nAthletes will complete 5 rounds of the barbell complex. advancing forward\nafter each round,\nTime cap: 12 minutes", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 648, "codigoAtividade": null, "nome": "Games 15.11", "descricao": "6 rounds for time of:\n400-meter run\n50-ft yoke carry (380 / 300 Ib)\n\nAthletes will begin on the field and wil complete 6 rounds of a lap up the\nstadium berm and a 50-ft yoke carry, The event is complete when the athlete\ncarries the yoke across the finish line of the last section\nTime cap: 25 minutes", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 649, "codigoAtividade": null, "nome": "Games 15.13", "descricao": "For time:\n12 parallethe handstand push-ups\n24-calorie row\n16-calorie bike\n8 kettlebell deadlifts (203 / 124 Ib)\n\nThis event will begin just after completing Pedal to the Metal 1\nTime Cap: 7 minutes", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 650, "codigoAtividade": null, "nome": "Games 16.06", "descricao": "For time:\n10 squat cleans (245 /165 Ib), by 2:00\n8 squat cleans (265 / 180 lb), by 4:00\n6 squat cleans (285 / 195 lb). by 6:00\n4 squat cleans (305 I 205 Ib). by 8:00\n2 squat cleans (325 / 215 Ib). by 11:00", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 651, "codigoAtividade": null, "nome": "Games 16.07", "descricao": "Double DT\n\n10 rounds for Lime of:\n12 deadliffs (155 / 105 Ib)\n9 hang power cleans (155 / 105 Ib)\n6 push jerks (155 / 105 Ib]\n\nTime cap: 15 minutes", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 652, "codigoAtividade": null, "nome": "Games 16.08", "descricao": "3 rounds for time of:\n500-m berm run\n2 rope ascents\n40-ft. Snail push\n2 rope ascents*\n\nEach round the athletes will run up and around Lhe berm, then proceed to the\nSnail for a 40-foot push. ‘Each time they pass through the rig they will\ncomplete 2 rope ascents (twice during rounds 1 and 2, once on the final\nround).\nTimee cap: 21 minutes", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 653, "codigoAtividade": null, "nome": "Games 16.09", "descricao": "Men\nFor time:\n12 ring handstand push-ups\n15 back squats (225 lb.)\n20 burpees\n9 ring handstand push-ups\n18 front squats (205 Ib.)\n20 burpees\n6 rlng handstand push-ups\n21 overhead squats (185 Ib)\n20 burpees\nScoring table drops to 50-point event for men who fail to complete the first\nround (first set of 20 burpees).\n\nWomen\nFor time:\n15 back squats (165 Ib.)\n20 burpees\n6 ring handstand push-ups\n18 front squats (145 Ib)\n20 burpees\n4 ring handstand push-ups\n21 overhead squats (125 Ib.)\n20 burpees\n2 ring handstand push-ups\n\nScoring table drops to 50-point event for women who fail to complete 1 rep\nof the ring handstand push-up The time of completion of each set of burpees\nwill be used as a tiebreak.\n\nTime cap: 16 minutes", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}, {"cod": 654, "codigoAtividade": null, "nome": "Games 16.14", "descricao": "For time:\n200m SkiErg\n50/40 double-unders\n200-m row\n50/40 double-unders\n04-mile Assault Air Bike\n50/48 double-unders\n200m row\n50/40 double-unders\n200m SkiErg\n90-ft sled pull (310 / 220 Ib)\n\nAthletes will use a jump rope with a weighted handle.\nTime cap: 11 minutes", "versao": "", "tipo": 0, "thumb": "", "img": "", "ordem": 0, "imgMedium": "", "urlVideo": "", "aparelhos": "", "identificador": "", "camposFiltro": "", "imgMediumUrls": null, "complementoNomeAtividade": null}]}