<?xml version="1.0" encoding="UTF-8"?>
<persistence xmlns="http://java.sun.com/xml/ns/persistence"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" version="1.0"
             xsi:schemaLocation="http://java.sun.com/xml/ns/persistence
		http://java.sun.com/xml/ns/persistence/persistence_1_0.xsd">

    <persistence-unit name="NewMuscPU" transaction-type="RESOURCE_LOCAL">
        <description>
            NewMusc
        </description>

        <provider>org.hibernate.ejb.HibernatePersistence</provider>
        
        <properties>
            <property name="hibernate.archive.autodetetion" value="class"/>
            <property name="hibernate.dialect" value="org.hibernate.dialect.PostgreSQLDialect"/>
            <property name="hibernate.connection.driver_class" value="org.postgresql.Driver"/>            
            <property name="hibernate.hbm2ddl.auto" value="update"/>

            <property name="hibernate.connection.url" value="***************************************"/>
            <property name="hibernate.connection.username" value="postgres" />
            <property name="hibernate.connection.password" value="pactodb" />
            
            <property name="hibernate.show_sql" value="false"/>
            <property name="hibernate.format_sql" value="false"/>
            <property name="hibernate.generate_statistics" value="false"/>
            <property name="hibernate.use_sql_comments" value="false"/>
            <property name="hibernate.pool_size" value="2"/>
                        
            <property name="hibernate.transaction.manager_lookup_class" value="com.atomikos.icatch.jta.hibernate3.TransactionManagerLookup" />
            <property name="hibernate.transaction.factory_class" value="com.atomikos.icatch.jta.hibernate3.AtomikosJTATransactionFactory"/>
        </properties>
    </persistence-unit>
</persistence>
