<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         version="3.1">
    <display-name>TreinoWeb</display-name>

    <context-param>
        <param-name>com.sun.faces.sendPoweredByHeader</param-name>
        <param-value>false</param-value>
    </context-param>
    <context-param>
        <param-name>javax.faces.PROJECT_STAGE</param-name>
        <param-value>Production</param-value>
    </context-param>
    
    <welcome-file-list>
        <welcome-file>index.html</welcome-file>
    </welcome-file-list>
    
    <error-page>
        <exception-type>javax.faces.application.ViewExpiredException</exception-type>
        <location>/home.xhtml?faces-redirect=true</location>
    </error-page>
    
    <mime-mapping>
        <extension>mp4</extension>
        <mime-type>video/mp4</mime-type>
    </mime-mapping>
    
    <mime-mapping>
        <extension>m4a</extension>
        <mime-type>video/m4a</mime-type>
    </mime-mapping>
    
    <mime-mapping>
        <extension>ttf</extension>
        <mime-type>application/octet-stream</mime-type>        
    </mime-mapping>
    
    <mime-mapping>
        <extension>woff</extension>
        <mime-type>application/font-woff</mime-type>        
    </mime-mapping>
    
    <mime-mapping>
        <extension>eot</extension>
        <mime-type>application/vnd.ms-fontobject</mime-type>        
    </mime-mapping>
    
    <mime-mapping>
        <extension>otf</extension>
        <mime-type>application/octet-stream</mime-type>
    </mime-mapping>
    
    <servlet>
        <servlet-name>Faces Servlet</servlet-name>
        <servlet-class>javax.faces.webapp.FacesServlet</servlet-class>
        <load-on-startup>1</load-on-startup>
    </servlet>

    <filter-mapping>
        <filter-name>CrossApplicationFilter</filter-name>
        <url-pattern>/prest/*</url-pattern>
    </filter-mapping>
    <filter>
        <filter-name>CrossApplicationFilter</filter-name>
        <filter-class>br.com.pacto.filter.CrossApplicationFilter</filter-class>
        <async-supported>true</async-supported>
    </filter>

    <filter-mapping>
        <filter-name>PermissaoFilter</filter-name>
        <url-pattern>/prest/*</url-pattern>
    </filter-mapping>
    <filter-mapping>
        <filter-name>Pretty Filter</filter-name>
        <url-pattern>/*</url-pattern>
        <dispatcher>FORWARD</dispatcher>
        <dispatcher>REQUEST</dispatcher>
        <dispatcher>ERROR</dispatcher>
    </filter-mapping>
    <servlet-mapping>
        <servlet-name>Faces Servlet</servlet-name>
        <url-pattern>/act/*</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>Faces Servlet</servlet-name>
        <url-pattern>*.psg</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>Faces Servlet</servlet-name>
        <url-pattern>*.xhtml</url-pattern>
    </servlet-mapping>
    <context-param>
        <param-name>javax.faces.FACELETS_LIBRARIES</param-name>
        <param-value>/WEB-INF/functions.taglib.xml</param-value>
    </context-param>
    <context-param>
        <description>State saving method: 'client' or 'server' (=default). See JSF Specification 2.5.2</description>
        <param-name>javax.faces.STATE_SAVING_METHOD</param-name>
        <param-value>server</param-value>
    </context-param>
    <context-param>
        <param-name>com.sun.faces.numberOfViewsInSession</param-name>
        <param-value>3</param-value>
    </context-param>

    <context-param>
        <param-name>com.sun.faces.numberOfLogicalViews</param-name>
        <param-value>10</param-value>
    </context-param>
    <context-param>
        <param-name>javax.servlet.jsp.jstl.fmt.localizationContext</param-name>
        <param-value>resources.application</param-value>
    </context-param>
    <context-param>
        <param-name>primefaces.THEME</param-name>
        <param-value>bootstrap</param-value>
    </context-param>
    <context-param>
        <param-name>primefaces.PRIVATE_CAPTCHA_KEY</param-name>
        <param-value>6LfAy_MSAAAAAMJVEbOwsbdkIoLjaUq-Zz4AB9g6</param-value>
    </context-param>
    <context-param>
        <param-name>primefaces.PUBLIC_CAPTCHA_KEY</param-name>
        <param-value>6LfAy_MSAAAAAN0lB6LdipxLIKtwj6F6B2BvuyxK</param-value>
    </context-param>
    <context-param>
        <param-name>contextConfigLocation</param-name>
        <param-value>/WEB-INF/applicationContext.xml</param-value>
    </context-param>
    <listener>
        <listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
    </listener>
    <listener>
        <listener-class>com.sun.faces.config.ConfigureListener</listener-class>
    </listener>
    <servlet>
        <servlet-name>mvc-dispatcher</servlet-name>
        <servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
        <load-on-startup>1</load-on-startup>
    </servlet>
    <servlet-mapping>
        <servlet-name>mvc-dispatcher</servlet-name>
        <url-pattern>/prest/*</url-pattern>
    </servlet-mapping>
    
    <servlet>
        <servlet-name>QRCode</servlet-name>
        <servlet-class>br.com.pacto.controller.servlet.QRCodeServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>QRCode</servlet-name>
        <url-pattern>/QRCode</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>OIDServlet</servlet-name>
        <servlet-class>br.com.pacto.servlet.OIDServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>OIDServlet</servlet-name>
        <url-pattern>/oid</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>AvaliacaoIntegradaServlet</servlet-name>
        <servlet-class>br.com.pacto.servlet.AvaliacaoIntegradaServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>AvaliacaoIntegradaServlet</servlet-name>
        <url-pattern>/avaliacaointegrada</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>ImpressaoAvaliacaoServlet</servlet-name>
        <servlet-class>br.com.pacto.servlet.ImpressaoAvaliacaoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ImpressaoAvaliacaoServlet</servlet-name>
        <url-pattern>/imprimiravaliacao</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>ImagemUploadServlet</servlet-name>
        <servlet-class>br.com.pacto.servlet.ImagemUploadServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ImagemUploadServlet</servlet-name>
        <url-pattern>/imagem/upload</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>RedirectServlet</servlet-name>
        <servlet-class>br.com.pacto.servlet.RedirectServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>RedirectServlet</servlet-name>
        <url-pattern>/redir</url-pattern>
    </servlet-mapping>

    <listener>
        <display-name>Oamd Context Factory Listener</display-name>
        <listener-class>br.com.pacto.base.oamd.OamdContextListener</listener-class>
    </listener>
    
    <listener>
        <display-name>cron4j scheduler manager</display-name>
        <listener-class>br.com.pacto.base.scheduling.base.SchedulerServletContextListener</listener-class>
    </listener>   
    
    <servlet>
        <display-name>Execution Interface</display-name>
        <servlet-name>ExecutionServlet</servlet-name>
        <servlet-class>br.com.pacto.base.scheduling.base.ExecutionServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ExecutionServlet</servlet-name>
        <url-pattern>/ongoing</url-pattern>
    </servlet-mapping>
    
    <session-config>
        <session-timeout>120</session-timeout>
    </session-config>
    <filter>
        <filter-name>Pretty Filter</filter-name>
        <filter-class>com.ocpsoft.pretty.PrettyFilter</filter-class>
        <async-supported>true</async-supported>
    </filter>

    <filter>
        <filter-name>PermissaoFilter</filter-name>
        <filter-class>br.com.pacto.security.filters.PermissaoFilter</filter-class>
    </filter>


    <!--Ajuste de url local-->
    <servlet>
        <servlet-name>MidiaZWInternalServlet</servlet-name>
        <servlet-class>servicos.integracao.midias.zwinternal.MidiaZWInternalServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>MidiaZWInternalServlet</servlet-name>
        <url-pattern>/zw-photos/*</url-pattern>
    </servlet-mapping>
    <!-- MessageBroker Servlet -->
    <servlet>
        <servlet-name>MessageBrokerServlet</servlet-name>
        <servlet-class>
            flex.messaging.MessageBrokerServlet
        </servlet-class>
        <init-param>
            <param-name>services.configuration.file</param-name>
            <param-value>/WEB-INF/flex/services-config.xml</param-value>
        </init-param>
        <init-param>
            <param-name>flex.write.path</param-name>
            <param-value>/WEB-INF/flex</param-value>
        </init-param>
        <load-on-startup>2</load-on-startup>
    </servlet>
    <servlet-mapping>
        <servlet-name>MessageBrokerServlet</servlet-name>
        <url-pattern>/messagebroker/*</url-pattern>
    </servlet-mapping>

</web-app>
