<?xml version="1.0" encoding="UTF-8"?>
<facelet-taglib
        xmlns="http://java.sun.com/xml/ns/javaee"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-facelettaglibrary_2_0.xsd"
        version="2.0">
    <namespace>http://example.com/functions</namespace>

    <function>
        <function-name>escapeJS</function-name>
        <function-class>org.apache.commons.lang3.StringEscapeUtils</function-class>
        <function-signature>java.lang.String escapeEcmaScript(java.lang.String)</function-signature>
    </function>

    <function>
        <function-name>abbreviate</function-name>
        <function-class>org.apache.commons.lang3.StringUtils</function-class>
        <function-signature>java.lang.String abbreviate(java.lang.String, int)</function-signature>
    </function>
</facelet-taglib>