<?xml version="1.0" encoding="UTF-8"?>
<service id="remoting-service" class="flex.messaging.services.RemotingService">

    <adapters>
        <adapter-definition id="java-object"
            class="flex.messaging.services.remoting.adapters.JavaAdapter" default="true"/>
    </adapters>

    <destination id="gerenciadorArquivos">
        <properties>
            <source>br.com.pacto.controller.flex.ImageUploadControle</source>
        </properties>
    </destination>

  </service>