<?xml version="1.0" encoding="UTF-8"?>
<services-config>

    <services>
        <service-include file-path="remoting-config.xml" />        
    	<!-- 
    	Application level default channels. Application level default channels are 
    	necessary when a dynamic destination is being used by a service component
    	and no ChannelSet has been defined for the service component. In that case,
    	application level default channels will be used to contact the destination.
        -->   
        <default-channels>
            <channel ref="my-amf"/>
            <channel ref="my-secure-amf"/>
        </default-channels>
    </services>

    <channels>
        <channel-definition id="my-amf" class="mx.messaging.channels.AMFChannel">
            <endpoint url="http://{server.name}:{server.port}/{context.root}/messagebroker/amf"
                class="flex.messaging.endpoints.AMFEndpoint"/>
            <properties>
                <polling-enabled>false</polling-enabled>
            </properties>
        </channel-definition>
        <channel-definition id="my-secure-amf" class="mx.messaging.channels.AMFChannel">
            <endpoint url="https://{server.name}:{server.port}/{context.root}/messagebroker/amf-secure"
                class="flex.messaging.endpoints.AMFEndpoint"/>
            <properties>
                <polling-enabled>false</polling-enabled>
            </properties>
        </channel-definition>
    </channels>

    <logging>
        <!-- You may also use flex.messaging.log.ServletLogTarget -->
        <target class="flex.messaging.log.ConsoleTarget" level="Error">
            <properties>
                <prefix>[BlazeDS] </prefix>
                <includeDate>true</includeDate>
                <includeTime>true</includeTime>
                <includeLevel>true</includeLevel>
                <includeCategory>false</includeCategory>
            </properties>
            <filters>
                <pattern>Endpoint.*</pattern>
                <pattern>Service.*</pattern>
                <pattern>Configuration</pattern>
            </filters>
        </target>
    </logging>

    <system>
        <redeploy>
            <enabled>true</enabled>
            <watch-interval>20</watch-interval>
            <watch-file>{context.root}/WEB-INF/flex/services-config.xml</watch-file>
            <watch-file>{context.root}/WEB-INF/flex/remoting-config.xml</watch-file>
            <touch-file>{context.root}/WEB-INF/web.xml</touch-file>
        </redeploy>
    </system>

</services-config>
