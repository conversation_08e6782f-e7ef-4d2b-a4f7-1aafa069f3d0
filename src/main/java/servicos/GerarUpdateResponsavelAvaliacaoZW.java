package servicos;

import br.com.pacto.base.oamd.OAMD;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.*;

public class GerarUpdateResponsavelAvaliacaoZW {

    public static void main(String[] args) {
        try {
            String key = args.length > 0 ? args[0] : "profitnessba";
            sincronizarClientes(key);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public static void sincronizarClientes(String key) throws Exception{
        Map<String, Map<String, Object>> oamd = OAMD.buscarListaEmpresasMapeado();
        Map<String, Object> params = oamd.get(key);
        String nomeBD = params.get("nomeBD").toString();
        Connection bdTR = conection(params, nomeBD);
        Connection bdZW = conection(params, nomeBD.replace("bdmusc", "bdzillyon"));

        ResultSet rs = bdTR.prepareStatement("select codigo from avaliacaofisica where responsavellancamento_codigo = 0;").executeQuery();
        List<Integer> avaliacoes = new ArrayList<>();
        while(rs.next()){
            avaliacoes.add(rs.getInt("codigo"));
        }

        Map<String, String> responsaveis = new HashMap<>();
        Map<String, Integer> usernameCodigo = new HashMap<>();
        Set<String> usuarios = new HashSet<>();
        for(Integer cod : avaliacoes){
            ResultSet rsUsuarioAntigo = bdTR.prepareStatement("select u.username, u.usuariozw from avaliacaofisica_aud aa " +
                    "inner join usuario u on u.codigo = aa.responsavellancamento_codigo " +
                    "where aa.codigo = " + cod + " order by rev limit 1").executeQuery();

            String usuario = "-";
            if(rsUsuarioAntigo.next()){
                usuarios.add(rsUsuarioAntigo.getString("username"));
                int usuariozw = rsUsuarioAntigo.getInt("usuariozw");
                if(usuariozw > 0){
                    usuario = rsUsuarioAntigo.getString("username");
                    usernameCodigo.put(usuario, usuariozw);
                } else {
                    ResultSet rsUserCod = bdZW.prepareStatement("select codigo from usuario where username ilike '" +
                            rsUsuarioAntigo.getString("username") +
                            "'").executeQuery();
                    if(rsUserCod.next()){
                        usuario = rsUsuarioAntigo.getString("username");
                        usernameCodigo.put(usuario, rsUserCod.getInt("codigo"));
                    }
                }



            }
            String avaliacoesUsuario = responsaveis.get(usuario);
            if(avaliacoesUsuario == null){
                avaliacoesUsuario = "";
            }
            avaliacoesUsuario += "," + cod;
            responsaveis.put(usuario, avaliacoesUsuario);
        }

        String naoEncontrados = responsaveis.get("-");
        if(naoEncontrados != null){
            System.out.println("--nao encontrados os responsaveis ");
            System.out.println("update avaliacaofisica set responsavellancamento_codigo = 2 " +
                    " where codigo in (" + naoEncontrados.replaceFirst(",", "") + ");");
        }


        for(String username : usuarios){
            System.out.println("--" + username);
            if(responsaveis.get(username) != null){
                String avaliacoesUsuario = responsaveis.get(username);
                System.out.println("update avaliacaofisica set responsavellancamento_codigo = " +
                        usernameCodigo.get(username) + " where codigo in (" + avaliacoesUsuario.replaceFirst(",", "") + ");");
            }
        }

    }

    public static Connection conection(Map<String, Object> empresa, String bd) throws Exception{
        String urlBD = "jdbc:postgresql://"+ empresa.get("hostBD") +
                ":" + empresa.get("porta") + "/" + bd;
        return DriverManager.getConnection(urlBD, empresa.get("userBD").toString(), empresa.get("passwordBD").toString());
    }


}

