package servicos;

import br.com.pacto.base.oamd.OAMD;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.usuario.StatusEnum;
import br.com.pacto.bean.usuario.TipoUsuarioEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.objeto.Uteis;

import java.math.BigInteger;
import java.security.SecureRandom;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Map;

public class CorrigirMatriculasZW {

    public static void main(String[] args) {
        try {
            String key = args.length > 0 ? args[0] : "engenhariadocorpomatriz";
            sincronizarClientes(key);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public static void sincronizarClientes(String key) throws Exception{
        Map<String, Map<String, Object>> oamd = OAMD.buscarListaEmpresasMapeado();
        Map<String, Object> params = oamd.get(key);
        String nomeBD = params.get("nomeBD").toString();
        Connection bdTR = conection(params, nomeBD);
        Connection bdZW = conection(params, nomeBD.replace("bdmusc", "bdzillyon"));
        bdZW.setAutoCommit(false);
        bdTR.setAutoCommit(false);
        ResultSet rs = bdZW.prepareStatement("select codigo, codigomatricula from cliente").executeQuery();
        System.out.println("atualizar matriculas...");
        while(rs.next()){
            int codigoCliente = rs.getInt("codigo");
            int matricula = rs.getInt("codigomatricula");
            bdTR.prepareStatement("update clientesintetico set matricula = " + matricula
                     + " where codigocliente = " + codigoCliente);

            bdTR.prepareStatement("update clientepesquisa set matricula = " + matricula
                     + " where codigoexterno = " + codigoCliente);
        }

        bdZW.setAutoCommit(true);
        bdTR.setAutoCommit(true);
        System.out.println("atualizadas!");
    }

    public static Connection conection(Map<String, Object> empresa, String bd) throws Exception{
        String urlBD = "jdbc:postgresql://"+ empresa.get("hostBD") +
                ":" + empresa.get("porta") + "/" + bd;
        return DriverManager.getConnection(urlBD, empresa.get("userBD").toString(), empresa.get("passwordBD").toString());
    }


}

