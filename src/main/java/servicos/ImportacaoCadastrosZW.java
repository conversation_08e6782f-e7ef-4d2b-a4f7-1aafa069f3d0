package servicos;

import br.com.pacto.base.oamd.OAMD;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.usuario.StatusEnum;
import br.com.pacto.bean.usuario.TipoUsuarioEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.objeto.Uteis;

import java.math.BigInteger;
import java.security.SecureRandom;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Map;

public class ImportacaoCadastrosZW {

    public static void main(String[] args) {
        try {
            String urlOamd2 = "******************************************";
            String ipZona = "*************";
            String key = args.length > 0 ? args[0] : "bed9b599b4dd4a32c42b59e81cd677b0";
            sincronizarClientes(key, urlOamd2, ipZona);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public static void sincronizarClientes(String key, String urlOamd2, String ipZona) throws Exception{
        Map<String, Map<String, Object>> oamd = OAMD.buscarListaEmpresasMapeado(urlOamd2);
        Map<String, Object> params = oamd.get(key);
        String nomeBD = params.get("nomeBD").toString();
        Connection bdTR = conection(params, nomeBD, ipZona);
        Connection bdZW = conection(params, nomeBD.replace("bdmusc", "bdzillyon"), ipZona);
        criarTabelaSeNaoExistir(bdZW);
        boolean temClientePraSincronizar = true;

        int total = 1;
        int pont = 0;
        while (temClientePraSincronizar && pont < total){
            try {
                ResultSet rsCont = bdZW.prepareStatement("select count(s.codigo) as cont from situacaoclientesinteticodw s " +
                        " left join clientebdtreino c on c.codigocliente = s.codigocliente " +
                        " where c.codigo is null").executeQuery();
                if(rsCont.next()){
                    total = rsCont.getInt("cont");
                    temClientePraSincronizar = rsCont.getInt("cont") > 0;
                } else {
                    break;
                }
            }catch (Exception e){
                break;
            }
            ResultSet rs = obterClientes(bdZW);
            while(rs.next()){
                System.out.println(++pont + "/" + total);
                int codigoCliente = rs.getInt("codigocliente");
                Integer clienteSintetico = inserirClienteSintetico(rs, bdTR);
                if(clienteSintetico != null){
                    System.out.println("obtendo usuario movel");
                    Usuario usuario = usuarioMovelZW(codigoCliente, rs.getInt("matricula"), rs.getInt("empresacliente"), bdZW);
                    usuario.setTipo(TipoUsuarioEnum.ALUNO);
                    usuario.setStatus(StatusEnum.ATIVO);
                    usuario.setCliente(new ClienteSintetico());
                    usuario.getCliente().setCodigo(clienteSintetico);
                    System.out.println("gravar usuario movel no treino");
                    inserirUsuarioTR(usuario, bdTR);
                    marcarJaExiste(bdZW, codigoCliente, clienteSintetico);
                    System.out.println("ok");
                }
            }

        }

    }

    public static Connection conection(Map<String, Object> empresa, String bd, String ipZona) throws Exception{
        String urlBD = "jdbc:postgresql://"+ ipZona +
                ":" + empresa.get("porta") + "/" + bd;
        return DriverManager.getConnection(urlBD, empresa.get("userBD").toString(), empresa.get("passwordBD").toString());
    }

    public static void inserirUsuarioTR(Usuario usuario, Connection bdTR){
        try {
            String sql = "INSERT INTO public.usuario\n" +
                    "(usuariozw, cliente_codigo, codigoexterno, empresazw, nome, senha, status, tipo, username)\n" +
                    "VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?);";
            PreparedStatement stm = bdTR.prepareStatement(sql);
            int i = 1;
            stm.setInt(i++, 0);
            stm.setInt(i++, usuario.getCliente().getCodigo());
            stm.setString(i++, usuario.getCodigoExterno());
            stm.setInt(i++, usuario.getEmpresaZW());
            stm.setString(i++, usuario.getNome());
            stm.setString(i++, usuario.getSenha());
            stm.setInt(i++, usuario.getStatus().ordinal());
            stm.setInt(i++, usuario.getTipo().ordinal());
            stm.setString(i++, usuario.getUserName());

            stm.execute();
        }catch (Exception e){
            e.printStackTrace();
        }
    }
    public static Usuario usuarioMovelZW(Integer codigoCliente, Integer matricula, Integer empresa, Connection bdzw){
        try {
            Usuario usuario = new Usuario();
            ResultSet rs = bdzw.prepareStatement("select nome, codigo, senha, empresa from usuariomovel u where cliente = " + codigoCliente).executeQuery();
            if(rs.next()){
                usuario.setCodigoExterno(String.valueOf(rs.getInt("codigo")));
                usuario.setUsuarioZW(0);
                usuario.setEmpresaZW(rs.getInt("empresa"));
                usuario.setUserName(rs.getString("nome"));
                usuario.setNome(rs.getString("nome"));
                usuario.setSenha(rs.getString("senha"));
            } else {
                PreparedStatement stm = bdzw.prepareStatement("insert into usuariomovel (nome, senha, empresa, cliente) values (?,?,?,?)");
                SecureRandom random = new SecureRandom();
                String senha = new BigInteger(130, random).toString(32);
                senha = senha.length() > 8 ? senha.substring(0, 8) : senha;
                senha = Uteis.encriptar(senha.toUpperCase());
                usuario.setSenha(senha);
                usuario.setUsuarioZW(0);
                usuario.setEmpresaZW(empresa);
                usuario.setUserName(String.valueOf(matricula));
                usuario.setNome(String.valueOf(matricula));

                stm.setString(1, usuario.getUserName());
                stm.setString(2, usuario.getSenha());
                stm.setInt(3, empresa);
                stm.setInt(4, codigoCliente);

                stm.execute();

                ResultSet rsUser = bdzw.prepareStatement("select codigo from usuariomovel u where cliente = " + codigoCliente).executeQuery();
                if(rsUser.next()){
                   usuario.setCodigoExterno(String.valueOf(rsUser.getInt("codigo")));
                } else {
                    throw new Exception("não foi possivel obter o usuariomovel do aluno");
                }
            }
            return usuario;
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }

    private static ResultSet obterClientes(Connection bdZW) throws Exception{
        return bdZW.prepareStatement("select s.*, " +
                "ARRAY_TO_STRING(ARRAY(select email from email where pessoa = s.codigopessoa), ';') as emails, " +
                "ARRAY_TO_STRING(ARRAY(select numero from telefone where pessoa = s.codigopessoa), ';') as telefones " +
                " from situacaoclientesinteticodw s " +
                " left join clientebdtreino c on c.codigocliente = s.codigocliente " +
                " where c.codigo is null " +
                " limit 5000").executeQuery();
    }

    private static Integer inserirClienteSintetico(ResultSet rs, Connection bdTR){
        try {
            StringBuilder insert = new StringBuilder();
            insert.append(" INSERT INTO public.clientesintetico\n");
            insert.append(" (cpf, rg, uf, ativo, bairro, cargo, cidade, codigoacesso,\n");
            insert.append("                 codigocliente, codigocontrato, codigoexterno, codigopessoa,\n");
            insert.append("                 codigoultimocontatocrm, colaboradores, crossfit, dadosavaliacao,\n");
            insert.append("                 dataatualizacaofoto, datacadastro, datafimperiodoacesso, datainicioperiodoacesso,\n");
            insert.append("                 datalancamentocontrato, datamatricula, datanascimento, datarematriculacontrato,\n");
            insert.append("                 datarenovacaocontrato, dataultimarematricula, dataultimobv, dataultimocontatocrm,\n");
            insert.append("                 dataultimoacesso, datavigenciaate, datavigenciaateajustada, datavigenciade,\n");
            insert.append("                 descricaoduracao, descricoesmodalidades, dia, diasacessomes2,\n");
            insert.append("                 diasacessomes3, diasacessomes4, diasacessosemana2, diasacessosemana3,\n");
            insert.append("                 diasacessosemana4, diasacessosemanapassada, diasacessoultimomes, diasassiduidadeultrematriculaatehoje,\n");
            insert.append("                 diasfaltasemacesso, duracaocontratomeses, email, empresa, empresausafreepass, endereco, estadocivil,\n");
            insert.append("                 existeparcvencidacontrato, faseatualcrm, fcmaxima, fcrepouso, fotokeyapp, freepass,\n");
            insert.append("                 frequenciasemanal, idade, matricula, mediadiasacesso4meses, mnemonicodocontrato, modalidades, nome, nomeconsulta,\n");
            insert.append("                 nomeplano, nraulasexperimentais, nrtreinosprevistos, nrtreinosrealizados, objetivos, parq, pesorisco,\n");
            insert.append("                 profissao, responsavelultimocontatocrm, saldocontacorrentecliente, saldocreditotreino, sexo, situacao,\n");
            insert.append("                 situacaocontrato, situacaocontratooperacao, situacaomatriculacontrato, telefones, tipoperiodoacesso,\n");
            insert.append("                 totalcreditotreino, ultimavisita, valorpagocontrato, valorparcabertocontrato, valorfaturadocontrato,\n");
            insert.append("                 versao, vezesporsemana, grupo_codigo, nivelaluno_codigo, pessoa_codigo, professorsintetico_codigo, \n");
            insert.append("                 massamagraatual, massamagrainicio, percentualgorduraatual, \n");
            insert.append("                 percentualgordurainicio, pesoatual, pesoinicio, gympassuniquetoken)\n");
            insert.append(" VALUES(?, ?, ?, true, ?, ?, ?, ?, ?, ?, '', ?, ?, ?, ?,\n");
            insert.append("         NULL, NULL, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,\n");
            insert.append("         ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,\n");
            insert.append("         ?, ?, ?, ?, NULL, NULL, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,\n");
            insert.append("         ?, NULL, NULL,NULL, '', NULL, 0, ?, ?, 0.0, 0, ?, ?, ?, ?, ?, ?, ?, 0, NULL, 0.0, 0.0, 0.0, 1, 0, NULL,\n");
            insert.append("         NULL, null, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);\n");
            PreparedStatement stm = bdTR.prepareStatement(insert.toString());
            int i = 1;
            stm.setString(i++, rs.getString("cpf"));
            stm.setString(i++, rs.getString("rg"));
            stm.setString(i++, rs.getString("uf"));
            stm.setString(i++, rs.getString("bairro"));
            stm.setString(i++, rs.getString("cargo"));
            stm.setString(i++, rs.getString("cidade"));
            stm.setString(i++, rs.getString("codacessocliente"));
            stm.setInt(i++, rs.getInt("codigocliente"));
            stm.setInt(i++, rs.getInt("codigocontrato"));
            stm.setInt(i++, rs.getInt("codigopessoa"));
            stm.setInt(i++, rs.getInt("codigoultimocontatocrm"));
            stm.setString(i++, rs.getString("colaboradores"));
            stm.setBoolean(i++, rs.getBoolean("crossfit"));
            stm.setDate(i++, rs.getDate("datacadastro"));
            stm.setDate(i++, rs.getDate("datafimperiodoacesso"));
            stm.setDate(i++, rs.getDate("datainicioperiodoacesso"));
            stm.setDate(i++, rs.getDate("datalancamentocontrato"));
            stm.setDate(i++, rs.getDate("datamatricula"));
            stm.setDate(i++, rs.getDate("datanascimento"));
            stm.setDate(i++, rs.getDate("datarematriculacontrato"));
            stm.setDate(i++, rs.getDate("datarenovacaocontrato"));
            stm.setDate(i++, rs.getDate("dataultimarematricula"));
            stm.setDate(i++, rs.getDate("dataultimobv"));
            stm.setDate(i++, rs.getDate("dataultimocontatocrm"));
            stm.setDate(i++, rs.getDate("dataultimoacesso"));
            stm.setDate(i++, rs.getDate("datavigenciaate"));
            stm.setDate(i++, rs.getDate("datavigenciaateajustada"));
            stm.setDate(i++, rs.getDate("datavigenciade"));
            int duracaocontratomeses = rs.getInt("duracaocontratomeses");
            stm.setString(i++, duracaocontratomeses == 1 ? "1 mês" : (duracaocontratomeses + " meses"));
            stm.setString(i++, rs.getString("descricoesmodalidades"));
            stm.setDate(i++, rs.getDate("dia"));
            stm.setInt(i++, rs.getInt("diasacessomes2"));
            stm.setInt(i++, rs.getInt("diasacessomes3"));
            stm.setInt(i++, rs.getInt("diasacessomes4"));
            stm.setInt(i++, rs.getInt("diasacessosemana2"));
            stm.setInt(i++, rs.getInt("diasacessosemana3"));
            stm.setInt(i++, rs.getInt("diasacessosemana4"));
            stm.setInt(i++, rs.getInt("diasacessosemanapassada"));
            stm.setInt(i++, rs.getInt("diasacessoultimomes"));
            stm.setInt(i++, rs.getInt("diasassiduidadeultrematriculaatehoje"));
            stm.setInt(i++, 0);
            stm.setInt(i++, duracaocontratomeses);
            stm.setString(i++, rs.getString("emails"));
            stm.setInt(i++, rs.getInt("empresacliente"));
            stm.setBoolean(i++, rs.getBoolean("empresausafreepass"));
            stm.setString(i++, rs.getString("endereco"));
            stm.setString(i++, rs.getString("estadocivil"));
            stm.setBoolean(i++, rs.getBoolean("empresausafreepass"));
            stm.setBoolean(i++, rs.getBoolean("existeparcvencidacontrato"));
            stm.setString(i++, rs.getString("faseatualcrm"));
            stm.setBoolean(i++, rs.getBoolean("freepass"));
            stm.setInt(i++, rs.getInt("frequenciasemanal"));
            stm.setInt(i++, rs.getInt("idade"));
            stm.setInt(i++, rs.getInt("matricula"));
            stm.setInt(i++, rs.getInt("mediadiasacesso4meses"));
            stm.setString(i++, "");
            stm.setString(i++, rs.getString("modalidades"));
            stm.setString(i++, rs.getString("nomecliente"));
            stm.setString(i++, rs.getString("nomeconsulta"));
            stm.setString(i++, rs.getString("nomeplano"));
            stm.setInt(i++, rs.getInt("nraulaexperimental"));
            stm.setString(i++, rs.getString("profissao"));
            stm.setString(i++, rs.getString("responsavelultimocontatocrm"));
            stm.setString(i++, rs.getString("sexocliente"));
            stm.setString(i++, rs.getString("situacao"));
            stm.setString(i++, rs.getString("situacaocontrato"));
            stm.setString(i++, rs.getString("situacaocontratooperacao"));
            stm.setString(i++, rs.getString("situacaomatriculacontrato"));
            stm.setString(i++, rs.getString("telefones"));

            stm.execute();
            int matricula = rs.getInt("matricula");
            ResultSet rsCodigo = bdTR.prepareStatement("select codigo from clientesintetico where matricula = " + matricula).executeQuery();
            if(rsCodigo.next()){
                return rsCodigo.getInt("codigo");
            }

        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }

    private static void marcarJaExiste(Connection bdZW, Integer codigoCliente, Integer clienteSintetico){
        try {
            bdZW.prepareStatement("insert into clientebdtreino(codigocliente,codigoclientesintetico) " +
                    "values ("+ codigoCliente +", "+clienteSintetico+")").execute();
        }catch (Exception e){
            e.printStackTrace();
        }

    }

    private static void criarTabelaSeNaoExistir(Connection bdZW){
        try {
            bdZW.prepareStatement("create table clientebdtreino(" +
                    " codigo serial primary key," +
                    " codigocliente int," +
                    " codigoclientesintetico int" +
                    ")").execute();
        }catch (Exception e){
            System.out.println("tabela já existe no bdZw");
        }
    }
}

