
package servicos.integracao.adm.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de empresaTO complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType name="empresaTO">
 *   &lt;complexContent>
 *     &lt;extension base="{http://adm.integracao.servicos/}superTO">
 *       &lt;sequence>
 *         &lt;element name="ativa" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *         &lt;element name="chave" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="codigo" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="hostBD" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="modulos" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="nomeBD" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="passwordBD" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="permitemultiempresas" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *         &lt;element name="porta" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="robocontrole" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="urlgestaonotas" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="urlgestaonotasrest" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="usarBDLocal" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *         &lt;element name="userBD" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "empresaTO", propOrder = {
    "ativa",
    "chave",
    "codigo",
    "hostBD",
    "modulos",
    "nomeBD",
    "passwordBD",
    "permitemultiempresas",
    "porta",
    "robocontrole",
    "urlgestaonotas",
    "urlgestaonotasrest",
    "usarBDLocal",
    "userBD"
})
public class EmpresaTO
    extends SuperTO
{

    protected Boolean ativa;
    protected String chave;
    protected Integer codigo;
    protected String hostBD;
    protected String modulos;
    protected String nomeBD;
    protected String passwordBD;
    protected Boolean permitemultiempresas;
    protected Integer porta;
    protected String robocontrole;
    protected String urlgestaonotas;
    protected String urlgestaonotasrest;
    protected Boolean usarBDLocal;
    protected String userBD;

    /**
     * Obtém o valor da propriedade ativa.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isAtiva() {
        return ativa;
    }

    /**
     * Define o valor da propriedade ativa.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setAtiva(Boolean value) {
        this.ativa = value;
    }

    /**
     * Obtém o valor da propriedade chave.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getChave() {
        return chave;
    }

    /**
     * Define o valor da propriedade chave.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setChave(String value) {
        this.chave = value;
    }

    /**
     * Obtém o valor da propriedade codigo.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCodigo() {
        return codigo;
    }

    /**
     * Define o valor da propriedade codigo.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCodigo(Integer value) {
        this.codigo = value;
    }

    /**
     * Obtém o valor da propriedade hostBD.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getHostBD() {
        return hostBD;
    }

    /**
     * Define o valor da propriedade hostBD.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setHostBD(String value) {
        this.hostBD = value;
    }

    /**
     * Obtém o valor da propriedade modulos.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getModulos() {
        return modulos;
    }

    /**
     * Define o valor da propriedade modulos.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setModulos(String value) {
        this.modulos = value;
    }

    /**
     * Obtém o valor da propriedade nomeBD.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNomeBD() {
        return nomeBD;
    }

    /**
     * Define o valor da propriedade nomeBD.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNomeBD(String value) {
        this.nomeBD = value;
    }

    /**
     * Obtém o valor da propriedade passwordBD.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPasswordBD() {
        return passwordBD;
    }

    /**
     * Define o valor da propriedade passwordBD.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPasswordBD(String value) {
        this.passwordBD = value;
    }

    /**
     * Obtém o valor da propriedade permitemultiempresas.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isPermitemultiempresas() {
        return permitemultiempresas;
    }

    /**
     * Define o valor da propriedade permitemultiempresas.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setPermitemultiempresas(Boolean value) {
        this.permitemultiempresas = value;
    }

    /**
     * Obtém o valor da propriedade porta.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getPorta() {
        return porta;
    }

    /**
     * Define o valor da propriedade porta.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setPorta(Integer value) {
        this.porta = value;
    }

    /**
     * Obtém o valor da propriedade robocontrole.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRobocontrole() {
        return robocontrole;
    }

    /**
     * Define o valor da propriedade robocontrole.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRobocontrole(String value) {
        this.robocontrole = value;
    }

    /**
     * Obtém o valor da propriedade urlgestaonotas.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUrlgestaonotas() {
        return urlgestaonotas;
    }

    /**
     * Define o valor da propriedade urlgestaonotas.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUrlgestaonotas(String value) {
        this.urlgestaonotas = value;
    }

    /**
     * Obtém o valor da propriedade urlgestaonotasrest.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUrlgestaonotasrest() {
        return urlgestaonotasrest;
    }

    /**
     * Define o valor da propriedade urlgestaonotasrest.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUrlgestaonotasrest(String value) {
        this.urlgestaonotasrest = value;
    }

    /**
     * Obtém o valor da propriedade usarBDLocal.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isUsarBDLocal() {
        return usarBDLocal;
    }

    /**
     * Define o valor da propriedade usarBDLocal.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setUsarBDLocal(Boolean value) {
        this.usarBDLocal = value;
    }

    /**
     * Obtém o valor da propriedade userBD.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUserBD() {
        return userBD;
    }

    /**
     * Define o valor da propriedade userBD.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUserBD(String value) {
        this.userBD = value;
    }

}
