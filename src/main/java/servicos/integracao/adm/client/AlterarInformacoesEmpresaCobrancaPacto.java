
package servicos.integracao.adm.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de alterarInformacoesEmpresaCobrancaPacto complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType name="alterarInformacoesEmpresaCobrancaPacto">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="key" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="codigoEmpresa" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="tipoCobrancaPacto" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="gerarCobrancaAutomaticaPacto" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="qtdDiasFechamentoCobrancaPacto" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="valorCreditoPacto" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/>
 *         &lt;element name="gerarNotaFiscalCobrancaPacto" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="qtdParcelasCobrancaPacto" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="qtdCreditoRenovarPrePagoCobrancaPacto" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="nomeClienteCobrancaPacto" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="emailClienteCobrancaPacto" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="celularClienteCobrancaPacto" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="nomeUsuarioOAMD" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "alterarInformacoesEmpresaCobrancaPacto", propOrder = {
    "key",
    "codigoEmpresa",
    "tipoCobrancaPacto",
    "gerarCobrancaAutomaticaPacto",
    "qtdDiasFechamentoCobrancaPacto",
    "valorCreditoPacto",
    "gerarNotaFiscalCobrancaPacto",
    "qtdParcelasCobrancaPacto",
    "qtdCreditoRenovarPrePagoCobrancaPacto",
    "nomeClienteCobrancaPacto",
    "emailClienteCobrancaPacto",
    "celularClienteCobrancaPacto",
    "nomeUsuarioOAMD"
})
public class AlterarInformacoesEmpresaCobrancaPacto {

    protected String key;
    protected Integer codigoEmpresa;
    protected Integer tipoCobrancaPacto;
    protected boolean gerarCobrancaAutomaticaPacto;
    protected Integer qtdDiasFechamentoCobrancaPacto;
    protected Double valorCreditoPacto;
    protected boolean gerarNotaFiscalCobrancaPacto;
    protected Integer qtdParcelasCobrancaPacto;
    protected Integer qtdCreditoRenovarPrePagoCobrancaPacto;
    protected String nomeClienteCobrancaPacto;
    protected String emailClienteCobrancaPacto;
    protected String celularClienteCobrancaPacto;
    protected String nomeUsuarioOAMD;

    /**
     * Obtém o valor da propriedade key.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKey() {
        return key;
    }

    /**
     * Define o valor da propriedade key.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKey(String value) {
        this.key = value;
    }

    /**
     * Obtém o valor da propriedade codigoEmpresa.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCodigoEmpresa() {
        return codigoEmpresa;
    }

    /**
     * Define o valor da propriedade codigoEmpresa.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCodigoEmpresa(Integer value) {
        this.codigoEmpresa = value;
    }

    /**
     * Obtém o valor da propriedade tipoCobrancaPacto.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getTipoCobrancaPacto() {
        return tipoCobrancaPacto;
    }

    /**
     * Define o valor da propriedade tipoCobrancaPacto.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setTipoCobrancaPacto(Integer value) {
        this.tipoCobrancaPacto = value;
    }

    /**
     * Obtém o valor da propriedade gerarCobrancaAutomaticaPacto.
     * 
     */
    public boolean isGerarCobrancaAutomaticaPacto() {
        return gerarCobrancaAutomaticaPacto;
    }

    /**
     * Define o valor da propriedade gerarCobrancaAutomaticaPacto.
     * 
     */
    public void setGerarCobrancaAutomaticaPacto(boolean value) {
        this.gerarCobrancaAutomaticaPacto = value;
    }

    /**
     * Obtém o valor da propriedade qtdDiasFechamentoCobrancaPacto.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getQtdDiasFechamentoCobrancaPacto() {
        return qtdDiasFechamentoCobrancaPacto;
    }

    /**
     * Define o valor da propriedade qtdDiasFechamentoCobrancaPacto.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setQtdDiasFechamentoCobrancaPacto(Integer value) {
        this.qtdDiasFechamentoCobrancaPacto = value;
    }

    /**
     * Obtém o valor da propriedade valorCreditoPacto.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getValorCreditoPacto() {
        return valorCreditoPacto;
    }

    /**
     * Define o valor da propriedade valorCreditoPacto.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setValorCreditoPacto(Double value) {
        this.valorCreditoPacto = value;
    }

    /**
     * Obtém o valor da propriedade gerarNotaFiscalCobrancaPacto.
     * 
     */
    public boolean isGerarNotaFiscalCobrancaPacto() {
        return gerarNotaFiscalCobrancaPacto;
    }

    /**
     * Define o valor da propriedade gerarNotaFiscalCobrancaPacto.
     * 
     */
    public void setGerarNotaFiscalCobrancaPacto(boolean value) {
        this.gerarNotaFiscalCobrancaPacto = value;
    }

    /**
     * Obtém o valor da propriedade qtdParcelasCobrancaPacto.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getQtdParcelasCobrancaPacto() {
        return qtdParcelasCobrancaPacto;
    }

    /**
     * Define o valor da propriedade qtdParcelasCobrancaPacto.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setQtdParcelasCobrancaPacto(Integer value) {
        this.qtdParcelasCobrancaPacto = value;
    }

    /**
     * Obtém o valor da propriedade qtdCreditoRenovarPrePagoCobrancaPacto.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getQtdCreditoRenovarPrePagoCobrancaPacto() {
        return qtdCreditoRenovarPrePagoCobrancaPacto;
    }

    /**
     * Define o valor da propriedade qtdCreditoRenovarPrePagoCobrancaPacto.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setQtdCreditoRenovarPrePagoCobrancaPacto(Integer value) {
        this.qtdCreditoRenovarPrePagoCobrancaPacto = value;
    }

    /**
     * Obtém o valor da propriedade nomeClienteCobrancaPacto.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNomeClienteCobrancaPacto() {
        return nomeClienteCobrancaPacto;
    }

    /**
     * Define o valor da propriedade nomeClienteCobrancaPacto.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNomeClienteCobrancaPacto(String value) {
        this.nomeClienteCobrancaPacto = value;
    }

    /**
     * Obtém o valor da propriedade emailClienteCobrancaPacto.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEmailClienteCobrancaPacto() {
        return emailClienteCobrancaPacto;
    }

    /**
     * Define o valor da propriedade emailClienteCobrancaPacto.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEmailClienteCobrancaPacto(String value) {
        this.emailClienteCobrancaPacto = value;
    }

    /**
     * Obtém o valor da propriedade celularClienteCobrancaPacto.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCelularClienteCobrancaPacto() {
        return celularClienteCobrancaPacto;
    }

    /**
     * Define o valor da propriedade celularClienteCobrancaPacto.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCelularClienteCobrancaPacto(String value) {
        this.celularClienteCobrancaPacto = value;
    }

    /**
     * Obtém o valor da propriedade nomeUsuarioOAMD.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNomeUsuarioOAMD() {
        return nomeUsuarioOAMD;
    }

    /**
     * Define o valor da propriedade nomeUsuarioOAMD.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNomeUsuarioOAMD(String value) {
        this.nomeUsuarioOAMD = value;
    }

}
