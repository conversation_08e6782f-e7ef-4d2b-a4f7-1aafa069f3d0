
package servicos.integracao.adm.client;

import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceException;
import javax.xml.ws.WebServiceFeature;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.6b21 
 * Generated source version: 2.1
 * 
 */
@WebServiceClient(name = "AdmWS", targetNamespace = "http://adm.integracao.servicos/", wsdlLocation = "https://app.pactosolucoes.com.br/app/AdmWS?wsdl")
public class AdmWS_Service
    extends Service
{

    private final static URL ADMWS_WSDL_LOCATION;
    private final static WebServiceException ADMWS_EXCEPTION;
    private final static QName ADMWS_QNAME = new QName("http://adm.integracao.servicos/", "AdmWS");

    static {
        URL url = null;
        WebServiceException e = null;
        try {
            url = new URL("https://app.pactosolucoes.com.br/app/AdmWS?wsdl");
        } catch (MalformedURLException ex) {
            e = new WebServiceException(ex);
        }
        ADMWS_WSDL_LOCATION = url;
        ADMWS_EXCEPTION = e;
    }

    public AdmWS_Service() {
        super(__getWsdlLocation(), ADMWS_QNAME);
    }

    public AdmWS_Service(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    /**
     * 
     * @return
     *     returns AdmWS
     */
    @WebEndpoint(name = "AdmWSPort")
    public AdmWS getAdmWSPort() {
        return super.getPort(new QName("http://adm.integracao.servicos/", "AdmWSPort"), AdmWS.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns AdmWS
     */
    @WebEndpoint(name = "AdmWSPort")
    public AdmWS getAdmWSPort(WebServiceFeature... features) {
        return super.getPort(new QName("http://adm.integracao.servicos/", "AdmWSPort"), AdmWS.class, features);
    }

    private static URL __getWsdlLocation() {
        if (ADMWS_EXCEPTION!= null) {
            throw ADMWS_EXCEPTION;
        }
        return ADMWS_WSDL_LOCATION;
    }

}
