
package servicos.integracao.adm.client;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.namespace.QName;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the servicos.integracao.adm.client package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _AlterarCreditoDCCResponse_QNAME = new QName("http://adm.integracao.servicos/", "alterarCreditoDCCResponse");
    private final static QName _AlterarGrupoChatMovidesk_QNAME = new QName("http://adm.integracao.servicos/", "alterarGrupoChatMovidesk");
    private final static QName _ObterUsuariosResponse_QNAME = new QName("http://adm.integracao.servicos/", "obterUsuariosResponse");
    private final static QName _ObterEmpresa_QNAME = new QName("http://adm.integracao.servicos/", "obterEmpresa");
    private final static QName _AlterarGrupoChatMovideskResponse_QNAME = new QName("http://adm.integracao.servicos/", "alterarGrupoChatMovideskResponse");
    private final static QName _ObterFotoResponse_QNAME = new QName("http://adm.integracao.servicos/", "obterFotoResponse");
    private final static QName _ObterEmpresasResponse_QNAME = new QName("http://adm.integracao.servicos/", "obterEmpresasResponse");
    private final static QName _ObterEmpresasComSituacaoResponse_QNAME = new QName("http://adm.integracao.servicos/", "obterEmpresasComSituacaoResponse");
    private final static QName _AlterarCreditoDCC_QNAME = new QName("http://adm.integracao.servicos/", "alterarCreditoDCC");
    private final static QName _ObterEmpresasUsuarioResponse_QNAME = new QName("http://adm.integracao.servicos/", "obterEmpresasUsuarioResponse");
    private final static QName _ObterFoto_QNAME = new QName("http://adm.integracao.servicos/", "obterFoto");
    private final static QName _GravarPrePagoDCCLancarAgendamentoFinanResponse_QNAME = new QName("http://adm.integracao.servicos/", "gravarPrePagoDCCLancarAgendamentoFinanResponse");
    private final static QName _ObterEmpresasUsuario_QNAME = new QName("http://adm.integracao.servicos/", "obterEmpresasUsuario");
    private final static QName _ObterEmpresasComSituacao_QNAME = new QName("http://adm.integracao.servicos/", "obterEmpresasComSituacao");
    private final static QName _ObterEmpresas_QNAME = new QName("http://adm.integracao.servicos/", "obterEmpresas");
    private final static QName _LancarContaFinanceiroResponse_QNAME = new QName("http://adm.integracao.servicos/", "lancarContaFinanceiroResponse");
    private final static QName _UtilizarMoviDeskResponse_QNAME = new QName("http://adm.integracao.servicos/", "utilizarMoviDeskResponse");
    private final static QName _ObterEmpresaResponse_QNAME = new QName("http://adm.integracao.servicos/", "obterEmpresaResponse");
    private final static QName _ConsultarQtdCreditoUtilizadoResponse_QNAME = new QName("http://adm.integracao.servicos/", "consultarQtdCreditoUtilizadoResponse");
    private final static QName _DesvincularNotaFiscalEmitidaResponse_QNAME = new QName("http://adm.integracao.servicos/", "desvincularNotaFiscalEmitidaResponse");
    private final static QName _ExecutarProcessoLatitudeLongitudeResponse_QNAME = new QName("http://adm.integracao.servicos/", "executarProcessoLatitudeLongitudeResponse");
    private final static QName _LancarContaFinanceiro_QNAME = new QName("http://adm.integracao.servicos/", "lancarContaFinanceiro");
    private final static QName _UtilizarChatMoviDeskResponse_QNAME = new QName("http://adm.integracao.servicos/", "utilizarChatMoviDeskResponse");
    private final static QName _ConsultarQtdCreditoUtilizado_QNAME = new QName("http://adm.integracao.servicos/", "consultarQtdCreditoUtilizado");
    private final static QName _AjustarEmpresaParaIniciarPosPago_QNAME = new QName("http://adm.integracao.servicos/", "ajustarEmpresaParaIniciarPosPago");
    private final static QName _AjustarEmpresaParaIniciarPosPagoResponse_QNAME = new QName("http://adm.integracao.servicos/", "ajustarEmpresaParaIniciarPosPagoResponse");
    private final static QName _AlterarInformacoesEmpresaCobrancaPacto_QNAME = new QName("http://adm.integracao.servicos/", "alterarInformacoesEmpresaCobrancaPacto");
    private final static QName _ObterUsuarios_QNAME = new QName("http://adm.integracao.servicos/", "obterUsuarios");
    private final static QName _GravarPosPagoDCCLancarAgendamentoFinanResponse_QNAME = new QName("http://adm.integracao.servicos/", "gravarPosPagoDCCLancarAgendamentoFinanResponse");
    private final static QName _UtilizarMoviDesk_QNAME = new QName("http://adm.integracao.servicos/", "utilizarMoviDesk");
    private final static QName _ExecutarProcessoLatitudeLongitude_QNAME = new QName("http://adm.integracao.servicos/", "executarProcessoLatitudeLongitude");
    private final static QName _GravarPrePagoDCCLancarAgendamentoFinan_QNAME = new QName("http://adm.integracao.servicos/", "gravarPrePagoDCCLancarAgendamentoFinan");
    private final static QName _ConsultarFavorecidoFinanceiro_QNAME = new QName("http://adm.integracao.servicos/", "consultarFavorecidoFinanceiro");
    private final static QName _ConsultarFavorecidoFinanceiroResponse_QNAME = new QName("http://adm.integracao.servicos/", "consultarFavorecidoFinanceiroResponse");
    private final static QName _AtualizarDadosEmpresa_QNAME = new QName("http://adm.integracao.servicos/", "atualizarDadosEmpresa");
    private final static QName _GravarPosPagoDCCLancarAgendamentoFinan_QNAME = new QName("http://adm.integracao.servicos/", "gravarPosPagoDCCLancarAgendamentoFinan");
    private final static QName _UtilizarChatMoviDesk_QNAME = new QName("http://adm.integracao.servicos/", "utilizarChatMoviDesk");
    private final static QName _AlterarInformacoesEmpresaCobrancaPactoResponse_QNAME = new QName("http://adm.integracao.servicos/", "alterarInformacoesEmpresaCobrancaPactoResponse");
    private final static QName _DesvincularNotaFiscalEmitida_QNAME = new QName("http://adm.integracao.servicos/", "desvincularNotaFiscalEmitida");
    private final static QName _AtualizarDadosEmpresaResponse_QNAME = new QName("http://adm.integracao.servicos/", "atualizarDadosEmpresaResponse");
    private final static QName _ObterFotoResponseReturn_QNAME = new QName("", "return");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: servicos.integracao.adm.client
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link ExecutarProcessoLatitudeLongitude }
     * 
     */
    public ExecutarProcessoLatitudeLongitude createExecutarProcessoLatitudeLongitude() {
        return new ExecutarProcessoLatitudeLongitude();
    }

    /**
     * Create an instance of {@link GravarPrePagoDCCLancarAgendamentoFinan }
     * 
     */
    public GravarPrePagoDCCLancarAgendamentoFinan createGravarPrePagoDCCLancarAgendamentoFinan() {
        return new GravarPrePagoDCCLancarAgendamentoFinan();
    }

    /**
     * Create an instance of {@link ConsultarFavorecidoFinanceiro }
     * 
     */
    public ConsultarFavorecidoFinanceiro createConsultarFavorecidoFinanceiro() {
        return new ConsultarFavorecidoFinanceiro();
    }

    /**
     * Create an instance of {@link ConsultarFavorecidoFinanceiroResponse }
     * 
     */
    public ConsultarFavorecidoFinanceiroResponse createConsultarFavorecidoFinanceiroResponse() {
        return new ConsultarFavorecidoFinanceiroResponse();
    }

    /**
     * Create an instance of {@link AtualizarDadosEmpresa }
     * 
     */
    public AtualizarDadosEmpresa createAtualizarDadosEmpresa() {
        return new AtualizarDadosEmpresa();
    }

    /**
     * Create an instance of {@link GravarPosPagoDCCLancarAgendamentoFinan }
     * 
     */
    public GravarPosPagoDCCLancarAgendamentoFinan createGravarPosPagoDCCLancarAgendamentoFinan() {
        return new GravarPosPagoDCCLancarAgendamentoFinan();
    }

    /**
     * Create an instance of {@link UtilizarChatMoviDesk }
     * 
     */
    public UtilizarChatMoviDesk createUtilizarChatMoviDesk() {
        return new UtilizarChatMoviDesk();
    }

    /**
     * Create an instance of {@link AlterarInformacoesEmpresaCobrancaPactoResponse }
     * 
     */
    public AlterarInformacoesEmpresaCobrancaPactoResponse createAlterarInformacoesEmpresaCobrancaPactoResponse() {
        return new AlterarInformacoesEmpresaCobrancaPactoResponse();
    }

    /**
     * Create an instance of {@link DesvincularNotaFiscalEmitida }
     * 
     */
    public DesvincularNotaFiscalEmitida createDesvincularNotaFiscalEmitida() {
        return new DesvincularNotaFiscalEmitida();
    }

    /**
     * Create an instance of {@link AtualizarDadosEmpresaResponse }
     * 
     */
    public AtualizarDadosEmpresaResponse createAtualizarDadosEmpresaResponse() {
        return new AtualizarDadosEmpresaResponse();
    }

    /**
     * Create an instance of {@link ConsultarQtdCreditoUtilizadoResponse }
     * 
     */
    public ConsultarQtdCreditoUtilizadoResponse createConsultarQtdCreditoUtilizadoResponse() {
        return new ConsultarQtdCreditoUtilizadoResponse();
    }

    /**
     * Create an instance of {@link DesvincularNotaFiscalEmitidaResponse }
     * 
     */
    public DesvincularNotaFiscalEmitidaResponse createDesvincularNotaFiscalEmitidaResponse() {
        return new DesvincularNotaFiscalEmitidaResponse();
    }

    /**
     * Create an instance of {@link ExecutarProcessoLatitudeLongitudeResponse }
     * 
     */
    public ExecutarProcessoLatitudeLongitudeResponse createExecutarProcessoLatitudeLongitudeResponse() {
        return new ExecutarProcessoLatitudeLongitudeResponse();
    }

    /**
     * Create an instance of {@link LancarContaFinanceiro }
     * 
     */
    public LancarContaFinanceiro createLancarContaFinanceiro() {
        return new LancarContaFinanceiro();
    }

    /**
     * Create an instance of {@link UtilizarChatMoviDeskResponse }
     * 
     */
    public UtilizarChatMoviDeskResponse createUtilizarChatMoviDeskResponse() {
        return new UtilizarChatMoviDeskResponse();
    }

    /**
     * Create an instance of {@link ConsultarQtdCreditoUtilizado }
     * 
     */
    public ConsultarQtdCreditoUtilizado createConsultarQtdCreditoUtilizado() {
        return new ConsultarQtdCreditoUtilizado();
    }

    /**
     * Create an instance of {@link AjustarEmpresaParaIniciarPosPago }
     * 
     */
    public AjustarEmpresaParaIniciarPosPago createAjustarEmpresaParaIniciarPosPago() {
        return new AjustarEmpresaParaIniciarPosPago();
    }

    /**
     * Create an instance of {@link AjustarEmpresaParaIniciarPosPagoResponse }
     * 
     */
    public AjustarEmpresaParaIniciarPosPagoResponse createAjustarEmpresaParaIniciarPosPagoResponse() {
        return new AjustarEmpresaParaIniciarPosPagoResponse();
    }

    /**
     * Create an instance of {@link AlterarInformacoesEmpresaCobrancaPacto }
     * 
     */
    public AlterarInformacoesEmpresaCobrancaPacto createAlterarInformacoesEmpresaCobrancaPacto() {
        return new AlterarInformacoesEmpresaCobrancaPacto();
    }

    /**
     * Create an instance of {@link ObterUsuarios }
     * 
     */
    public ObterUsuarios createObterUsuarios() {
        return new ObterUsuarios();
    }

    /**
     * Create an instance of {@link GravarPosPagoDCCLancarAgendamentoFinanResponse }
     * 
     */
    public GravarPosPagoDCCLancarAgendamentoFinanResponse createGravarPosPagoDCCLancarAgendamentoFinanResponse() {
        return new GravarPosPagoDCCLancarAgendamentoFinanResponse();
    }

    /**
     * Create an instance of {@link UtilizarMoviDesk }
     * 
     */
    public UtilizarMoviDesk createUtilizarMoviDesk() {
        return new UtilizarMoviDesk();
    }

    /**
     * Create an instance of {@link ObterEmpresasUsuarioResponse }
     * 
     */
    public ObterEmpresasUsuarioResponse createObterEmpresasUsuarioResponse() {
        return new ObterEmpresasUsuarioResponse();
    }

    /**
     * Create an instance of {@link ObterFoto }
     * 
     */
    public ObterFoto createObterFoto() {
        return new ObterFoto();
    }

    /**
     * Create an instance of {@link GravarPrePagoDCCLancarAgendamentoFinanResponse }
     * 
     */
    public GravarPrePagoDCCLancarAgendamentoFinanResponse createGravarPrePagoDCCLancarAgendamentoFinanResponse() {
        return new GravarPrePagoDCCLancarAgendamentoFinanResponse();
    }

    /**
     * Create an instance of {@link ObterEmpresasUsuario }
     * 
     */
    public ObterEmpresasUsuario createObterEmpresasUsuario() {
        return new ObterEmpresasUsuario();
    }

    /**
     * Create an instance of {@link ObterEmpresasComSituacao }
     * 
     */
    public ObterEmpresasComSituacao createObterEmpresasComSituacao() {
        return new ObterEmpresasComSituacao();
    }

    /**
     * Create an instance of {@link ObterEmpresas }
     * 
     */
    public ObterEmpresas createObterEmpresas() {
        return new ObterEmpresas();
    }

    /**
     * Create an instance of {@link LancarContaFinanceiroResponse }
     * 
     */
    public LancarContaFinanceiroResponse createLancarContaFinanceiroResponse() {
        return new LancarContaFinanceiroResponse();
    }

    /**
     * Create an instance of {@link UtilizarMoviDeskResponse }
     * 
     */
    public UtilizarMoviDeskResponse createUtilizarMoviDeskResponse() {
        return new UtilizarMoviDeskResponse();
    }

    /**
     * Create an instance of {@link ObterEmpresaResponse }
     * 
     */
    public ObterEmpresaResponse createObterEmpresaResponse() {
        return new ObterEmpresaResponse();
    }

    /**
     * Create an instance of {@link AlterarCreditoDCCResponse }
     * 
     */
    public AlterarCreditoDCCResponse createAlterarCreditoDCCResponse() {
        return new AlterarCreditoDCCResponse();
    }

    /**
     * Create an instance of {@link AlterarGrupoChatMovidesk }
     * 
     */
    public AlterarGrupoChatMovidesk createAlterarGrupoChatMovidesk() {
        return new AlterarGrupoChatMovidesk();
    }

    /**
     * Create an instance of {@link ObterUsuariosResponse }
     * 
     */
    public ObterUsuariosResponse createObterUsuariosResponse() {
        return new ObterUsuariosResponse();
    }

    /**
     * Create an instance of {@link ObterEmpresa }
     * 
     */
    public ObterEmpresa createObterEmpresa() {
        return new ObterEmpresa();
    }

    /**
     * Create an instance of {@link AlterarGrupoChatMovideskResponse }
     * 
     */
    public AlterarGrupoChatMovideskResponse createAlterarGrupoChatMovideskResponse() {
        return new AlterarGrupoChatMovideskResponse();
    }

    /**
     * Create an instance of {@link ObterFotoResponse }
     * 
     */
    public ObterFotoResponse createObterFotoResponse() {
        return new ObterFotoResponse();
    }

    /**
     * Create an instance of {@link ObterEmpresasResponse }
     * 
     */
    public ObterEmpresasResponse createObterEmpresasResponse() {
        return new ObterEmpresasResponse();
    }

    /**
     * Create an instance of {@link ObterEmpresasComSituacaoResponse }
     * 
     */
    public ObterEmpresasComSituacaoResponse createObterEmpresasComSituacaoResponse() {
        return new ObterEmpresasComSituacaoResponse();
    }

    /**
     * Create an instance of {@link AlterarCreditoDCC }
     * 
     */
    public AlterarCreditoDCC createAlterarCreditoDCC() {
        return new AlterarCreditoDCC();
    }

    /**
     * Create an instance of {@link EmpresaWS }
     * 
     */
    public EmpresaWS createEmpresaWS() {
        return new EmpresaWS();
    }

    /**
     * Create an instance of {@link UsuarioTO }
     * 
     */
    public UsuarioTO createUsuarioTO() {
        return new UsuarioTO();
    }

    /**
     * Create an instance of {@link SuperTO }
     * 
     */
    public SuperTO createSuperTO() {
        return new SuperTO();
    }

    /**
     * Create an instance of {@link EmpresaTO }
     * 
     */
    public EmpresaTO createEmpresaTO() {
        return new EmpresaTO();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AlterarCreditoDCCResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://adm.integracao.servicos/", name = "alterarCreditoDCCResponse")
    public JAXBElement<AlterarCreditoDCCResponse> createAlterarCreditoDCCResponse(AlterarCreditoDCCResponse value) {
        return new JAXBElement<AlterarCreditoDCCResponse>(_AlterarCreditoDCCResponse_QNAME, AlterarCreditoDCCResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AlterarGrupoChatMovidesk }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://adm.integracao.servicos/", name = "alterarGrupoChatMovidesk")
    public JAXBElement<AlterarGrupoChatMovidesk> createAlterarGrupoChatMovidesk(AlterarGrupoChatMovidesk value) {
        return new JAXBElement<AlterarGrupoChatMovidesk>(_AlterarGrupoChatMovidesk_QNAME, AlterarGrupoChatMovidesk.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterUsuariosResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://adm.integracao.servicos/", name = "obterUsuariosResponse")
    public JAXBElement<ObterUsuariosResponse> createObterUsuariosResponse(ObterUsuariosResponse value) {
        return new JAXBElement<ObterUsuariosResponse>(_ObterUsuariosResponse_QNAME, ObterUsuariosResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterEmpresa }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://adm.integracao.servicos/", name = "obterEmpresa")
    public JAXBElement<ObterEmpresa> createObterEmpresa(ObterEmpresa value) {
        return new JAXBElement<ObterEmpresa>(_ObterEmpresa_QNAME, ObterEmpresa.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AlterarGrupoChatMovideskResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://adm.integracao.servicos/", name = "alterarGrupoChatMovideskResponse")
    public JAXBElement<AlterarGrupoChatMovideskResponse> createAlterarGrupoChatMovideskResponse(AlterarGrupoChatMovideskResponse value) {
        return new JAXBElement<AlterarGrupoChatMovideskResponse>(_AlterarGrupoChatMovideskResponse_QNAME, AlterarGrupoChatMovideskResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterFotoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://adm.integracao.servicos/", name = "obterFotoResponse")
    public JAXBElement<ObterFotoResponse> createObterFotoResponse(ObterFotoResponse value) {
        return new JAXBElement<ObterFotoResponse>(_ObterFotoResponse_QNAME, ObterFotoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterEmpresasResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://adm.integracao.servicos/", name = "obterEmpresasResponse")
    public JAXBElement<ObterEmpresasResponse> createObterEmpresasResponse(ObterEmpresasResponse value) {
        return new JAXBElement<ObterEmpresasResponse>(_ObterEmpresasResponse_QNAME, ObterEmpresasResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterEmpresasComSituacaoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://adm.integracao.servicos/", name = "obterEmpresasComSituacaoResponse")
    public JAXBElement<ObterEmpresasComSituacaoResponse> createObterEmpresasComSituacaoResponse(ObterEmpresasComSituacaoResponse value) {
        return new JAXBElement<ObterEmpresasComSituacaoResponse>(_ObterEmpresasComSituacaoResponse_QNAME, ObterEmpresasComSituacaoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AlterarCreditoDCC }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://adm.integracao.servicos/", name = "alterarCreditoDCC")
    public JAXBElement<AlterarCreditoDCC> createAlterarCreditoDCC(AlterarCreditoDCC value) {
        return new JAXBElement<AlterarCreditoDCC>(_AlterarCreditoDCC_QNAME, AlterarCreditoDCC.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterEmpresasUsuarioResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://adm.integracao.servicos/", name = "obterEmpresasUsuarioResponse")
    public JAXBElement<ObterEmpresasUsuarioResponse> createObterEmpresasUsuarioResponse(ObterEmpresasUsuarioResponse value) {
        return new JAXBElement<ObterEmpresasUsuarioResponse>(_ObterEmpresasUsuarioResponse_QNAME, ObterEmpresasUsuarioResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterFoto }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://adm.integracao.servicos/", name = "obterFoto")
    public JAXBElement<ObterFoto> createObterFoto(ObterFoto value) {
        return new JAXBElement<ObterFoto>(_ObterFoto_QNAME, ObterFoto.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GravarPrePagoDCCLancarAgendamentoFinanResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://adm.integracao.servicos/", name = "gravarPrePagoDCCLancarAgendamentoFinanResponse")
    public JAXBElement<GravarPrePagoDCCLancarAgendamentoFinanResponse> createGravarPrePagoDCCLancarAgendamentoFinanResponse(GravarPrePagoDCCLancarAgendamentoFinanResponse value) {
        return new JAXBElement<GravarPrePagoDCCLancarAgendamentoFinanResponse>(_GravarPrePagoDCCLancarAgendamentoFinanResponse_QNAME, GravarPrePagoDCCLancarAgendamentoFinanResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterEmpresasUsuario }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://adm.integracao.servicos/", name = "obterEmpresasUsuario")
    public JAXBElement<ObterEmpresasUsuario> createObterEmpresasUsuario(ObterEmpresasUsuario value) {
        return new JAXBElement<ObterEmpresasUsuario>(_ObterEmpresasUsuario_QNAME, ObterEmpresasUsuario.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterEmpresasComSituacao }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://adm.integracao.servicos/", name = "obterEmpresasComSituacao")
    public JAXBElement<ObterEmpresasComSituacao> createObterEmpresasComSituacao(ObterEmpresasComSituacao value) {
        return new JAXBElement<ObterEmpresasComSituacao>(_ObterEmpresasComSituacao_QNAME, ObterEmpresasComSituacao.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterEmpresas }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://adm.integracao.servicos/", name = "obterEmpresas")
    public JAXBElement<ObterEmpresas> createObterEmpresas(ObterEmpresas value) {
        return new JAXBElement<ObterEmpresas>(_ObterEmpresas_QNAME, ObterEmpresas.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link LancarContaFinanceiroResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://adm.integracao.servicos/", name = "lancarContaFinanceiroResponse")
    public JAXBElement<LancarContaFinanceiroResponse> createLancarContaFinanceiroResponse(LancarContaFinanceiroResponse value) {
        return new JAXBElement<LancarContaFinanceiroResponse>(_LancarContaFinanceiroResponse_QNAME, LancarContaFinanceiroResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link UtilizarMoviDeskResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://adm.integracao.servicos/", name = "utilizarMoviDeskResponse")
    public JAXBElement<UtilizarMoviDeskResponse> createUtilizarMoviDeskResponse(UtilizarMoviDeskResponse value) {
        return new JAXBElement<UtilizarMoviDeskResponse>(_UtilizarMoviDeskResponse_QNAME, UtilizarMoviDeskResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterEmpresaResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://adm.integracao.servicos/", name = "obterEmpresaResponse")
    public JAXBElement<ObterEmpresaResponse> createObterEmpresaResponse(ObterEmpresaResponse value) {
        return new JAXBElement<ObterEmpresaResponse>(_ObterEmpresaResponse_QNAME, ObterEmpresaResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarQtdCreditoUtilizadoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://adm.integracao.servicos/", name = "consultarQtdCreditoUtilizadoResponse")
    public JAXBElement<ConsultarQtdCreditoUtilizadoResponse> createConsultarQtdCreditoUtilizadoResponse(ConsultarQtdCreditoUtilizadoResponse value) {
        return new JAXBElement<ConsultarQtdCreditoUtilizadoResponse>(_ConsultarQtdCreditoUtilizadoResponse_QNAME, ConsultarQtdCreditoUtilizadoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link DesvincularNotaFiscalEmitidaResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://adm.integracao.servicos/", name = "desvincularNotaFiscalEmitidaResponse")
    public JAXBElement<DesvincularNotaFiscalEmitidaResponse> createDesvincularNotaFiscalEmitidaResponse(DesvincularNotaFiscalEmitidaResponse value) {
        return new JAXBElement<DesvincularNotaFiscalEmitidaResponse>(_DesvincularNotaFiscalEmitidaResponse_QNAME, DesvincularNotaFiscalEmitidaResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ExecutarProcessoLatitudeLongitudeResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://adm.integracao.servicos/", name = "executarProcessoLatitudeLongitudeResponse")
    public JAXBElement<ExecutarProcessoLatitudeLongitudeResponse> createExecutarProcessoLatitudeLongitudeResponse(ExecutarProcessoLatitudeLongitudeResponse value) {
        return new JAXBElement<ExecutarProcessoLatitudeLongitudeResponse>(_ExecutarProcessoLatitudeLongitudeResponse_QNAME, ExecutarProcessoLatitudeLongitudeResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link LancarContaFinanceiro }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://adm.integracao.servicos/", name = "lancarContaFinanceiro")
    public JAXBElement<LancarContaFinanceiro> createLancarContaFinanceiro(LancarContaFinanceiro value) {
        return new JAXBElement<LancarContaFinanceiro>(_LancarContaFinanceiro_QNAME, LancarContaFinanceiro.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link UtilizarChatMoviDeskResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://adm.integracao.servicos/", name = "utilizarChatMoviDeskResponse")
    public JAXBElement<UtilizarChatMoviDeskResponse> createUtilizarChatMoviDeskResponse(UtilizarChatMoviDeskResponse value) {
        return new JAXBElement<UtilizarChatMoviDeskResponse>(_UtilizarChatMoviDeskResponse_QNAME, UtilizarChatMoviDeskResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarQtdCreditoUtilizado }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://adm.integracao.servicos/", name = "consultarQtdCreditoUtilizado")
    public JAXBElement<ConsultarQtdCreditoUtilizado> createConsultarQtdCreditoUtilizado(ConsultarQtdCreditoUtilizado value) {
        return new JAXBElement<ConsultarQtdCreditoUtilizado>(_ConsultarQtdCreditoUtilizado_QNAME, ConsultarQtdCreditoUtilizado.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AjustarEmpresaParaIniciarPosPago }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://adm.integracao.servicos/", name = "ajustarEmpresaParaIniciarPosPago")
    public JAXBElement<AjustarEmpresaParaIniciarPosPago> createAjustarEmpresaParaIniciarPosPago(AjustarEmpresaParaIniciarPosPago value) {
        return new JAXBElement<AjustarEmpresaParaIniciarPosPago>(_AjustarEmpresaParaIniciarPosPago_QNAME, AjustarEmpresaParaIniciarPosPago.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AjustarEmpresaParaIniciarPosPagoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://adm.integracao.servicos/", name = "ajustarEmpresaParaIniciarPosPagoResponse")
    public JAXBElement<AjustarEmpresaParaIniciarPosPagoResponse> createAjustarEmpresaParaIniciarPosPagoResponse(AjustarEmpresaParaIniciarPosPagoResponse value) {
        return new JAXBElement<AjustarEmpresaParaIniciarPosPagoResponse>(_AjustarEmpresaParaIniciarPosPagoResponse_QNAME, AjustarEmpresaParaIniciarPosPagoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AlterarInformacoesEmpresaCobrancaPacto }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://adm.integracao.servicos/", name = "alterarInformacoesEmpresaCobrancaPacto")
    public JAXBElement<AlterarInformacoesEmpresaCobrancaPacto> createAlterarInformacoesEmpresaCobrancaPacto(AlterarInformacoesEmpresaCobrancaPacto value) {
        return new JAXBElement<AlterarInformacoesEmpresaCobrancaPacto>(_AlterarInformacoesEmpresaCobrancaPacto_QNAME, AlterarInformacoesEmpresaCobrancaPacto.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterUsuarios }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://adm.integracao.servicos/", name = "obterUsuarios")
    public JAXBElement<ObterUsuarios> createObterUsuarios(ObterUsuarios value) {
        return new JAXBElement<ObterUsuarios>(_ObterUsuarios_QNAME, ObterUsuarios.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GravarPosPagoDCCLancarAgendamentoFinanResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://adm.integracao.servicos/", name = "gravarPosPagoDCCLancarAgendamentoFinanResponse")
    public JAXBElement<GravarPosPagoDCCLancarAgendamentoFinanResponse> createGravarPosPagoDCCLancarAgendamentoFinanResponse(GravarPosPagoDCCLancarAgendamentoFinanResponse value) {
        return new JAXBElement<GravarPosPagoDCCLancarAgendamentoFinanResponse>(_GravarPosPagoDCCLancarAgendamentoFinanResponse_QNAME, GravarPosPagoDCCLancarAgendamentoFinanResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link UtilizarMoviDesk }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://adm.integracao.servicos/", name = "utilizarMoviDesk")
    public JAXBElement<UtilizarMoviDesk> createUtilizarMoviDesk(UtilizarMoviDesk value) {
        return new JAXBElement<UtilizarMoviDesk>(_UtilizarMoviDesk_QNAME, UtilizarMoviDesk.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ExecutarProcessoLatitudeLongitude }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://adm.integracao.servicos/", name = "executarProcessoLatitudeLongitude")
    public JAXBElement<ExecutarProcessoLatitudeLongitude> createExecutarProcessoLatitudeLongitude(ExecutarProcessoLatitudeLongitude value) {
        return new JAXBElement<ExecutarProcessoLatitudeLongitude>(_ExecutarProcessoLatitudeLongitude_QNAME, ExecutarProcessoLatitudeLongitude.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GravarPrePagoDCCLancarAgendamentoFinan }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://adm.integracao.servicos/", name = "gravarPrePagoDCCLancarAgendamentoFinan")
    public JAXBElement<GravarPrePagoDCCLancarAgendamentoFinan> createGravarPrePagoDCCLancarAgendamentoFinan(GravarPrePagoDCCLancarAgendamentoFinan value) {
        return new JAXBElement<GravarPrePagoDCCLancarAgendamentoFinan>(_GravarPrePagoDCCLancarAgendamentoFinan_QNAME, GravarPrePagoDCCLancarAgendamentoFinan.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarFavorecidoFinanceiro }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://adm.integracao.servicos/", name = "consultarFavorecidoFinanceiro")
    public JAXBElement<ConsultarFavorecidoFinanceiro> createConsultarFavorecidoFinanceiro(ConsultarFavorecidoFinanceiro value) {
        return new JAXBElement<ConsultarFavorecidoFinanceiro>(_ConsultarFavorecidoFinanceiro_QNAME, ConsultarFavorecidoFinanceiro.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarFavorecidoFinanceiroResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://adm.integracao.servicos/", name = "consultarFavorecidoFinanceiroResponse")
    public JAXBElement<ConsultarFavorecidoFinanceiroResponse> createConsultarFavorecidoFinanceiroResponse(ConsultarFavorecidoFinanceiroResponse value) {
        return new JAXBElement<ConsultarFavorecidoFinanceiroResponse>(_ConsultarFavorecidoFinanceiroResponse_QNAME, ConsultarFavorecidoFinanceiroResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AtualizarDadosEmpresa }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://adm.integracao.servicos/", name = "atualizarDadosEmpresa")
    public JAXBElement<AtualizarDadosEmpresa> createAtualizarDadosEmpresa(AtualizarDadosEmpresa value) {
        return new JAXBElement<AtualizarDadosEmpresa>(_AtualizarDadosEmpresa_QNAME, AtualizarDadosEmpresa.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GravarPosPagoDCCLancarAgendamentoFinan }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://adm.integracao.servicos/", name = "gravarPosPagoDCCLancarAgendamentoFinan")
    public JAXBElement<GravarPosPagoDCCLancarAgendamentoFinan> createGravarPosPagoDCCLancarAgendamentoFinan(GravarPosPagoDCCLancarAgendamentoFinan value) {
        return new JAXBElement<GravarPosPagoDCCLancarAgendamentoFinan>(_GravarPosPagoDCCLancarAgendamentoFinan_QNAME, GravarPosPagoDCCLancarAgendamentoFinan.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link UtilizarChatMoviDesk }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://adm.integracao.servicos/", name = "utilizarChatMoviDesk")
    public JAXBElement<UtilizarChatMoviDesk> createUtilizarChatMoviDesk(UtilizarChatMoviDesk value) {
        return new JAXBElement<UtilizarChatMoviDesk>(_UtilizarChatMoviDesk_QNAME, UtilizarChatMoviDesk.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AlterarInformacoesEmpresaCobrancaPactoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://adm.integracao.servicos/", name = "alterarInformacoesEmpresaCobrancaPactoResponse")
    public JAXBElement<AlterarInformacoesEmpresaCobrancaPactoResponse> createAlterarInformacoesEmpresaCobrancaPactoResponse(AlterarInformacoesEmpresaCobrancaPactoResponse value) {
        return new JAXBElement<AlterarInformacoesEmpresaCobrancaPactoResponse>(_AlterarInformacoesEmpresaCobrancaPactoResponse_QNAME, AlterarInformacoesEmpresaCobrancaPactoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link DesvincularNotaFiscalEmitida }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://adm.integracao.servicos/", name = "desvincularNotaFiscalEmitida")
    public JAXBElement<DesvincularNotaFiscalEmitida> createDesvincularNotaFiscalEmitida(DesvincularNotaFiscalEmitida value) {
        return new JAXBElement<DesvincularNotaFiscalEmitida>(_DesvincularNotaFiscalEmitida_QNAME, DesvincularNotaFiscalEmitida.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AtualizarDadosEmpresaResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://adm.integracao.servicos/", name = "atualizarDadosEmpresaResponse")
    public JAXBElement<AtualizarDadosEmpresaResponse> createAtualizarDadosEmpresaResponse(AtualizarDadosEmpresaResponse value) {
        return new JAXBElement<AtualizarDadosEmpresaResponse>(_AtualizarDadosEmpresaResponse_QNAME, AtualizarDadosEmpresaResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link byte[]}{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "", name = "return", scope = ObterFotoResponse.class)
    public JAXBElement<byte[]> createObterFotoResponseReturn(byte[] value) {
        return new JAXBElement<byte[]>(_ObterFotoResponseReturn_QNAME, byte[].class, ObterFotoResponse.class, ((byte[]) value));
    }

}
