
package servicos.integracao.adm.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de gravarPosPagoDCCLancarAgendamentoFinan complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType name="gravarPosPagoDCCLancarAgendamentoFinan">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="key" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="codigoEmpresa" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="tipoCobrancaPacto" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="qtdCredito" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *         &lt;element name="qtdParcelas" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="valorTotal" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/>
 *         &lt;element name="gerarNota" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="nomeUsuarioOAMD" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="justificativa" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="gerarCobrancaFinanceiro" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "gravarPosPagoDCCLancarAgendamentoFinan", propOrder = {
    "key",
    "codigoEmpresa",
    "tipoCobrancaPacto",
    "qtdCredito",
    "qtdParcelas",
    "valorTotal",
    "gerarNota",
    "nomeUsuarioOAMD",
    "justificativa",
    "gerarCobrancaFinanceiro"
})
public class GravarPosPagoDCCLancarAgendamentoFinan {

    protected String key;
    protected Integer codigoEmpresa;
    protected Integer tipoCobrancaPacto;
    protected int qtdCredito;
    protected Integer qtdParcelas;
    protected Double valorTotal;
    protected boolean gerarNota;
    protected String nomeUsuarioOAMD;
    protected String justificativa;
    protected boolean gerarCobrancaFinanceiro;

    /**
     * Obtém o valor da propriedade key.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKey() {
        return key;
    }

    /**
     * Define o valor da propriedade key.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKey(String value) {
        this.key = value;
    }

    /**
     * Obtém o valor da propriedade codigoEmpresa.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCodigoEmpresa() {
        return codigoEmpresa;
    }

    /**
     * Define o valor da propriedade codigoEmpresa.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCodigoEmpresa(Integer value) {
        this.codigoEmpresa = value;
    }

    /**
     * Obtém o valor da propriedade tipoCobrancaPacto.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getTipoCobrancaPacto() {
        return tipoCobrancaPacto;
    }

    /**
     * Define o valor da propriedade tipoCobrancaPacto.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setTipoCobrancaPacto(Integer value) {
        this.tipoCobrancaPacto = value;
    }

    /**
     * Obtém o valor da propriedade qtdCredito.
     * 
     */
    public int getQtdCredito() {
        return qtdCredito;
    }

    /**
     * Define o valor da propriedade qtdCredito.
     * 
     */
    public void setQtdCredito(int value) {
        this.qtdCredito = value;
    }

    /**
     * Obtém o valor da propriedade qtdParcelas.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getQtdParcelas() {
        return qtdParcelas;
    }

    /**
     * Define o valor da propriedade qtdParcelas.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setQtdParcelas(Integer value) {
        this.qtdParcelas = value;
    }

    /**
     * Obtém o valor da propriedade valorTotal.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getValorTotal() {
        return valorTotal;
    }

    /**
     * Define o valor da propriedade valorTotal.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setValorTotal(Double value) {
        this.valorTotal = value;
    }

    /**
     * Obtém o valor da propriedade gerarNota.
     * 
     */
    public boolean isGerarNota() {
        return gerarNota;
    }

    /**
     * Define o valor da propriedade gerarNota.
     * 
     */
    public void setGerarNota(boolean value) {
        this.gerarNota = value;
    }

    /**
     * Obtém o valor da propriedade nomeUsuarioOAMD.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNomeUsuarioOAMD() {
        return nomeUsuarioOAMD;
    }

    /**
     * Define o valor da propriedade nomeUsuarioOAMD.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNomeUsuarioOAMD(String value) {
        this.nomeUsuarioOAMD = value;
    }

    /**
     * Obtém o valor da propriedade justificativa.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getJustificativa() {
        return justificativa;
    }

    /**
     * Define o valor da propriedade justificativa.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setJustificativa(String value) {
        this.justificativa = value;
    }

    /**
     * Obtém o valor da propriedade gerarCobrancaFinanceiro.
     * 
     */
    public boolean isGerarCobrancaFinanceiro() {
        return gerarCobrancaFinanceiro;
    }

    /**
     * Define o valor da propriedade gerarCobrancaFinanceiro.
     * 
     */
    public void setGerarCobrancaFinanceiro(boolean value) {
        this.gerarCobrancaFinanceiro = value;
    }

}
