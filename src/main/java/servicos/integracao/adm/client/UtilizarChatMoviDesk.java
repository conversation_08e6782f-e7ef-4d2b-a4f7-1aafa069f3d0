
package servicos.integracao.adm.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de utilizarChatMoviDesk complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType name="utilizarChatMoviDesk">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="chave" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="deveUtilizarChatMoviDesk" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "utilizarChatMoviDesk", propOrder = {
    "chave",
    "deveUtilizarChatMoviDesk"
})
public class UtilizarChatMoviDesk {

    protected String chave;
    protected String deveUtilizarChatMoviDesk;

    /**
     * Obtém o valor da propriedade chave.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getChave() {
        return chave;
    }

    /**
     * Define o valor da propriedade chave.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setChave(String value) {
        this.chave = value;
    }

    /**
     * Obtém o valor da propriedade deveUtilizarChatMoviDesk.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDeveUtilizarChatMoviDesk() {
        return deveUtilizarChatMoviDesk;
    }

    /**
     * Define o valor da propriedade deveUtilizarChatMoviDesk.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDeveUtilizarChatMoviDesk(String value) {
        this.deveUtilizarChatMoviDesk = value;
    }

}
