
package servicos.integracao.adm.client;

import java.util.List;
import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.6b21 
 * Generated source version: 2.1
 * 
 */
@WebService(name = "AdmWS", targetNamespace = "http://adm.integracao.servicos/")
@XmlSeeAlso({
    ObjectFactory.class
})
public interface AdmWS {


    /**
     * 
     * @param key
     * @return
     *     returns servicos.integracao.adm.client.EmpresaTO
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "obterEmpresa", targetNamespace = "http://adm.integracao.servicos/", className = "servicos.integracao.adm.client.ObterEmpresa")
    @ResponseWrapper(localName = "obterEmpresaResponse", targetNamespace = "http://adm.integracao.servicos/", className = "servicos.integracao.adm.client.ObterEmpresaResponse")
    public EmpresaTO obterEmpresa(
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param key
     * @param usuarioZW
     * @return
     *     returns java.util.List<servicos.integracao.adm.client.EmpresaWS>
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "obterEmpresasUsuario", targetNamespace = "http://adm.integracao.servicos/", className = "servicos.integracao.adm.client.ObterEmpresasUsuario")
    @ResponseWrapper(localName = "obterEmpresasUsuarioResponse", targetNamespace = "http://adm.integracao.servicos/", className = "servicos.integracao.adm.client.ObterEmpresasUsuarioResponse")
    public List<EmpresaWS> obterEmpresasUsuario(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "usuarioZW", targetNamespace = "")
        Integer usuarioZW);

    /**
     * 
     * @param key
     * @return
     *     returns java.util.List<servicos.integracao.adm.client.EmpresaWS>
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "obterEmpresas", targetNamespace = "http://adm.integracao.servicos/", className = "servicos.integracao.adm.client.ObterEmpresas")
    @ResponseWrapper(localName = "obterEmpresasResponse", targetNamespace = "http://adm.integracao.servicos/", className = "servicos.integracao.adm.client.ObterEmpresasResponse")
    public List<EmpresaWS> obterEmpresas(
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param ativa
     * @param key
     * @return
     *     returns java.util.List<servicos.integracao.adm.client.EmpresaWS>
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "obterEmpresasComSituacao", targetNamespace = "http://adm.integracao.servicos/", className = "servicos.integracao.adm.client.ObterEmpresasComSituacao")
    @ResponseWrapper(localName = "obterEmpresasComSituacaoResponse", targetNamespace = "http://adm.integracao.servicos/", className = "servicos.integracao.adm.client.ObterEmpresasComSituacaoResponse")
    public List<EmpresaWS> obterEmpresasComSituacao(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "ativa", targetNamespace = "")
        Boolean ativa);

    /**
     * 
     * @param key
     * @return
     *     returns java.util.List<servicos.integracao.adm.client.UsuarioTO>
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "obterUsuarios", targetNamespace = "http://adm.integracao.servicos/", className = "servicos.integracao.adm.client.ObterUsuarios")
    @ResponseWrapper(localName = "obterUsuariosResponse", targetNamespace = "http://adm.integracao.servicos/", className = "servicos.integracao.adm.client.ObterUsuariosResponse")
    public List<UsuarioTO> obterUsuarios(
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param codEmpresa
     * @param key
     * @return
     *     returns byte[]
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "obterFoto", targetNamespace = "http://adm.integracao.servicos/", className = "servicos.integracao.adm.client.ObterFoto")
    @ResponseWrapper(localName = "obterFotoResponse", targetNamespace = "http://adm.integracao.servicos/", className = "servicos.integracao.adm.client.ObterFotoResponse")
    public byte[] obterFoto(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codEmpresa", targetNamespace = "")
        Integer codEmpresa);

    /**
     * 
     * @param codigoEmpresa
     * @param quantidade
     * @param key
     * @return
     *     returns java.lang.Integer
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "alterarCreditoDCC", targetNamespace = "http://adm.integracao.servicos/", className = "servicos.integracao.adm.client.AlterarCreditoDCC")
    @ResponseWrapper(localName = "alterarCreditoDCCResponse", targetNamespace = "http://adm.integracao.servicos/", className = "servicos.integracao.adm.client.AlterarCreditoDCCResponse")
    public Integer alterarCreditoDCC(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codigoEmpresa", targetNamespace = "")
        Integer codigoEmpresa,
        @WebParam(name = "quantidade", targetNamespace = "")
        int quantidade);

    /**
     * 
     * @param codigoEmpresa
     * @param nomeUsuarioOAMD
     * @param gerarNota
     * @param qtdParcelas
     * @param valorTotal
     * @param justificativa
     * @param gerarCobrancaFinanceiro
     * @param quantidade
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "gravarPrePagoDCCLancarAgendamentoFinan", targetNamespace = "http://adm.integracao.servicos/", className = "servicos.integracao.adm.client.GravarPrePagoDCCLancarAgendamentoFinan")
    @ResponseWrapper(localName = "gravarPrePagoDCCLancarAgendamentoFinanResponse", targetNamespace = "http://adm.integracao.servicos/", className = "servicos.integracao.adm.client.GravarPrePagoDCCLancarAgendamentoFinanResponse")
    public String gravarPrePagoDCCLancarAgendamentoFinan(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codigoEmpresa", targetNamespace = "")
        Integer codigoEmpresa,
        @WebParam(name = "quantidade", targetNamespace = "")
        int quantidade,
        @WebParam(name = "qtdParcelas", targetNamespace = "")
        Integer qtdParcelas,
        @WebParam(name = "valorTotal", targetNamespace = "")
        Double valorTotal,
        @WebParam(name = "gerarNota", targetNamespace = "")
        boolean gerarNota,
        @WebParam(name = "nomeUsuarioOAMD", targetNamespace = "")
        String nomeUsuarioOAMD,
        @WebParam(name = "justificativa", targetNamespace = "")
        String justificativa,
        @WebParam(name = "gerarCobrancaFinanceiro", targetNamespace = "")
        boolean gerarCobrancaFinanceiro);

    /**
     * 
     * @param codigoEmpresa
     * @param nomeUsuarioOAMD
     * @param gerarNota
     * @param qtdParcelas
     * @param valorTotal
     * @param justificativa
     * @param tipoCobrancaPacto
     * @param gerarCobrancaFinanceiro
     * @param key
     * @param qtdCredito
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "gravarPosPagoDCCLancarAgendamentoFinan", targetNamespace = "http://adm.integracao.servicos/", className = "servicos.integracao.adm.client.GravarPosPagoDCCLancarAgendamentoFinan")
    @ResponseWrapper(localName = "gravarPosPagoDCCLancarAgendamentoFinanResponse", targetNamespace = "http://adm.integracao.servicos/", className = "servicos.integracao.adm.client.GravarPosPagoDCCLancarAgendamentoFinanResponse")
    public String gravarPosPagoDCCLancarAgendamentoFinan(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codigoEmpresa", targetNamespace = "")
        Integer codigoEmpresa,
        @WebParam(name = "tipoCobrancaPacto", targetNamespace = "")
        Integer tipoCobrancaPacto,
        @WebParam(name = "qtdCredito", targetNamespace = "")
        int qtdCredito,
        @WebParam(name = "qtdParcelas", targetNamespace = "")
        Integer qtdParcelas,
        @WebParam(name = "valorTotal", targetNamespace = "")
        Double valorTotal,
        @WebParam(name = "gerarNota", targetNamespace = "")
        boolean gerarNota,
        @WebParam(name = "nomeUsuarioOAMD", targetNamespace = "")
        String nomeUsuarioOAMD,
        @WebParam(name = "justificativa", targetNamespace = "")
        String justificativa,
        @WebParam(name = "gerarCobrancaFinanceiro", targetNamespace = "")
        boolean gerarCobrancaFinanceiro);

    /**
     * 
     * @param valorCreditoPacto
     * @param gerarNotaFiscalCobrancaPacto
     * @param celularClienteCobrancaPacto
     * @param tipoCobrancaPacto
     * @param nomeClienteCobrancaPacto
     * @param qtdDiasFechamentoCobrancaPacto
     * @param qtdCreditoRenovarPrePagoCobrancaPacto
     * @param qtdParcelasCobrancaPacto
     * @param codigoEmpresa
     * @param emailClienteCobrancaPacto
     * @param nomeUsuarioOAMD
     * @param key
     * @param gerarCobrancaAutomaticaPacto
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "alterarInformacoesEmpresaCobrancaPacto", targetNamespace = "http://adm.integracao.servicos/", className = "servicos.integracao.adm.client.AlterarInformacoesEmpresaCobrancaPacto")
    @ResponseWrapper(localName = "alterarInformacoesEmpresaCobrancaPactoResponse", targetNamespace = "http://adm.integracao.servicos/", className = "servicos.integracao.adm.client.AlterarInformacoesEmpresaCobrancaPactoResponse")
    public String alterarInformacoesEmpresaCobrancaPacto(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codigoEmpresa", targetNamespace = "")
        Integer codigoEmpresa,
        @WebParam(name = "tipoCobrancaPacto", targetNamespace = "")
        Integer tipoCobrancaPacto,
        @WebParam(name = "gerarCobrancaAutomaticaPacto", targetNamespace = "")
        boolean gerarCobrancaAutomaticaPacto,
        @WebParam(name = "qtdDiasFechamentoCobrancaPacto", targetNamespace = "")
        Integer qtdDiasFechamentoCobrancaPacto,
        @WebParam(name = "valorCreditoPacto", targetNamespace = "")
        Double valorCreditoPacto,
        @WebParam(name = "gerarNotaFiscalCobrancaPacto", targetNamespace = "")
        boolean gerarNotaFiscalCobrancaPacto,
        @WebParam(name = "qtdParcelasCobrancaPacto", targetNamespace = "")
        Integer qtdParcelasCobrancaPacto,
        @WebParam(name = "qtdCreditoRenovarPrePagoCobrancaPacto", targetNamespace = "")
        Integer qtdCreditoRenovarPrePagoCobrancaPacto,
        @WebParam(name = "nomeClienteCobrancaPacto", targetNamespace = "")
        String nomeClienteCobrancaPacto,
        @WebParam(name = "emailClienteCobrancaPacto", targetNamespace = "")
        String emailClienteCobrancaPacto,
        @WebParam(name = "celularClienteCobrancaPacto", targetNamespace = "")
        String celularClienteCobrancaPacto,
        @WebParam(name = "nomeUsuarioOAMD", targetNamespace = "")
        String nomeUsuarioOAMD);

    /**
     * 
     * @param codigoEmpresa
     * @param tipoCobrancaPacto
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarQtdCreditoUtilizado", targetNamespace = "http://adm.integracao.servicos/", className = "servicos.integracao.adm.client.ConsultarQtdCreditoUtilizado")
    @ResponseWrapper(localName = "consultarQtdCreditoUtilizadoResponse", targetNamespace = "http://adm.integracao.servicos/", className = "servicos.integracao.adm.client.ConsultarQtdCreditoUtilizadoResponse")
    public String consultarQtdCreditoUtilizado(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codigoEmpresa", targetNamespace = "")
        Integer codigoEmpresa,
        @WebParam(name = "tipoCobrancaPacto", targetNamespace = "")
        Integer tipoCobrancaPacto);

    /**
     * 
     * @param cpfCnpj
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarFavorecidoFinanceiro", targetNamespace = "http://adm.integracao.servicos/", className = "servicos.integracao.adm.client.ConsultarFavorecidoFinanceiro")
    @ResponseWrapper(localName = "consultarFavorecidoFinanceiroResponse", targetNamespace = "http://adm.integracao.servicos/", className = "servicos.integracao.adm.client.ConsultarFavorecidoFinanceiroResponse")
    public String consultarFavorecidoFinanceiro(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "cpf_cnpj", targetNamespace = "")
        String cpfCnpj);

    /**
     * 
     * @param codigoEmpresa
     * @param gerarNota
     * @param qtdParcelas
     * @param valorTotal
     * @param key
     * @param qtdCredito
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "lancarContaFinanceiro", targetNamespace = "http://adm.integracao.servicos/", className = "servicos.integracao.adm.client.LancarContaFinanceiro")
    @ResponseWrapper(localName = "lancarContaFinanceiroResponse", targetNamespace = "http://adm.integracao.servicos/", className = "servicos.integracao.adm.client.LancarContaFinanceiroResponse")
    public String lancarContaFinanceiro(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codigoEmpresa", targetNamespace = "")
        Integer codigoEmpresa,
        @WebParam(name = "qtdCredito", targetNamespace = "")
        Integer qtdCredito,
        @WebParam(name = "qtdParcelas", targetNamespace = "")
        Integer qtdParcelas,
        @WebParam(name = "valorTotal", targetNamespace = "")
        Double valorTotal,
        @WebParam(name = "gerarNota", targetNamespace = "")
        Boolean gerarNota);

    /**
     * 
     * @param json
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "desvincularNotaFiscalEmitida", targetNamespace = "http://adm.integracao.servicos/", className = "servicos.integracao.adm.client.DesvincularNotaFiscalEmitida")
    @ResponseWrapper(localName = "desvincularNotaFiscalEmitidaResponse", targetNamespace = "http://adm.integracao.servicos/", className = "servicos.integracao.adm.client.DesvincularNotaFiscalEmitidaResponse")
    public String desvincularNotaFiscalEmitida(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "json", targetNamespace = "")
        String json);

    /**
     * 
     * @param chave
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "executarProcessoLatitudeLongitude", targetNamespace = "http://adm.integracao.servicos/", className = "servicos.integracao.adm.client.ExecutarProcessoLatitudeLongitude")
    @ResponseWrapper(localName = "executarProcessoLatitudeLongitudeResponse", targetNamespace = "http://adm.integracao.servicos/", className = "servicos.integracao.adm.client.ExecutarProcessoLatitudeLongitudeResponse")
    public String executarProcessoLatitudeLongitude(
        @WebParam(name = "chave", targetNamespace = "")
        String chave);

    /**
     * 
     * @param deveUtilizarMoviDesk
     * @param chave
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "utilizarMoviDesk", targetNamespace = "http://adm.integracao.servicos/", className = "servicos.integracao.adm.client.UtilizarMoviDesk")
    @ResponseWrapper(localName = "utilizarMoviDeskResponse", targetNamespace = "http://adm.integracao.servicos/", className = "servicos.integracao.adm.client.UtilizarMoviDeskResponse")
    public String utilizarMoviDesk(
        @WebParam(name = "chave", targetNamespace = "")
        String chave,
        @WebParam(name = "deveUtilizarMoviDesk", targetNamespace = "")
        String deveUtilizarMoviDesk);

    /**
     * 
     * @param chave
     * @param deveUtilizarChatMoviDesk
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "utilizarChatMoviDesk", targetNamespace = "http://adm.integracao.servicos/", className = "servicos.integracao.adm.client.UtilizarChatMoviDesk")
    @ResponseWrapper(localName = "utilizarChatMoviDeskResponse", targetNamespace = "http://adm.integracao.servicos/", className = "servicos.integracao.adm.client.UtilizarChatMoviDeskResponse")
    public String utilizarChatMoviDesk(
        @WebParam(name = "chave", targetNamespace = "")
        String chave,
        @WebParam(name = "deveUtilizarChatMoviDesk", targetNamespace = "")
        String deveUtilizarChatMoviDesk);

    /**
     * 
     * @param chave
     * @param corrigirProtocolo
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "atualizarDadosEmpresa", targetNamespace = "http://adm.integracao.servicos/", className = "servicos.integracao.adm.client.AtualizarDadosEmpresa")
    @ResponseWrapper(localName = "atualizarDadosEmpresaResponse", targetNamespace = "http://adm.integracao.servicos/", className = "servicos.integracao.adm.client.AtualizarDadosEmpresaResponse")
    public String atualizarDadosEmpresa(
        @WebParam(name = "chave", targetNamespace = "")
        String chave,
        @WebParam(name = "corrigirProtocolo", targetNamespace = "")
        String corrigirProtocolo);

    /**
     * 
     * @param chave
     * @param codigoGrupo
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "alterarGrupoChatMovidesk", targetNamespace = "http://adm.integracao.servicos/", className = "servicos.integracao.adm.client.AlterarGrupoChatMovidesk")
    @ResponseWrapper(localName = "alterarGrupoChatMovideskResponse", targetNamespace = "http://adm.integracao.servicos/", className = "servicos.integracao.adm.client.AlterarGrupoChatMovideskResponse")
    public String alterarGrupoChatMovidesk(
        @WebParam(name = "chave", targetNamespace = "")
        String chave,
        @WebParam(name = "codigoGrupo", targetNamespace = "")
        String codigoGrupo);

    /**
     * 
     * @param json
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "ajustarEmpresaParaIniciarPosPago", targetNamespace = "http://adm.integracao.servicos/", className = "servicos.integracao.adm.client.AjustarEmpresaParaIniciarPosPago")
    @ResponseWrapper(localName = "ajustarEmpresaParaIniciarPosPagoResponse", targetNamespace = "http://adm.integracao.servicos/", className = "servicos.integracao.adm.client.AjustarEmpresaParaIniciarPosPagoResponse")
    public String ajustarEmpresaParaIniciarPosPago(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "json", targetNamespace = "")
        String json);

}
