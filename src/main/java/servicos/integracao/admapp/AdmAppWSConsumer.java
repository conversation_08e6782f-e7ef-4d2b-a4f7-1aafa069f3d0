/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package servicos.integracao.admapp;

import br.com.pacto.controller.json.atividade.TemaAtividade;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Uteis;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.xml.namespace.QName;
import javax.xml.ws.BindingProvider;
import servicos.integracao.admapp.client.AdmAppWS;
import servicos.integracao.admapp.client.AdmAppWS_Service;
import servicos.integracao.admapp.client.Empresa;
import servicos.integracao.admapp.client.Midia;

/**
 *
 * <AUTHOR>
 */
public class AdmAppWSConsumer {

    private static AdmAppWS servico;
    private static final int CONNECT_TIMEOUT_MS = 3000;
    private static final int READ_TIMEOUT_MS = 10000;
    public static final String CONNECT_TIMEOUT = "com.sun.xml.ws.connect.timeout";
    public static final String REQUEST_TIMEOUT = "com.sun.xml.ws.request.timeout";

    public static void init() {
        servico = null;
    }

    private static AdmAppWS getInstance(final String key) {
        if (servico == null) {
            try {
                URL u = new URL(Aplicacao.getProp(key, Aplicacao.urlAdmApp) + "/AdmAppWS?wsdl");
                QName qName = new QName("http://webservice.pacto.com.br/", "AdmAppWS");
                servico = new AdmAppWS_Service(u, qName).getAdmAppWSPort();
                Map<String, Object> reqContext = ((BindingProvider) servico).getRequestContext();
                reqContext.put(CONNECT_TIMEOUT, CONNECT_TIMEOUT_MS);
                reqContext.put(REQUEST_TIMEOUT, READ_TIMEOUT_MS);
            } catch (MalformedURLException ex) {
                Logger.getLogger(AdmAppWSConsumer.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
        return servico;
    }

    public static List<Midia> obterMidias(final String chave) {
        Uteis.logar(null, "Obtendo midias para chave: " + chave);
        final String url = obterUrlBase(chave);
        if (url != null && !url.isEmpty()) {
            List<Midia> lista = getInstance(chave).obterMidias(chave);
            Uteis.logar(null, String.format(" Encontradas: %s Midias para Chave: %s", new Object[]{lista != null ? lista.size() : 0, chave}));
            return lista;
        } else {
            return null;
        }
    }

    public static String obterUrlBase(final String chave) {
        try {
            return TemaAtividade.getURLBase(chave);
        } catch (Exception e) {
            Logger.getLogger(AdmAppWSConsumer.class.getName()).log(Level.SEVERE, null, e);
            return "";
        }
    }

    public static String obterUrlBaseJaCarregada(final String chave) {
        try {
            final String url = obterUrlBase(chave);
            return url == null ? "" : url;
        } catch (Exception e) {
            return "";
        }
    }
}
