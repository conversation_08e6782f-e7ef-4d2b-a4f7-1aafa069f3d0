
package servicos.integracao.admapp.client;

import java.util.List;
import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.6b21 
 * Generated source version: 2.1
 * 
 */
@WebService(name = "AdmAppWS", targetNamespace = "http://webservice.pacto.com.br/")
@XmlSeeAlso({
    ObjectFactory.class
})
public interface AdmAppWS {


    /**
     * 
     * @param key
     * @return
     *     returns servicos.integracao.admapp.client.Empresa
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "obterEmpresa", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.admapp.client.ObterEmpresa")
    @ResponseWrapper(localName = "obterEmpresaResponse", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.admapp.client.ObterEmpresaResponse")
    public Empresa obterEmpresa(
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param key
     * @return
     *     returns java.util.List<servicos.integracao.admapp.client.Midia>
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "obterMidias", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.admapp.client.ObterMidias")
    @ResponseWrapper(localName = "obterMidiasResponse", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.admapp.client.ObterMidiasResponse")
    public List<Midia> obterMidias(
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param listaMidias
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "inserirMidias", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.admapp.client.InserirMidias")
    @ResponseWrapper(localName = "inserirMidiasResponse", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.admapp.client.InserirMidiasResponse")
    public String inserirMidias(
        @WebParam(name = "listaMidias", targetNamespace = "")
        List<Midia> listaMidias);

}
