
package servicos.integracao.admapp.client;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.namespace.QName;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the servicos.integracao.admapp.client package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _ObterEmpresaResponse_QNAME = new QName("http://webservice.pacto.com.br/", "obterEmpresaResponse");
    private final static QName _InserirMidiasResponse_QNAME = new QName("http://webservice.pacto.com.br/", "inserirMidiasResponse");
    private final static QName _InserirMidias_QNAME = new QName("http://webservice.pacto.com.br/", "inserirMidias");
    private final static QName _ObterMidiasResponse_QNAME = new QName("http://webservice.pacto.com.br/", "obterMidiasResponse");
    private final static QName _ObterEmpresa_QNAME = new QName("http://webservice.pacto.com.br/", "obterEmpresa");
    private final static QName _ObterMidias_QNAME = new QName("http://webservice.pacto.com.br/", "obterMidias");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: servicos.integracao.admapp.client
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link ObterMidias }
     * 
     */
    public ObterMidias createObterMidias() {
        return new ObterMidias();
    }

    /**
     * Create an instance of {@link ObterEmpresa }
     * 
     */
    public ObterEmpresa createObterEmpresa() {
        return new ObterEmpresa();
    }

    /**
     * Create an instance of {@link InserirMidias }
     * 
     */
    public InserirMidias createInserirMidias() {
        return new InserirMidias();
    }

    /**
     * Create an instance of {@link ObterMidiasResponse }
     * 
     */
    public ObterMidiasResponse createObterMidiasResponse() {
        return new ObterMidiasResponse();
    }

    /**
     * Create an instance of {@link InserirMidiasResponse }
     * 
     */
    public InserirMidiasResponse createInserirMidiasResponse() {
        return new InserirMidiasResponse();
    }

    /**
     * Create an instance of {@link ObterEmpresaResponse }
     * 
     */
    public ObterEmpresaResponse createObterEmpresaResponse() {
        return new ObterEmpresaResponse();
    }

    /**
     * Create an instance of {@link Midia }
     * 
     */
    public Midia createMidia() {
        return new Midia();
    }

    /**
     * Create an instance of {@link Empresa }
     * 
     */
    public Empresa createEmpresa() {
        return new Empresa();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterEmpresaResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "obterEmpresaResponse")
    public JAXBElement<ObterEmpresaResponse> createObterEmpresaResponse(ObterEmpresaResponse value) {
        return new JAXBElement<ObterEmpresaResponse>(_ObterEmpresaResponse_QNAME, ObterEmpresaResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link InserirMidiasResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "inserirMidiasResponse")
    public JAXBElement<InserirMidiasResponse> createInserirMidiasResponse(InserirMidiasResponse value) {
        return new JAXBElement<InserirMidiasResponse>(_InserirMidiasResponse_QNAME, InserirMidiasResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link InserirMidias }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "inserirMidias")
    public JAXBElement<InserirMidias> createInserirMidias(InserirMidias value) {
        return new JAXBElement<InserirMidias>(_InserirMidias_QNAME, InserirMidias.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterMidiasResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "obterMidiasResponse")
    public JAXBElement<ObterMidiasResponse> createObterMidiasResponse(ObterMidiasResponse value) {
        return new JAXBElement<ObterMidiasResponse>(_ObterMidiasResponse_QNAME, ObterMidiasResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterEmpresa }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "obterEmpresa")
    public JAXBElement<ObterEmpresa> createObterEmpresa(ObterEmpresa value) {
        return new JAXBElement<ObterEmpresa>(_ObterEmpresa_QNAME, ObterEmpresa.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterMidias }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "obterMidias")
    public JAXBElement<ObterMidias> createObterMidias(ObterMidias value) {
        return new JAXBElement<ObterMidias>(_ObterMidias_QNAME, ObterMidias.class, null, value);
    }

}
