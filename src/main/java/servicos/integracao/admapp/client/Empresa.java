
package servicos.integracao.admapp.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de empresa complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType name="empresa">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="ativa" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="chave" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="codigo" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="hostBD" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="modulos" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="nomeBD" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="passwordBD" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="permiteMultiEmpresas" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="porta" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="roboControle" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="temaMidias" type="{http://webservice.pacto.com.br/}temaEnum" minOccurs="0"/>
 *         &lt;element name="urlRepositorioMidias" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="usarBDLocal" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="userBD" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "empresa", propOrder = {
    "ativa",
    "chave",
    "codigo",
    "hostBD",
    "modulos",
    "nomeBD",
    "passwordBD",
    "permiteMultiEmpresas",
    "porta",
    "roboControle",
    "temaMidias",
    "urlRepositorioMidias",
    "usarBDLocal",
    "userBD"
})
public class Empresa {

    protected boolean ativa;
    protected String chave;
    protected Integer codigo;
    protected String hostBD;
    protected String modulos;
    protected String nomeBD;
    protected String passwordBD;
    protected boolean permiteMultiEmpresas;
    protected Integer porta;
    protected String roboControle;
    protected TemaEnum temaMidias;
    protected String urlRepositorioMidias;
    protected boolean usarBDLocal;
    protected String userBD;

    /**
     * Obtém o valor da propriedade ativa.
     * 
     */
    public boolean isAtiva() {
        return ativa;
    }

    /**
     * Define o valor da propriedade ativa.
     * 
     */
    public void setAtiva(boolean value) {
        this.ativa = value;
    }

    /**
     * Obtém o valor da propriedade chave.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getChave() {
        return chave;
    }

    /**
     * Define o valor da propriedade chave.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setChave(String value) {
        this.chave = value;
    }

    /**
     * Obtém o valor da propriedade codigo.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCodigo() {
        return codigo;
    }

    /**
     * Define o valor da propriedade codigo.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCodigo(Integer value) {
        this.codigo = value;
    }

    /**
     * Obtém o valor da propriedade hostBD.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getHostBD() {
        return hostBD;
    }

    /**
     * Define o valor da propriedade hostBD.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setHostBD(String value) {
        this.hostBD = value;
    }

    /**
     * Obtém o valor da propriedade modulos.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getModulos() {
        return modulos;
    }

    /**
     * Define o valor da propriedade modulos.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setModulos(String value) {
        this.modulos = value;
    }

    /**
     * Obtém o valor da propriedade nomeBD.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNomeBD() {
        return nomeBD;
    }

    /**
     * Define o valor da propriedade nomeBD.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNomeBD(String value) {
        this.nomeBD = value;
    }

    /**
     * Obtém o valor da propriedade passwordBD.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPasswordBD() {
        return passwordBD;
    }

    /**
     * Define o valor da propriedade passwordBD.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPasswordBD(String value) {
        this.passwordBD = value;
    }

    /**
     * Obtém o valor da propriedade permiteMultiEmpresas.
     * 
     */
    public boolean isPermiteMultiEmpresas() {
        return permiteMultiEmpresas;
    }

    /**
     * Define o valor da propriedade permiteMultiEmpresas.
     * 
     */
    public void setPermiteMultiEmpresas(boolean value) {
        this.permiteMultiEmpresas = value;
    }

    /**
     * Obtém o valor da propriedade porta.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getPorta() {
        return porta;
    }

    /**
     * Define o valor da propriedade porta.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setPorta(Integer value) {
        this.porta = value;
    }

    /**
     * Obtém o valor da propriedade roboControle.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRoboControle() {
        return roboControle;
    }

    /**
     * Define o valor da propriedade roboControle.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRoboControle(String value) {
        this.roboControle = value;
    }

    /**
     * Obtém o valor da propriedade temaMidias.
     * 
     * @return
     *     possible object is
     *     {@link TemaEnum }
     *     
     */
    public TemaEnum getTemaMidias() {
        return temaMidias;
    }

    /**
     * Define o valor da propriedade temaMidias.
     * 
     * @param value
     *     allowed object is
     *     {@link TemaEnum }
     *     
     */
    public void setTemaMidias(TemaEnum value) {
        this.temaMidias = value;
    }

    /**
     * Obtém o valor da propriedade urlRepositorioMidias.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUrlRepositorioMidias() {
        return urlRepositorioMidias;
    }

    /**
     * Define o valor da propriedade urlRepositorioMidias.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUrlRepositorioMidias(String value) {
        this.urlRepositorioMidias = value;
    }

    /**
     * Obtém o valor da propriedade usarBDLocal.
     * 
     */
    public boolean isUsarBDLocal() {
        return usarBDLocal;
    }

    /**
     * Define o valor da propriedade usarBDLocal.
     * 
     */
    public void setUsarBDLocal(boolean value) {
        this.usarBDLocal = value;
    }

    /**
     * Obtém o valor da propriedade userBD.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUserBD() {
        return userBD;
    }

    /**
     * Define o valor da propriedade userBD.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUserBD(String value) {
        this.userBD = value;
    }

}
