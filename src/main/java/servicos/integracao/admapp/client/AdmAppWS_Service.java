
package servicos.integracao.admapp.client;

import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceException;
import javax.xml.ws.WebServiceFeature;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.6b21 
 * Generated source version: 2.1
 * 
 */
@WebServiceClient(name = "AdmAppWS", targetNamespace = "http://webservice.pacto.com.br/", wsdlLocation = "https://app.pactosolucoes.com.br/AdmAPP/AdmAppWS?wsdl")
public class AdmAppWS_Service
    extends Service
{

    private final static URL ADMAPPWS_WSDL_LOCATION;
    private final static WebServiceException ADMAPPWS_EXCEPTION;
    private final static QName ADMAPPWS_QNAME = new QName("http://webservice.pacto.com.br/", "AdmAppWS");

    static {
        URL url = null;
        WebServiceException e = null;
        try {
            url = new URL("https://app.pactosolucoes.com.br/AdmAPP/AdmAppWS?wsdl");
        } catch (MalformedURLException ex) {
            e = new WebServiceException(ex);
        }
        ADMAPPWS_WSDL_LOCATION = url;
        ADMAPPWS_EXCEPTION = e;
    }

    public AdmAppWS_Service() {
        super(__getWsdlLocation(), ADMAPPWS_QNAME);
    }

    public AdmAppWS_Service(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    /**
     * 
     * @return
     *     returns AdmAppWS
     */
    @WebEndpoint(name = "AdmAppWSPort")
    public AdmAppWS getAdmAppWSPort() {
        return super.getPort(new QName("http://webservice.pacto.com.br/", "AdmAppWSPort"), AdmAppWS.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns AdmAppWS
     */
    @WebEndpoint(name = "AdmAppWSPort")
    public AdmAppWS getAdmAppWSPort(WebServiceFeature... features) {
        return super.getPort(new QName("http://webservice.pacto.com.br/", "AdmAppWSPort"), AdmAppWS.class, features);
    }

    private static URL __getWsdlLocation() {
        if (ADMAPPWS_EXCEPTION!= null) {
            throw ADMAPPWS_EXCEPTION;
        }
        return ADMAPPWS_WSDL_LOCATION;
    }

}
