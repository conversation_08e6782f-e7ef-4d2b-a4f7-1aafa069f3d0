
package servicos.integracao.admapp.client;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de inserirMidias complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType name="inserirMidias">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="listaMidias" type="{http://webservice.pacto.com.br/}midia" maxOccurs="unbounded" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "inserirMidias", propOrder = {
    "listaMidias"
})
public class InserirMidias {

    protected List<Midia> listaMidias;

    /**
     * Gets the value of the listaMidias property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the listaMidias property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getListaMidias().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link Midia }
     * 
     * 
     */
    public List<Midia> getListaMidias() {
        if (listaMidias == null) {
            listaMidias = new ArrayList<Midia>();
        }
        return this.listaMidias;
    }

}
