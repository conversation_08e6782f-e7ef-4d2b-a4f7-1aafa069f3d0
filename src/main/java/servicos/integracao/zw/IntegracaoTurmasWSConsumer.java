/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.integracao.zw;

import br.com.pacto.base.impl.EntityManagerFactoryService;
import br.com.pacto.base.oamd.ManyDataBasesException;
import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.controller.json.empresa.ResultEmpresasJSON;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.JSONMapper;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.AgendadoJSON;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.bean.AgendaTotalTO;
import br.com.pacto.util.bean.AgendadoTO;
import br.com.pacto.util.bean.AgendamentoConfirmadoTO;
import br.com.pacto.util.bean.AgendamentoDesmarcadoTO;
import br.com.pacto.util.json.AgendaTotalJSON;
import br.com.pacto.util.json.AlunoAulaAcessoJSON;
import br.com.pacto.util.json.ControleCreditoTreinoJSON;
import br.com.pacto.util.json.ParamTurmasJSON;
import br.com.pacto.util.json.ResultAlunoClienteSinteticoJSON;
import br.com.pacto.util.json.TurmaAulaCheiaJSON;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.stereotype.Service;
import org.springframework.ui.ModelMap;
import servicos.integracao.zw.client.EventJSON;
import servicos.integracao.zw.client.HorarioTurmaDTO;
import servicos.integracao.zw.client.IntegracaoTurmasWS;
import servicos.integracao.zw.client.IntegracaoTurmasWS_Service;

import javax.xml.namespace.QName;
import javax.xml.ws.BindingProvider;
import java.net.URL;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 *
 * <AUTHOR>
 */
@Service
public class IntegracaoTurmasWSConsumer {

    private static final ConcurrentHashMap<String, ResultEmpresasJSON> cacheUnidades = new ConcurrentHashMap<>();
    public static void purgeCache(final String chave) {
        cacheUnidades.remove(chave);
    }
    private IntegracaoTurmasWS wrapper = null;

    public IntegracaoTurmasWSConsumer() {
        try {
            getInstance();
        }catch (Exception e){
            Uteis.logar(e, IntegracaoCadastrosWSConsumer.class);
        }
    }

    public void init() {
        try {
            wrapper = null;
            getInstance();
        }catch (Exception e){
            Uteis.logar(e, IntegracaoCadastrosWSConsumer.class);
        }
    }


    private IntegracaoTurmasWS getInstance() throws Exception {
        if (wrapper == null) {
            URL u = new URL(Aplicacao.getProp(Aplicacao.urlZillyonWebIntegProp) + "/IntegracaoTurmasWS?wsdl");
            QName qName = new QName("http://webservice.basico.comuns.negocio/", "IntegracaoTurmasWS");
            wrapper = new IntegracaoTurmasWS_Service(u, qName).getIntegracaoTurmasWSPort();
            Map<String, Object> reqContext = ((BindingProvider) wrapper).getRequestContext();
            reqContext.put("com.sun.xml.ws.connect.timeout", 5000);
            reqContext.put("com.sun.xml.ws.request.timeout", 30000);
        }
        return wrapper;
    }

    public List<AgendaTotalJSON> consultarAgendamentosJSON(final String key, final Date inicio,
                                                           final Date fim, final Integer empresa, final String modalidade, String timeZone) throws Exception{
        String agendamentos = getInstance().consultarAgendamentos(key, Uteis.getData(inicio), Uteis.getData(fim), empresa, modalidade == null ? "" : modalidade);
        validateKey(key);
        JSONArray jsonRetorno = new JSONArray(agendamentos);
        if (jsonRetorno.length() > 0) {
            JSONObject obj = jsonRetorno.getJSONObject(0);
            if (obj.opt("erro") != null) {
                throw new Exception(obj.get("erro").toString());
            }
        }

        return JSONMapper.getListFromTimeZone(new JSONArray(agendamentos), AgendaTotalJSON.class, timeZone);
    }


    public List<AgendaTotalJSON> consultarAgendamentosJSONDoProfessor( final String key,  final Date inicio,
                                                                       final Date fim, final Integer empresa, final String modalidade, final Integer codigoProfessor) throws Exception{
        validateKey(key);
        String agendamentos = getInstance().consultarAgendamentos(key, Uteis.getData(inicio), Uteis.getData(fim), empresa, modalidade == null ? "" : modalidade);
        List<AgendaTotalJSON> lista = JSONMapper.getList(new JSONArray(agendamentos), AgendaTotalJSON.class);


        Iterator<AgendaTotalJSON> i = lista.iterator();
        while (i.hasNext()) {
            AgendaTotalJSON j = i.next();
            if (j.getCodigoResponsavel().intValue() != codigoProfessor) {
                i.remove();
            }
        }
        return lista;
    }


    public List<AgendaTotalTO> consultarAgendamentos( final String key,  final Date inicio,
                                                      final Date fim, final Integer empresa, final String modalidade) throws Exception{
        validateKey(key);
        List<AgendaTotalJSON> jsons = consultarAgendamentosJSON(key, inicio, fim, empresa, modalidade, null);
        List<AgendaTotalTO> lista = new ArrayList<AgendaTotalTO>();
        for(AgendaTotalJSON j : jsons){
            lista.add(new AgendaTotalTO(j));
        }
        return lista;
    }

    public String consultarAgendamentosModalidadeAluno( final String key,  final Date inicio,
                                                        final Date fim, final Integer matricula) throws Exception{
        validateKey(key);
        return getInstance().consultarAgendamentosModalidadeAluno(key, Uteis.getData(inicio), Uteis.getData(fim), matricula, true);
    }

    public ModelMap buscarUnidades(final String chave) throws Exception {
        validateKey(chave);
        ModelMap mm = new ModelMap();
        if (cacheUnidades.containsKey(chave)) return mm.addAttribute("resultEmpresasJSON", cacheUnidades.get(chave).getResult());

        ResultEmpresasJSON result = JSONMapper.getObject(new JSONObject(getInstance().buscarUnidades(chave)), ResultEmpresasJSON.class);
        if (result != null && result.getResult() != null && !result.getResult().isEmpty())
            cacheUnidades.put(chave, result);

        return  mm.addAttribute("resultEmpresasJSON", result.getResult());
    }

    public List<AgendadoTO> consultarAgendados( final String key,  final Date inicio,
                                                final Date fim, final Integer empresa) throws Exception{
        validateKey(key);
        String agendados = getInstance().consultarAgendados(key, Uteis.getData(inicio), Uteis.getData(fim), empresa);
        List<AgendadoJSON> jsons = JSONMapper.getList(new JSONArray(agendados), AgendadoJSON.class);
        List<AgendadoTO> lista = new ArrayList<AgendadoTO>();
        for(AgendadoJSON j : jsons){
            lista.add(new AgendadoTO(j));
        }
        return lista;
    }

    public List<AgendadoTO> consultarReposicoes( final String key,  final Date inicio,
                                                 final Date fim, final Integer empresa) throws Exception{
        validateKey(key);
        String agendados = getInstance().consultarReposicoes(key, Uteis.getData(inicio), Uteis.getData(fim), empresa);
        List<AgendadoJSON> jsons = JSONMapper.getList(new JSONArray(agendados), AgendadoJSON.class);
        List<AgendadoTO> lista = new ArrayList<AgendadoTO>();
        for(AgendadoJSON j : jsons){
            lista.add(new AgendadoTO(j));
        }
        return lista;
    }

    public Map<Integer, Map<String,AgendamentoDesmarcadoTO>> consultarDesmarcados( final String key, final Date inicio,
                                                                                   final Date fim, final Integer empresa) throws Exception {
        validateKey(key);
        String dados = getInstance().consultarDesmarcados(key, Uteis.getData(inicio), Uteis.getData(fim), empresa);
        Map<Integer, Map<String,AgendamentoDesmarcadoTO>> mapa = new HashMap<>();
        JSONArray array = new JSONArray(dados);
        for (int i = 0; i < array.length(); i++) {
            Map<String,AgendamentoDesmarcadoTO> mapaCliente = mapa.get(array.getJSONObject(i).getInt("codigoCliente"));
            if(mapaCliente == null){
                mapaCliente = new HashMap<>();
                mapa.put(array.getJSONObject(i).getInt("codigoCliente"), mapaCliente);
            }
            mapaCliente.put(array.getJSONObject(i).getString("idAgendamento"),
                    new AgendamentoDesmarcadoTO(array.getJSONObject(i).getString("idAgendamento"),
                            array.getJSONObject(i).getInt("codigoCliente"),
                            array.getJSONObject(i).getBoolean("reposto"),
                            array.getJSONObject(i).getInt("codigoContrato"),
                            array.getJSONObject(i).getString("justificativa")));
        }
        return mapa;
    }
    public Map<Integer, Map<String,AgendamentoConfirmadoTO>> consultarConfirmados( final String key, final Date inicio,
                                                                                   final Date fim) throws Exception {
        validateKey(key);
        String dados = getInstance().consultarConfirmados(key, Uteis.getData(inicio), Uteis.getData(fim));
        Map<Integer, Map<String,AgendamentoConfirmadoTO>> mapa = new HashMap<>();
        JSONArray array = new JSONArray(dados);
        for (int i = 0; i < array.length(); i++) {
            Map<String,AgendamentoConfirmadoTO> mapaCliente = mapa.get(array.getJSONObject(i).getInt("codigoCliente"));
            if(mapaCliente == null){
                mapaCliente = new HashMap<>();
                mapa.put(array.getJSONObject(i).getInt("codigoCliente"), mapaCliente);
            }
            mapaCliente.put(array.getJSONObject(i).getString("idAgendamento"),
                    new AgendamentoConfirmadoTO(array.getJSONObject(i).getString("idAgendamento"),
                            array.getJSONObject(i).getInt("codigoCliente")));
        }
        return mapa;
    }

    public List<AgendadoTO> consultarAlunos( final String key,
                                             final Integer empresa, final String nome, final Integer matricula,
                                             final Integer modalidade,
                                             final Boolean somenteAtivos) throws Exception{
        validateKey(key);
        String agendados = getInstance().consultarClientesModalidade(key,empresa,matricula, nome, modalidade, somenteAtivos.toString());
        List<AgendadoJSON> jsons = JSONMapper.getList(new JSONArray(agendados), AgendadoJSON.class);
        List<AgendadoTO> lista = new ArrayList<AgendadoTO>();
        for(AgendadoJSON j : jsons){
            lista.add(new AgendadoTO(j));
        }
        return lista;
    }


    public String desmarcarAula( final String key, ParamTurmasJSON json) throws Exception{
        validateKey(key);
        return getInstance().desmarcarAulas(key, json.toJSON());
    }

    public String reporAula( final String key, ParamTurmasJSON json) throws Exception{
        validateKey(key);
        return getInstance().reporAula(key, json.toJSON());
    }

    public String inserirAlunoAulaCheia( final String key, ParamTurmasJSON json) throws Exception{
        validateKey(key);
        return getInstance().inserirAlunoAulaCheia(key, json.toJSON());
    }

    public String excluirAlunoAulaCheia( final String key, ParamTurmasJSON json) throws Exception{
        validateKey(key);
        return getInstance().excluirAlunoAulaCheia(key, json.toJSON());
    }

    public String aulaARepor( final String key, final Integer cliente, final Integer contrato,
                              final Integer modalidade) throws Exception{
        validateKey(key);
        return getInstance().aulaARepor(key, cliente, contrato, modalidade);
    }

    public String gravarTurmaAulaCheia( final String key, final TurmaAulaCheiaJSON json) throws Exception{
        validateKey(key);
        return getInstance().gravarAula(key, json.toJSON());
    }

    public List<TurmaAulaCheiaJSON> obterTurmasAulaCheia( final String key, final Integer empresa) throws Exception {
        validateKey(key);
        String aulasColetivas = getInstance().obterAulasColetivas(key, empresa);
        return JSONMapper.getList(new JSONArray(aulasColetivas), TurmaAulaCheiaJSON.class);
    }

    public List<Integer> alunosPresentes( final String key, final Integer horarioTurma, final Date dia) throws Exception {
        validateKey(key);
        String presentes = getInstance().alunosPresentes(key, horarioTurma, Uteis.getData(dia));
        List<Integer> cods = new ArrayList<Integer>();
        JSONArray array = new JSONArray(presentes);
        for(int i = 0; i < array.length(); i++){
            cods.add(array.getInt(i));
        }
        return cods;
    }

    public List<AgendadoTO> consultarPresencas( final String key,final Integer empresa) throws Exception{
        validateKey(key);
        String agendados = getInstance().obterPresencas(key, empresa);
        List<AgendadoJSON> jsons = JSONMapper.getList(new JSONArray(agendados), AgendadoJSON.class);
        List<AgendadoTO> lista = new ArrayList<AgendadoTO>();
        for(AgendadoJSON j : jsons){
            lista.add(new AgendadoTO(j));
        }
        return lista;
    }

    public ResultAlunoClienteSinteticoJSON obterAlunosDeUmaAula( final String key,final Integer codigoHorario, Date dia) throws Exception{
        validateKey(key);
        String agendados = getInstance().obterAlunosDeUmaAula(key, codigoHorario, Uteis.getData(dia));
        return JSONMapper.getObject(new JSONObject(agendados), ResultAlunoClienteSinteticoJSON.class);
    }

    public ResultAlunoClienteSinteticoJSON obterAlunosAulaNaoColetiva( final String key,final Integer codigoHorario, Date dia) throws Exception{
        validateKey(key);
        String agendados = getInstance().obterAlunosAulaNaoColetiva(key, codigoHorario, Uteis.getData(dia));
        return JSONMapper.getObject(new JSONObject(agendados), ResultAlunoClienteSinteticoJSON.class);
    }

    public List<AgendadoJSON> obterAlunosDeUmaTurma( final String key,
                                                     final Integer empresa, final Integer codigoHorario, Date dia) throws Exception{
        validateKey(key);
        String agendados = getInstance().obterAlunosHorarioTurma(key, empresa, codigoHorario, Uteis.getData(dia));
        return JSONMapper.getList(new JSONArray(agendados), AgendadoJSON.class);
    }

    public JSONArray consultarAcessos( final String key,final Integer empresa) throws Exception{
        validateKey(key);
        String acessos = getInstance().obterAcessos(key, empresa);
        return new JSONArray(acessos);

    }

    public JSONArray consultarAcessosDia( final String key,final Integer empresa, final Date dia) throws Exception{
        validateKey(key);
        String acessos = getInstance().obterAcessosDia(key, empresa, Uteis.getData(dia));
        return new JSONArray(acessos);

    }

    public JSONArray consultarProfessores( final String key,final Integer empresa, final Boolean somenteAtivos) throws Exception{
        validateKey(key);
        String acessos = getInstance().consultarProfessores(key, empresa, somenteAtivos.toString());
        return new JSONArray(acessos);
    }

    public String consultarProximasAulas( final String key,  final Integer matricula, Boolean proximas30dias) throws Exception{
//           String agendamentos = getInstance().consultarProximasAulas(key, matricula, proximas30dias.toString());
//           return JSONMapper.getList(new JSONArray(agendamentos), AgendaTotalJSON.class);
        validateKey(key);
        return getInstance().consultarProximasAulas(key, matricula, proximas30dias.toString());
    }

    public List<AgendaTotalJSON> consultarProximasAulaCheia( final String key,  final Integer matricula) throws Exception{
        validateKey(key);
        String agendamentos = getInstance().consultarProximasAulaCheia(key, matricula);
        return JSONMapper.getList(new JSONArray(agendamentos), AgendaTotalJSON.class);
    }

    public List<AgendaTotalJSON> consultarTurmasAluno( final String key,  final Integer matricula) throws Exception{
        validateKey(key);
        String agendamentos = getInstance().consultarTurmasAluno(key, matricula);
        return JSONMapper.getList(new JSONArray(agendamentos), AgendaTotalJSON.class);
    }

    public List<AgendaTotalJSON> obterAulasDesmarcadas(final String url, final String key,  final Integer matricula, final Integer modalidade) throws Exception{
        validateKey(key);
        final String urlIntegracaoTurmaZW = url + "/prest/integracaoTurma?operacao=obterAulasDesmarcadasSemReposicao&key=" + key + "&matricula=" + matricula + "&modalidade="+ (UteisValidacao.emptyNumber(modalidade) ? "0" : modalidade);

        CloseableHttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
        HttpPost httpPost = new HttpPost(urlIntegracaoTurmaZW);
        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        String body = handler.handleResponse(response);
        client.close();

        return JSONMapper.getList(new JSONArray(body), AgendaTotalJSON.class);
    }

    public List<AgendaTotalJSON> obterAulasDiaAluno(final String url, final String key,  final Integer matricula, final Date dia, final Integer modalidade) throws Exception{
        validateKey(key);
        final String urlIntegracaoTurmaZW = url + "/prest/integracaoTurma?operacao=obterAulasDoDiaAluno&key=" + key + "&matricula=" + matricula + "&modalidade="+ (UteisValidacao.emptyNumber(modalidade) ? "0" : modalidade) + "&dia="+Uteis.getData(dia);

        CloseableHttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
        HttpPost httpPost = new HttpPost(urlIntegracaoTurmaZW);
        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        String body = handler.handleResponse(response);
        client.close();
        if(body.toUpperCase().contains("ERRO")){
            throw new Exception(body);
        }

        return JSONMapper.getList(new JSONArray(body), AgendaTotalJSON.class);
    }

    public List<AgendaTotalJSON> obterAulasAlunoPeriodo(
            final String key,  final Integer matricula,
            final Date inicio,
            final Date fim) throws Exception{
        validateKey(key);
        String agendamentos = getInstance().consultarAulasAlunoPeriodo(key,
                matricula, Uteis.getData(inicio), Uteis.getData(fim));
        return JSONMapper.getList(new JSONArray(agendamentos), AgendaTotalJSON.class);
    }

    public String marcarDesmarcarAulasApp( final String key,  final Integer matricula,
                                           Integer codigoHorarioTurma, Date data, Boolean marcar, Boolean validarParcelaVencida, Integer contrato) throws Exception{
        validateKey(key);
        return getInstance().marcarDesmarcarAulasApp(key, matricula,
                codigoHorarioTurma, Uteis.getDataAplicandoFormatacao(data, "dd/MM/yyyy"),
                marcar.toString(), validarParcelaVencida.toString());
    }

    public List<ControleCreditoTreinoJSON> obterExtratoCreditos( final String key,  final Integer matricula,final Date data) throws Exception{
        validateKey(key);
        String extrato = getInstance().obterExtratoCreditos(key, matricula, Uteis.getDataAplicandoFormatacao(data, "dd/MM/yyyy HH:mm:ss"));
        return JSONMapper.getList(new JSONArray(extrato), ControleCreditoTreinoJSON.class);
    }

    public String obterSaldoAluno( final String key,  final Integer matricula) throws Exception{
        validateKey(key);
        return getInstance().obterSaldoAluno(key, matricula);
    }

    public String excluirAulaCheia( final String key,  final Integer codigoAula, final Integer codigoUsuario) throws Exception{
        validateKey(key);
        return getInstance().excluirAulaCheia(key, codigoAula, codigoUsuario);
    }

    public String marcarEuQueroHorario( final String key, final Integer codigoAluno,
                                        Integer codigoHorarioTurma, String data) throws Exception {
        validateKey(key);
        return getInstance().marcarEuQueroHorario(key, codigoAluno, codigoHorarioTurma, data);
    }

    public String desmarcarEuQueroHorario( final String key, final Integer codigoAluno,
                                           Integer codigoHorarioTurma, String data) throws Exception {
        validateKey(key);
        return getInstance().desmarcarEuQueroHorario(key, codigoAluno, codigoHorarioTurma, data);
    }

    public String consultarTemAulaExtra( final String key, final Integer contrato,
                                         Integer modalidade, Integer saldo) throws Exception {
        validateKey(key);
        return getInstance().consultarTemAulaExtra(key, modalidade, saldo, contrato);
    }


    public String consultarDemandasSintetico( final String key, String filtroProfessores,
                                              String filtroModalidades, String filtroAmbientes, String filtroTurmas,
                                              String filtroHorarios, String filtroDiasDaSemana, String dataInicio,
                                              String dataFim) throws Exception {
        validateKey(key);
        return getInstance().consultarDemandasSintetico(key, filtroProfessores, filtroModalidades, filtroAmbientes,
                filtroTurmas, filtroHorarios, filtroDiasDaSemana, dataInicio,
                dataFim);
    }

    public String consultarDemandasAnalitico( final String key, String filtroProfessores,
                                              String filtroModalidades, String filtroAmbientes, String filtroTurmas,
                                              String filtroHorarios, String filtroDiasDaSemana, String dataInicio,
                                              String dataFim) throws Exception {
        validateKey(key);
        return getInstance().consultarDemandasAnalitico(key, filtroProfessores, filtroModalidades, filtroAmbientes,
                filtroTurmas, filtroHorarios, filtroDiasDaSemana, dataInicio,
                dataFim);
    }

    public String consultarTodasTurmas( String key) throws Exception {
        validateKey(key);
        return getInstance().consultarTodasTurmas(key);
    }

    public String consultarTodasHoraInicial( String key) throws Exception {
        validateKey(key);
        return getInstance().consultarTodasHoraInicial(key);

    }

    public String confirmarAlunoAula( final String key,final Integer cliente,
                                      final Integer horarioTurma,final String dia,final Integer usuario) throws Exception{
        validateKey(key);
        return getInstance().confirmarAulaAluno(key, horarioTurma, cliente, dia, usuario);
    }

    public String gravarPresenca( final String key,  final Integer cliente,
                                  Integer codigoHorarioTurma, Date data, Boolean reposicao, Boolean desmarcar) throws Exception{
        validateKey(key);
        return getInstance().marcarPresenca(key, cliente, codigoHorarioTurma, Uteis.getDataAplicandoFormatacao(data, "dd/MM/yyyy"), reposicao.toString(),
                desmarcar.toString());
    }

    public String desconfirmarAlunoAula( final String key,final Integer cliente,
                                         final Integer horarioTurma,final String dia) throws Exception{
        validateKey(key);
        return getInstance().removerAulaConfirmadaAluno(key, horarioTurma, cliente, dia);
    }

    public String inserirAlunoExperimental( final String key,  final Integer matricula,
                                            Integer codigoHorarioTurma, Date data, Integer usuario, Integer produtoFreePass) throws Exception{
        validateKey(key);
        return getInstance().inserirAlunoExperimental(key, matricula, codigoHorarioTurma,
                Uteis.getDataAplicandoFormatacao(data, "dd/MM/yyyy"), usuario, produtoFreePass);
    }

    public String produtosFreePass( final String key,  final Integer empresa, final Boolean somenteAtivos) throws Exception{
        validateKey(key);
        return getInstance().consultarProdutosAulaCheia(key, empresa, somenteAtivos);
    }

    public String pesquisarHorarioTurma( final String key, final Integer codigoHorarioTurma) throws Exception{
        validateKey(key);
        return getInstance().pesquisarHorarioTurma(key, codigoHorarioTurma);
    }

    public List<AgendaTotalJSON> pesquisarHorarioTurmaPorTurma( final String key, final Integer codigoTurma) throws Exception {
        validateKey(key);
        String retorno = getInstance().pesquisarHorarioTurmaPorTurma(key, codigoTurma);
        return JSONMapper.getList(new JSONArray(retorno), AgendaTotalJSON.class);
    }

    public String consultarAulaPeloClienteHorario( final String key, final String matricula, final Date data, final String horaInicial, final String horaFinal) throws Exception{
        validateKey(key);
        return getInstance().consultarAulaPeloClienteHorario(key, Uteis.getDataAplicandoFormatacao(data, "dd/MM/yyyy"), matricula, horaInicial, horaFinal);
    }

    public boolean consultarAlunoTemDiaria( final String key, final Integer codigoCliente, final Integer codigoModalidade, final Date data) throws Exception {
        validateKey(key);
        String retorno = getInstance().consultarAlunoTemDiaria(key, codigoCliente, codigoModalidade, Uteis.getDataAplicandoFormatacao(data, "dd/MM/yyyy"));
        return Boolean.parseBoolean(retorno);
    }

    public String atualizarConfiguracaoNrAulasExperimentais( final String key, final Integer aulasExperimentaisAntesAlteracao, final Integer valorNovo) throws Exception {
        validateKey(key);
        return getInstance().atualizarConfiguracaoNrAulasExperimentais(key, aulasExperimentaisAntesAlteracao, valorNovo);
    }

    public List<TurmaAulaCheiaJSON> obterTurmasAulaCheiaPorDia( final String key, final Integer empresa, Integer dia) throws Exception {
        validateKey(key);
        String aulasColetivas = getInstance().obterAulasColetivasPorDiaSemana(key, empresa, dia);
        return JSONMapper.getList(new JSONArray(aulasColetivas), TurmaAulaCheiaJSON.class);
    }

    public AgendaTotalJSON consultarUmaAula( final String key, String dia, Integer codigoHorarioTurma)  throws Exception{
        validateKey(key);
        String retorno = getInstance().consultarUmaAula(key, dia, codigoHorarioTurma);
        return retorno == null ? null : JSONMapper.getObject(new JSONObject(retorno), AgendaTotalJSON.class);
    }

    public List<AlunoAulaAcessoJSON> obterAlunosDaAulaComAcesso(final  String url,
                                                                final String key,
                                                                final Integer horarioTurma,
                                                                String dataAula) throws Exception {
        validateKey(key);

        String alunos = getInstance().obterAlunosDaAulaComAcesso(key, horarioTurma, dataAula);
        if (alunos.contains("O ambiente não tem nenhum coletor registrado, configure um coletor para o ambiente!")) {
            throw new Exception("O ambiente não tem nenhum coletor registrado, configure um coletor para o ambiente!");
        }
        return JSONMapper.getList(new JSONArray(alunos), AlunoAulaAcessoJSON.class);
    }

    public boolean existeReposicaoAlunoDia( String contexto, Integer horarioTurma, Integer codigoCliente, Date dia) throws Exception {
        validateKey(contexto);
        final String retorno = getInstance()
                .existeReposicao(contexto, horarioTurma, codigoCliente, Uteis.getDataAplicandoFormatacao(dia, "dd/MM/yyyy"));
        return Boolean.parseBoolean(retorno);
    }

    public EventJSON obterAulaSpivi( final String key, final Integer horarioTurmaCodigo, String data) throws Exception {
        validateKey(key);
        return getInstance().obterAulaSpivi(key, horarioTurmaCodigo, data);
    }

    public List<HorarioTurmaDTO> horariosTurma( String key, Integer turma) throws Exception{
        validateKey(key);
        return getInstance().horariosTurma(key, turma);
    }

    public String atualizarSpiviClientID(final String url, final String key, final Integer codigoPessoa, final Integer spiviClientID) throws Exception {
        validateKey(key);
        final String urlIntegracaoTurmaZW = url + "/prest/integracaoTurma?operacao=atualizarSpiviClientID&key=" + key + "&codigoPessoa=" + codigoPessoa + "&spiviClientID=" + spiviClientID;

        CloseableHttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
        HttpPost httpPost = new HttpPost(urlIntegracaoTurmaZW);
        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        String body = handler.handleResponse(response);
        client.close();

        return body;
    }

    public JSONObject obterContratoVigentePorPessoaSintetico(final String url, final String key, final Integer codigoPessoa, Date dataAtual) throws Exception {
        validateKey(key);
        final String urlIntegracaoTurmaZW = url + "/prest/integracaoTurma?operacao=obterContratoVigentePorPessoaSintetico&key=" + key + "&codigoPessoa=" + codigoPessoa + "&dataAtual=" + Uteis.getDataAplicandoFormatacao(dataAtual, "dd/MM/yyyy");

        CloseableHttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
        HttpPost httpPost = new HttpPost(urlIntegracaoTurmaZW);
        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        String body = handler.handleResponse(response);
        client.close();
        return new JSONObject(body);
    }

    private static void validateKey(final String key) throws ManyDataBasesException {
        EntityManagerFactoryService.getPropsKey(key);
    }
}
