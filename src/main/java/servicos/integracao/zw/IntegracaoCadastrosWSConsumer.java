/**
 * <AUTHOR>
 */
package servicos.integracao.zw;

import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.bean.bi.AcessoBIJSON;
import br.com.pacto.bean.bi.AcessosExecucoesJSON;
import br.com.pacto.bean.bi.DadosBITreinoJSON;
import br.com.pacto.bean.bi.FiltrosDashboard;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.controller.json.log.LogJSON;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.JSONMapper;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.intf.Validacao;
import br.com.pacto.util.bean.GenericoTO;
import org.json.JSONArray;
import org.json.JSONObject;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import servicos.integracao.zw.client.IntegracaoCadastrosWS;
import servicos.integracao.zw.client.IntegracaoCadastrosWS_Service;
import servicos.integracao.zw.json.AddClienteJSON;
import servicos.integracao.zw.json.AddColaboradorJSON;
import servicos.integracao.zw.json.AmbienteJSON;
import servicos.integracao.zw.json.AtestadoClienteJSON;
import servicos.integracao.zw.json.ClienteBIJSON;
import servicos.integracao.zw.json.ColetorJSON;
import servicos.integracao.zw.json.ModalidadeJSON;

import javax.xml.namespace.QName;
import javax.xml.ws.BindingProvider;
import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
@Component
public class IntegracaoCadastrosWSConsumer {


    private static final Map<String, Boolean> LOAD_MAP = Collections.synchronizedMap(new HashMap<>());
    private static Map<String, IntegracaoCadastrosWS> connectorMap = Collections.synchronizedMap(new HashMap<>());

    @Autowired
    private Validacao v;

    public static void clean() {
        connectorMap = null;
        connectorMap = new HashMap<>();
    }

    public static Map<String, IntegracaoCadastrosWS> getConnectorMap() {
        if (connectorMap == null) {
            connectorMap = new HashMap<>();
        }
        return connectorMap;
    }

    private IntegracaoCadastrosWS getInstance(String urlServicoParam) {
        try {
            if (LOAD_MAP != null && LOAD_MAP.get(urlServicoParam) != null && LOAD_MAP.get(urlServicoParam)) {
                try {
                    Thread.sleep(5000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }

            if (getConnectorMap().get(urlServicoParam) == null) {
                LOAD_MAP.put(urlServicoParam, true);

                URL u = new URL(urlServicoParam + "/IntegracaoCadastrosWS?wsdl");
                QName qName = new QName("http://webservice.basico.comuns.negocio/", "IntegracaoCadastrosWS");
                IntegracaoCadastrosWS intf = new IntegracaoCadastrosWS_Service(u, qName).getIntegracaoCadastrosWSPort();

                Map<String, Object> reqContext = ((BindingProvider) intf).getRequestContext();
                reqContext.put("com.sun.xml.ws.request.timeout", 30000);
                reqContext.put("com.sun.xml.ws.connect.timeout", 5000);

                getConnectorMap().put(urlServicoParam, intf);
                return intf;
            } else {
                return getConnectorMap().get(urlServicoParam);
            }
        } catch (MalformedURLException ex) {
            Logger.getLogger(IntegracaoCadastrosWSConsumer.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            LOAD_MAP.remove(urlServicoParam);
        }
        return null;
    }

    public List<AddClienteJSON> consultarClientesZW(final String url, final String key,
                                                    final Integer empresa,
                                                    final String cpf, final String nome, final Integer matricula) throws Exception {
        final String resultado = getInstance(url).consultarClientesTreino(cpf, key, empresa, matricula, nome);
        if (v.isEmpty(resultado)) {
            return null;
        }
        JSONArray arr = new JSONArray(resultado);
        return JSONMapper.getList(arr, AddClienteJSON.class);
    }


    public ClienteSintetico consultarClienteSintetico(final String url, final String key,
                                                      final Integer codigoCliente) throws Exception {
        final String retorno = getInstance(url).consultarClienteSintetico(codigoCliente, key);
//        System.out.println(retorno);
        JSONObject o = new JSONObject(retorno);
        if (!o.isNull("codigoCliente")) {
            return new ClienteSintetico(o);
        } else {
            return null;
        }
    }

    public List<AddColaboradorJSON> consultarColaboradores(final String url, final String key,
                                                           final Integer empresa,
                                                           final String nome,
                                                           final Boolean incluirPersonais, final Boolean todosProfessores) throws Exception {
        final String resultado = getInstance(url).consultarColaboradoresTreino(key, empresa, nome, incluirPersonais, todosProfessores);
        if (v.isEmpty(resultado)) {
            return null;
        }
        JSONArray arr = new JSONArray(resultado);
        return JSONMapper.getList(arr, AddColaboradorJSON.class);
    }

    public String gerarUsuarioMovelAluno(final String url, final String key,
            final String username,
            final String senha,
            final Integer codigoCliente, final String nomeApp, final String appUrlEmail, String nomeEmpresa) throws Exception {
        return getInstance(url).gerarUsuarioMovelAluno(codigoCliente, key, senha, username, appUrlEmail, nomeEmpresa);
    }

    public String alterarUsuarioMovelAluno(final String url, final String key,
            final String username,
            final String senha,
            final Integer codigoCliente, final String nomeApp, final String appUrlEmail) throws Exception {
        return getInstance(url).alterarUsuarioMovelAluno(codigoCliente, key, senha, username, nomeApp, appUrlEmail);
    }

    public String alterarUsuarioMovelColaborador(final String url, final String key,
            final String username,
            final String senha,
            final Integer codigoColaborador, final String nomeApp, final String appUrlEmail) throws Exception {
        return getInstance(url).alterarUsuarioMovelProfessor(codigoColaborador, key, senha, username, nomeApp, appUrlEmail);
    }

    public String gerarVinculoProfessorAluno(final String url, final String key, final Integer codigoCliente,
                                             final Integer codigoProfessor, final Integer codigoUsuario) throws Exception {
        return getInstance(url).gerarVinculoProfessorAluno(codigoCliente, codigoProfessor, codigoUsuario, key);
    }

    public Integer obterDiasParcelaEmAberto(final String url, final String key, final Integer codigoPessoa) throws Exception {
        return getInstance(url).consultarNrDiasParcelaEmAberto(codigoPessoa, key);
    }

    public void atualizarSaldoPersonal(final String url, final String key,
                                       final Integer codigoColaborador, final Integer saldo) throws Exception {
        getInstance(url).atualizarCadastroPersonal(codigoColaborador, Boolean.TRUE, saldo, null, key);
    }

    public void atualizarEmAtendimento(final String url, final String key,
                                       final Integer codigoColaborador, final boolean emAtendimento) throws Exception {
        getInstance(url).atualizarCadastroPersonal(codigoColaborador, Boolean.FALSE, null, emAtendimento, key);
    }

    public String gerarVendaCredito(final String url, final String key, final Integer codigoColaborador,
                                    final Integer codigoUsuario, final Integer numeroCreditos) throws Exception {
        return getInstance(url).gerarVendaCreditos(codigoColaborador, codigoUsuario, numeroCreditos, key);
    }

    public String enviarEmailColaborador(final String url, final String key, final Integer codigoColaborador, String texto) throws Exception {
        return getInstance(url).enviarEmailColaborador(codigoColaborador, key, texto);
    }

    public Integer obterDuracaoCreditos(final String url, final String key, final Integer empresa) throws Exception {
        return getInstance(url).obterDuracaoCreditosEmpresa(empresa, key);
    }

    public DadosBITreinoJSON obterDadosBI(final String url, final String key,final Integer professor,
                                          FiltrosDashboard filtrosDashboard, Date dataProcessamento) throws Exception{
        try {
            Date inicioAVencer = dataProcessamento;
            Date fimAvencer = Uteis.somarDias(dataProcessamento, filtrosDashboard.getDiasParaFrente());
            Date fim = dataProcessamento;
            Date inicio = Uteis.somarDias(dataProcessamento, -filtrosDashboard.getDiasParaTras());
            String dadosBITreino = getInstance(url).consultarDadosBITreino(key, Uteis.getData(inicio) ,
                    Uteis.getData(fim), Uteis.getData(inicioAVencer), Uteis.getData(fimAvencer), professor, filtrosDashboard.getEmpresa());
            return JSONMapper.getObject(new JSONObject(dadosBITreino), DadosBITreinoJSON.class);
        } catch (Exception e) {
            Uteis.logar(e, IntegracaoCadastrosWSConsumer.class);
            return new DadosBITreinoJSON();
        }
    }

    public DadosBITreinoJSON obterDadosBIRanking(final String url, final String key,
                                                 final Integer professor,
                                                 final Integer codEmpresaZW,
                                          Date inicio, Date fim) throws Exception{
        try {
            String dadosBITreino = getInstance(url).consultarDadosBITreino(key, Uteis.getData(inicio) ,
                    Uteis.getData(fim), Uteis.getData(inicio), Uteis.getData(fim), professor, codEmpresaZW);
            return JSONMapper.getObject(new JSONObject(dadosBITreino), DadosBITreinoJSON.class);
        } catch (Exception e) {
            Uteis.logar(e, IntegracaoCadastrosWSConsumer.class);
            return new DadosBITreinoJSON();
        }
    }

    public List<ClienteBIJSON> consultarClientesRenovaramOuNao(final String url, final String key,final Integer professor,
                                                               FiltrosDashboard filtrosDashboard, Boolean naoRenovaram) throws Exception {
        Date fim = Calendario.hoje();
        Date inicio = Uteis.somarDias(Calendario.hoje(), -filtrosDashboard.getDiasParaTras());
        final String resultado = getInstance(url).consultarClientesRenovaramOuNao(key, Uteis.getData(inicio),
                Uteis.getData(fim), professor,filtrosDashboard.getEmpresa(), naoRenovaram.toString());
        JSONArray arr = new JSONArray(resultado);
        return JSONMapper.getList(arr, ClienteBIJSON.class);
    }

    public List<ModalidadeJSON> consultarModalidades(final String url, final String key, final Integer empresa) throws Exception {
        final String resultado = getInstance(url).consultarModalidadesEmpresa(key, empresa);
        if (v.isEmpty(resultado)) {
            return null;
        }
        JSONArray arr = new JSONArray(resultado);
        return JSONMapper.getList(arr, ModalidadeJSON.class);
    }

    public List<AmbienteJSON> consultarAmbientes(final String url, final String key) throws Exception {
        final String resultado = getInstance(url).consultarAmbientes(key);
        if (v.isEmpty(resultado)) {
            return null;
        }
        JSONArray arr = new JSONArray(resultado);
        return JSONMapper.getList(arr, AmbienteJSON.class);
    }

    public List<ClienteBIJSON> consultarClientesAtivosForaTreino(final String url, final String key,
                                                                 final Integer empresa, final String filtroModalidades) throws Exception {
        final String resultado = getInstance(url).consultarClientesAtivosForaTreino(key, empresa, filtroModalidades.replaceAll(",", "-"));
        if (v.isEmpty(resultado)) {
            return null;
        }
        JSONArray arr = new JSONArray(resultado);
        return JSONMapper.getList(arr, ClienteBIJSON.class);
    }

    public List<ClienteBIJSON> consultarClientesForaTreino(final String url, final String key,
                                                                 final Integer empresa) throws Exception {
        final String resultado = getInstance(url).consultarClientesForaTreino(key, empresa);
        if (v.isEmpty(resultado)) {
            return null;
        }
        JSONArray arr = new JSONArray(resultado);
        return JSONMapper.getList(arr, ClienteBIJSON.class);
    }

    public List<ModalidadeJSON> consultarModalidadesClientesAtivosForaTreino(final String url, final String key,
                                                                             final Integer empresa) throws Exception {
        final String resultado = getInstance(url).consultarModalidadesAtivosForaTreino(key, empresa);
        if (v.isEmpty(resultado)) {
            return null;
        }
        JSONArray arr = new JSONArray(resultado);
        return JSONMapper.getList(arr, ModalidadeJSON.class);
    }

    public List<ClienteBIJSON> consultarAlunos(final String url, final String key, final String codigos, final String filter) throws Exception {
        String resultado = getInstance(url).consultarAlunos(key, codigos, filter);
        JSONArray arr = new JSONArray(resultado);
        return JSONMapper.getList(arr, ClienteBIJSON.class);
    }

    public List<AcessosExecucoesJSON> obterAcessos(final String url, final String key,final Integer professor,
                                                   FiltrosDashboard filtrosDashboard) throws Exception{
        return obterAcessos(url, key,professor, filtrosDashboard, Calendario.hoje());
    }

    public List<AcessosExecucoesJSON> obterAcessos(final String url, final String key,final Integer professor,
                                                   FiltrosDashboard filtrosDashboard, Date fim) throws Exception{
        Date inicio = Uteis.somarDias(fim, -filtrosDashboard.getDiasParaTras());
        String dadosAcesso = getInstance(url).consultarAcessosDia(key, Uteis.getData(inicio),Uteis.getData(fim),professor, filtrosDashboard.getEmpresa());
        JSONArray arr = new JSONArray(dadosAcesso);
        return JSONMapper.getList(arr, AcessosExecucoesJSON.class);
    }

    public List<AcessoBIJSON> obterListaAcessos(final String url, final String key,final Integer professor,
                                                Date dia, Integer empresa, Boolean apenasTreino) throws Exception{
        String dadosAcesso = getInstance(url).consultarAcessosListaDia(key, Uteis.getData(dia), Uteis.getData(dia), professor, empresa, apenasTreino.toString());
        JSONArray arr = new JSONArray(dadosAcesso);
        return JSONMapper.getList(arr, AcessoBIJSON.class);
    }

    public JSONObject obterDadosAtendimentoSol(final String url, final String key,final Integer codigoEmpresaZW,final Integer codigoUsuarioZW) throws Exception{
        String dadosSolicitarAtendimento = getInstance(url).consultarDadosSolicitarAtendimento(key,codigoEmpresaZW,codigoUsuarioZW);
        return new JSONObject(dadosSolicitarAtendimento);
    }
    public List<AtestadoClienteJSON> consultarAtestadoCliente(final String url, final String key,final Integer matriculaCliente) throws Exception{
        String resultado = getInstance(url).consultarAtestadoCliente(key, matriculaCliente);
        if (resultado.startsWith("ERROR")) {
            System.out.println("[Retorno consultarAtestadoCliente no ZW com problema: " + resultado + "]");
        }
        List<AtestadoClienteJSON> atestados = new ArrayList<>();
        JSONArray array = new JSONArray(resultado);
        for(int i = 0; i < array.length(); i++){
            AtestadoClienteJSON atestado = new AtestadoClienteJSON();
            JSONObject jsonObject = array.getJSONObject(i);
            atestado.setDescricao(jsonObject.optString("descricao"));
            atestado.setObservacao(jsonObject.optString("observacao"));
            atestado.setCodAtestado(jsonObject.optInt("codAtestado"));
            atestado.setExtensao(jsonObject.optString("extensao"));
            atestado.setNomeArquivoGerado(jsonObject.optString("nomeArquivoGerado"));
            atestado.setParq(jsonObject.optBoolean("parq"));
            try {
                atestado.setDataInicio(Uteis.getDate(jsonObject.optString("dataInicio"), "yyyy-MM-dd"));
                atestado.setDataFinal(Uteis.getDate(jsonObject.optString("dataFinal"), "yyyy-MM-dd"));
            }catch (Exception e){
                Uteis.logar(e, IntegracaoCadastrosWSConsumer.class);
            }
            atestados.add(atestado);
        }
        return atestados;
    }

    public JSONObject configsEmail(final String url) throws Exception{
        String c = getInstance(url).obterConfiguracaoEmailPadrao();
        return new JSONObject(c);
    }

    public void gravarResposta(final String url, final String key,final Integer codigoNotf, String resposta) throws Exception{
        getInstance(url).gravarResposta(key, codigoNotf.toString(), resposta);
    }

    public String nomenclaturaVendaCredito(final String url, final String key, final String matricula) throws Exception {
        return getInstance(url).nomenclaturaVendaCredito(key, matricula);
    }

    public String listaAlunoPontos(final String url, final String key, final String situacao,boolean analitico,Integer cliente,String nomeCliente) throws Exception{
        return getInstance(url).listaAlunoPontos(key, situacao,analitico,cliente,nomeCliente);
    }

    public String listaAlunoPontosData(final String url, final String key, final String situacao,boolean analitico,Integer cliente,String nomeCliente, Date dataInicio, Date dataFinal) throws Exception{
        return getInstance(url).listaAlunoPontosData(key, situacao,analitico,cliente,nomeCliente, Uteis.getData(dataInicio), Uteis.getData(dataFinal));
    }

    public List<GenericoTO> produtosAtestadoZW(final String url, final String key) throws Exception{
        return consultarProdutosZW(url, key, true);
    }

    public List<GenericoTO> produtosServicosZW(final String url, final String key) throws Exception{
        return consultarProdutosZW(url, key, false);
    }

    private List<GenericoTO> consultarProdutosZW(final String url, final String key, boolean atestado) throws Exception{
        String produtosServicos = "";
        if (atestado) {
            produtosServicos = getInstance(url).listaProdutosAtestado(key);
        } else {
            produtosServicos = getInstance(url).listaProdutosServicos(key);
        }
        JSONArray array = new JSONArray();
        if (!produtosServicos.contains("Não foi encontrado empresa com essa chave")){
            array = new JSONArray(produtosServicos);
        }
        List<GenericoTO> produtos = new ArrayList<GenericoTO>();
        for(int i = 0; i < array.length(); i++){
            JSONObject json = array.getJSONObject(i);
            produtos.add(new GenericoTO(json.getInt("codigo"), json.getString("descricao") + " - " + json.getString("valorApresentar")));
        }
        return produtos;
    }

    public String lancarProdutoAtestado(final String url, final String key, boolean incluir, Integer cliente, Integer usuario, Integer produto, Integer avaliacaoFisica, boolean parqpositivo, String observacao, Date dataInicio, Date dataFinal) throws Exception {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("incluir", incluir);
        jsonObject.put("cliente", cliente);
        jsonObject.put("usuario", usuario);
        jsonObject.put("produto", produto);
        jsonObject.put("avaliacaoFisica", avaliacaoFisica);
        jsonObject.put("parqpositivo", parqpositivo);
        jsonObject.put("observacao", observacao);
        jsonObject.put("dataInicio", Uteis.getData(dataInicio));
        jsonObject.put("dataFinal", Uteis.getData(dataFinal));
        return getInstance(url).lancarProdutoAtestado(key, jsonObject.toString());
    }

    public String verificarAlunoTemProdutoVigente(final String url, final String key, final Integer codigoClienteZW, final Integer produto) throws Exception{
        return getInstance(url).validarAlunoProdutoVigente(key, codigoClienteZW, produto);
    }

    public String verificarAlunoTemProdutoVigenteRetornandoQuantidade(final String url, final String key, final Integer codigoClienteZW, final Integer produto) throws Exception{
        return getInstance(url).validarProdutoVigente(key, codigoClienteZW, produto);
    }

    public String lancarProdutoAluno(final String url, final String key,final Integer codigoClienteZW,
                                     final Integer produto, final Integer usuario, final Date vencimento) throws Exception{
        return getInstance(url).lancarProdutoCliente(key, codigoClienteZW, usuario, produto, Uteis.getData(vencimento));
    }


    public String lancarProdutoAlunoValor(final String url, final String key,final Integer codigoClienteZW,
                                          final Integer produto, final Integer usuario, final Date vencimento, final Double valor) throws Exception{
        return getInstance(url).lancarProdutoClienteVAlor(key, codigoClienteZW, usuario, produto, Uteis.getData(vencimento), valor);
    }

    public String listaAlunoPontosApp(final String url,final String key,final String matriculaCliente,boolean sintetico) throws Exception{
        return getInstance(url).listaAlunoPontosApp(key, matriculaCliente,sintetico);
    }

    public String alterarUsuarioMovelAlunoNovoAppAluno(final String url, final String key,
                                           final String username,
                                           final String senha,
                                           final Integer codigoCliente,
                                                       final String nomeApp, final String appUrlEmail) throws Exception {
        return getInstance(url).alterarUsuarioMovelAlunoNovo(codigoCliente, key, senha, username, nomeApp, appUrlEmail);
    }

    public String obterValorProdutoCfgEmpresa(final String url, final String key, final Integer produto, final Integer empresa) throws Exception{
        return getInstance(url).obterValorProdutoCfgEmpresa(key, produto, empresa);
    }

    public String persistirLog(final String url, final String key, LogJSON logJSON) throws Exception {
        return getInstance(url).persistirLogAuditoria(key, logJSON.toJSON());
    }

    public String obterPontosPorCliente(final String url, final String key, Integer cliente) throws Exception {
        return getInstance(url).obterPontosPorCliente(key, cliente);
    }

    public List<ColetorJSON> consultarLocaisAcessoPorEmpresa(final String url, final String key, Integer empresa) throws Exception {
        String resultado = getInstance(url).buscarLocaisAcesso(key, empresa);
        JSONArray arr = new JSONArray(resultado);
        return JSONMapper.getList(arr, ColetorJSON.class);
    }

    public Map<Integer, String> tiposColaboradores(final String url, final String key, final Integer colaborador) throws Exception{
        String s = getInstance(url).obterTiposColaboradoes(key, colaborador);
        Map<Integer, String> mapa = new HashMap<>();
        JSONArray array = new JSONArray(s);
        for(int i = 0; i < array.length(); i++){
            mapa.put(array.getJSONObject(i).getInt("colaborador"),
                    array.getJSONObject(i).getString("tipos"));
        }
        return mapa;
    }


    public ColetorJSON consultarLocaisAcessoPorEmpresa(final String url, final String key, String nfc) throws Exception {
        String resultado = getInstance(url).consultarLocalAcessoPorNFC(key, nfc);
        JSONObject arr = new JSONObject(resultado);
        return JSONMapper.getObject(arr, ColetorJSON.class);
    }

    public String buscarClientesPesquisaTW(String url, String contexto) throws Exception {
        return getInstance(url)
                .buscarClientesPesquisaTW(contexto);
    }

    public String consultarFotoKeyPessoa(final String url, final String key, Integer codigoPessoa) throws Exception {
        return getInstance(url).consultarFotoKeyPessoa(codigoPessoa, key);
    }

    public String consultarQuantidadeAcessosClientesAgrupadosDia(final String url, final String key, Integer codCliente, Long dataInicial,
                                                                 Long dataFinal)  throws Exception {

        return getInstance(url).consultarQuantidadeAcessosClientesAgrupadosDia(key, codCliente, dataInicial, dataFinal);
    }

    public String enviarEmailAngular(final String url, final String key, final String json) throws Exception {
        return getInstance(url).enviarEmailAngular(key,json);
    }

    public void verificarUsuarioMovel(final String url, final String key, Integer codigoCliente, Integer matricula) throws Exception {
        getInstance(url).verificarUsuarioMovel(key, codigoCliente, matricula);
    }

    public String gravarUtilizacaoAvaliacaoFisica(String key, Integer codigoCliente, Integer codigoAvaliacaoFisica, Date dataAvaliacao) throws Exception {
        String url = Aplicacao.getProp(key, Aplicacao.urlZillyonWebInteg);
        final String urlIntegracaoCadastrosZW = url + "/prest/integracaoCadastro?operacao=gravarUtilizacaoAvaliacaoFisica&key=" + key +
                "&codigoCliente=" + codigoCliente +
                "&codigoAvaliacaoFisica=" + codigoAvaliacaoFisica +
                "&dataAvaliacao=" + Uteis.getDataAplicandoFormatacao(dataAvaliacao, "dd/MM/yyyy%20HH:mm:ss");

        return getSimpleHTTPResponse(urlIntegracaoCadastrosZW);
    }

    public String removerUtilizacaoAvaliacaoFisica(String key, Integer codigoAvaliacaoFisica) throws Exception {
        String url = Aplicacao.getProp(key, Aplicacao.urlZillyonWebInteg);
        final String urlIntegracaoCadastrosZW = url + "/prest/integracaoCadastro?operacao=removerUtilizacaoAvaliacaoFisica&key=" + key + "&codigoAvaliacaoFisica=" + codigoAvaliacaoFisica;

        return getSimpleHTTPResponse(urlIntegracaoCadastrosZW);
    }

    private String getSimpleHTTPResponse(String urlIntegracao) throws IOException {
        CloseableHttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
        HttpPost httpPost = new HttpPost(urlIntegracao);
        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        String body = handler.handleResponse(response);
        client.close();

        return body;
    }
}
