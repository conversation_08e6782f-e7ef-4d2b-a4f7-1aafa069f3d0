
package servicos.integracao.zw.client;

import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceException;
import javax.xml.ws.WebServiceFeature;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.6b21 
 * Generated source version: 2.1
 * 
 */
@WebServiceClient(name = "ConviteAulaExperimentalWS", targetNamespace = "http://webservice.basico.comuns.negocio/", wsdlLocation = "https://app.pactosolucoes.com.br/app/ConviteAulaExperimentalWS?wsdl")
public class ConviteAulaExperimentalWS_Service
    extends Service
{

    private final static URL CONVITEAULAEXPERIMENTALWS_WSDL_LOCATION;
    private final static WebServiceException CONVITEAULAEXPERIMENTALWS_EXCEPTION;
    private final static QName CONVITEAULAEXPERIMENTALWS_QNAME = new QName("http://webservice.basico.comuns.negocio/", "ConviteAulaExperimentalWS");

    static {
        URL url = null;
        WebServiceException e = null;
        try {
            url = new URL("https://app.pactosolucoes.com.br/app/ConviteAulaExperimentalWS?wsdl");
        } catch (MalformedURLException ex) {
            e = new WebServiceException(ex);
        }
        CONVITEAULAEXPERIMENTALWS_WSDL_LOCATION = url;
        CONVITEAULAEXPERIMENTALWS_EXCEPTION = e;
    }

    public ConviteAulaExperimentalWS_Service() {
        super(__getWsdlLocation(), CONVITEAULAEXPERIMENTALWS_QNAME);
    }

    public ConviteAulaExperimentalWS_Service(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    /**
     * 
     * @return
     *     returns ConviteAulaExperimentalWS
     */
    @WebEndpoint(name = "ConviteAulaExperimentalWSPort")
    public ConviteAulaExperimentalWS getConviteAulaExperimentalWSPort() {
        return super.getPort(new QName("http://webservice.basico.comuns.negocio/", "ConviteAulaExperimentalWSPort"), ConviteAulaExperimentalWS.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns ConviteAulaExperimentalWS
     */
    @WebEndpoint(name = "ConviteAulaExperimentalWSPort")
    public ConviteAulaExperimentalWS getConviteAulaExperimentalWSPort(WebServiceFeature... features) {
        return super.getPort(new QName("http://webservice.basico.comuns.negocio/", "ConviteAulaExperimentalWSPort"), ConviteAulaExperimentalWS.class, features);
    }

    private static URL __getWsdlLocation() {
        if (CONVITEAULAEXPERIMENTALWS_EXCEPTION!= null) {
            throw CONVITEAULAEXPERIMENTALWS_EXCEPTION;
        }
        return CONVITEAULAEXPERIMENTALWS_WSDL_LOCATION;
    }

}
