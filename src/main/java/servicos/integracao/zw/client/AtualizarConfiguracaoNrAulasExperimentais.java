
package servicos.integracao.zw.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de atualizarConfiguracaoNrAulasExperimentais complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType name="atualizarConfiguracaoNrAulasExperimentais">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="key" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="nrAulasExperimentaisAntesAlteracao" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="valorNovo" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "atualizarConfiguracaoNrAulasExperimentais", propOrder = {
    "key",
    "nrAulasExperimentaisAntesAlteracao",
    "valorNovo"
})
public class AtualizarConfiguracaoNrAulasExperimentais {

    protected String key;
    protected Integer nrAulasExperimentaisAntesAlteracao;
    protected Integer valorNovo;

    /**
     * Obtém o valor da propriedade key.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKey() {
        return key;
    }

    /**
     * Define o valor da propriedade key.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKey(String value) {
        this.key = value;
    }

    /**
     * Obtém o valor da propriedade nrAulasExperimentaisAntesAlteracao.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getNrAulasExperimentaisAntesAlteracao() {
        return nrAulasExperimentaisAntesAlteracao;
    }

    /**
     * Define o valor da propriedade nrAulasExperimentaisAntesAlteracao.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setNrAulasExperimentaisAntesAlteracao(Integer value) {
        this.nrAulasExperimentaisAntesAlteracao = value;
    }

    /**
     * Obtém o valor da propriedade valorNovo.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getValorNovo() {
        return valorNovo;
    }

    /**
     * Define o valor da propriedade valorNovo.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setValorNovo(Integer value) {
        this.valorNovo = value;
    }

}
