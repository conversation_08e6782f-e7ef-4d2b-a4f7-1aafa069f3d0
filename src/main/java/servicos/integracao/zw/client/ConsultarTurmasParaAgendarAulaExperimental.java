
package servicos.integracao.zw.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de consultarTurmasParaAgendarAulaExperimental complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType name="consultarTurmasParaAgendarAulaExperimental">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="key" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="inicio" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="fim" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="codigoConvite" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="consultarAulaCheia" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="consultarTurmaZW" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "consultarTurmasParaAgendarAulaExperimental", propOrder = {
    "key",
    "inicio",
    "fim",
    "codigoConvite",
    "consultarAulaCheia",
    "consultarTurmaZW"
})
public class ConsultarTurmasParaAgendarAulaExperimental {

    protected String key;
    protected String inicio;
    protected String fim;
    protected Integer codigoConvite;
    protected Integer consultarAulaCheia;
    protected Integer consultarTurmaZW;

    /**
     * Obtém o valor da propriedade key.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKey() {
        return key;
    }

    /**
     * Define o valor da propriedade key.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKey(String value) {
        this.key = value;
    }

    /**
     * Obtém o valor da propriedade inicio.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getInicio() {
        return inicio;
    }

    /**
     * Define o valor da propriedade inicio.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setInicio(String value) {
        this.inicio = value;
    }

    /**
     * Obtém o valor da propriedade fim.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFim() {
        return fim;
    }

    /**
     * Define o valor da propriedade fim.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFim(String value) {
        this.fim = value;
    }

    /**
     * Obtém o valor da propriedade codigoConvite.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCodigoConvite() {
        return codigoConvite;
    }

    /**
     * Define o valor da propriedade codigoConvite.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCodigoConvite(Integer value) {
        this.codigoConvite = value;
    }

    /**
     * Obtém o valor da propriedade consultarAulaCheia.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getConsultarAulaCheia() {
        return consultarAulaCheia;
    }

    /**
     * Define o valor da propriedade consultarAulaCheia.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setConsultarAulaCheia(Integer value) {
        this.consultarAulaCheia = value;
    }

    /**
     * Obtém o valor da propriedade consultarTurmaZW.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getConsultarTurmaZW() {
        return consultarTurmaZW;
    }

    /**
     * Define o valor da propriedade consultarTurmaZW.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setConsultarTurmaZW(Integer value) {
        this.consultarTurmaZW = value;
    }

}
