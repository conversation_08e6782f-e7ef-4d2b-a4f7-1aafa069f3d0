
package servicos.integracao.zw.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de listaClientesEstacionamentoSelfit complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType name="listaClientesEstacionamentoSelfit">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="key" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="listaPessoas" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "listaClientesEstacionamentoSelfit", propOrder = {
    "key",
    "listaPessoas"
})
public class ListaClientesEstacionamentoSelfit {

    protected String key;
    protected String listaPessoas;

    /**
     * Obtém o valor da propriedade key.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKey() {
        return key;
    }

    /**
     * Define o valor da propriedade key.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKey(String value) {
        this.key = value;
    }

    /**
     * Obtém o valor da propriedade listaPessoas.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getListaPessoas() {
        return listaPessoas;
    }

    /**
     * Define o valor da propriedade listaPessoas.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setListaPessoas(String value) {
        this.listaPessoas = value;
    }

}
