/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.integracao.midias.awss3;

import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import com.amazonaws.AmazonClientException;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.regions.Region;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.Bucket;
import com.amazonaws.services.s3.model.GetObjectRequest;
import com.amazonaws.services.s3.model.ListObjectsRequest;
import com.amazonaws.services.s3.model.ObjectListing;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectSummary;
import com.amazonaws.services.s3.transfer.TransferManager;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.apache.commons.io.IOUtils;
import servicos.integracao.midias.MidiaService;
import servicos.integracao.midias.commons.MidiaEntidadeEnum;

/**
 *
 * <AUTHOR>
 */
public class AmazonS3Client extends MidiaService {

    private AmazonS3 s3 = null;
    private String bucketName = "prod-zwphotos";

    public String getBucketName() {
        return bucketName;
    }

    public AmazonS3Client() {
        internal();
    }

    public AmazonS3Client(final String bucketName) {
        this.bucketName = bucketName;
        internal();
    }

    private void internal() {
        try {
            s3 = new com.amazonaws.services.s3.AmazonS3Client(new AWSCredentials() {
                @Override
                public String getAWSAccessKeyId() {
                    return "********************";
                }

                @Override
                public String getAWSSecretKey() {
                    return "GBHnWy3xl22G85vM0jdrYQ6LBisocM/wdiSvM5Bs";
                }
            });
            Region region = Region.getRegion(Regions.DEFAULT_REGION);
            s3.setRegion(region);
            s3.setEndpoint("s3-sa-east-1.amazonaws.com");
        } catch (Exception e) {
            throw new AmazonClientException(
                    "AmazonS3ServiceClient.internal(): Nao pude inicializar Amazon S3 Client devido ao erro:"
                    + e.getMessage(),
                    e);
        }
    }

    public void listBuckets() {
        Uteis.logar(null, "Listing buckets");
        for (Bucket bucket : s3.listBuckets()) {
            Uteis.logar(null, " - " + bucket.getName());
        }
    }

    @Override
    public boolean exists(final String key) {
        try {
            s3.getObjectMetadata(bucketName, key);
            return true;
        } catch (Exception e) {
        }
        return false;
    }

    public void listBucketsContentsBetweenDate(final String prefix, final Date fromDate,
            final Date toDate) {
        ObjectListing objectListing = s3.listObjects(new ListObjectsRequest()
                .withBucketName(bucketName)
                .withPrefix(prefix));
        int i = 1;
        while (objectListing != null && objectListing.getObjectSummaries().size() > 0) {
            for (S3ObjectSummary objectSummary : objectListing.getObjectSummaries()) {
                if (Calendario.entre(objectSummary.getLastModified(), fromDate, toDate)) {
                    try {
                        final String key = objectSummary.getKey();
                        int inicio = key.lastIndexOf("/");
                        int fim = key.indexOf("==", inicio);
                        final String identificador = objectSummary.getKey().substring(inicio + 1, fim + 2);
                        System.out.println(String.format("%s - Object: %s size: %s data: %s identificador: %s",
                                i,
                                objectSummary.getKey(),
                                objectSummary.getSize(),
                                objectSummary.getLastModified(),
                                Uteis.desencriptarAWS(identificador)));
                    } catch (Exception ex) {
                        Logger.getLogger(AmazonS3Client.class.getName()).log(Level.SEVERE, null, ex);
                    }
                    i++;
                }
            }
            objectListing = s3.listNextBatchOfObjects(objectListing);
        }
    }

    public void listBucketsContents(final String prefix) {
        ObjectListing objectListing = s3.listObjects(new ListObjectsRequest()
                .withBucketName(bucketName)
                .withPrefix(prefix));
        int i = 1;
        while (objectListing != null && objectListing.getObjectSummaries().size() > 0) {
            for (S3ObjectSummary objectSummary : objectListing.getObjectSummaries()) {
                try {
                    final String key = objectSummary.getKey();
                    int inicio = key.lastIndexOf("/");
                    int fim = key.indexOf("==", inicio);
                    final String identificador = objectSummary.getKey().substring(inicio + 1, fim + 2);
                    Uteis.logar(null, String.format("%s - Object: %s size: %s data: %s identificador: %s",
                            i,
                            objectSummary.getKey(),
                            objectSummary.getSize(),
                            objectSummary.getLastModified(),
                            Uteis.desencriptarAWS(identificador)));

                } catch (Exception ex) {
                    Logger.getLogger(AmazonS3Client.class.getName()).log(Level.SEVERE, null, ex);
                }
                i++;
            }
            objectListing = s3.listNextBatchOfObjects(objectListing);
        }
    }

    public void deleteContentsBetween(final String prefix, final Date fromDate,
            final Date toDate) {
        ObjectListing objectListing = s3.listObjects(new ListObjectsRequest()
                .withBucketName(bucketName)
                .withPrefix(prefix));
        int i = 1;
        while (objectListing != null && objectListing.getObjectSummaries().size() > 0) {
            for (S3ObjectSummary objectSummary : objectListing.getObjectSummaries()) {
                if (Calendario.entre(objectSummary.getLastModified(), fromDate, toDate)) {
                    try {
                        final String key = objectSummary.getKey();
                        int inicio = key.lastIndexOf("/");
                        int fim = key.indexOf("==", inicio);
                        final String identificador = objectSummary.getKey().substring(inicio + 1, fim + 2);
                        Uteis.logar(null, String.format("%s - Object: %s size: %s data: %s identificador: %s",
                                i,
                                objectSummary.getKey(),
                                objectSummary.getSize(),
                                objectSummary.getLastModified(),
                                Uteis.desencriptarAWS(identificador)));
                        deleteObject(objectSummary.getKey());
                    } catch (Exception ex) {
                        Logger.getLogger(AmazonS3Client.class.getName()).log(Level.SEVERE, null, ex);
                    }
                    i++;
                }
            }
            objectListing = s3.listNextBatchOfObjects(objectListing);
        }
    }

    public void createBucket(final String bucketName) {
        s3.createBucket(bucketName);
    }

    @Override
    public String uploadObject(final String chave, MidiaEntidadeEnum tipo,
            final String identificador, File file) throws Exception {
        final String key = genKey(chave, tipo, identificador);
        Uteis.logar(null, "Uploading a new object to S3 from a file " + key);
        s3.putObject(new PutObjectRequest(bucketName, key, file));
        return key;
    }

    public String uploadObject(final String chave, MidiaEntidadeEnum tipo, File file) throws Exception {
        String identificador = file.getAbsolutePath();
        identificador = identificador.substring(identificador.indexOf(chave)).replaceAll("\\\\", "/");
        identificador = identificador.replaceAll("\\+", "*");
        Uteis.logar(null, "Uploading a new object to S3 from a file " + identificador);
        s3.putObject(new PutObjectRequest(bucketName, identificador, file));
        return identificador;
    }

    @Override
    public String uploadObjectFromByteArray(final String chave, MidiaEntidadeEnum tipo,
            final String identificador, byte[] bytes) throws Exception {
        if (bytes != null && bytes.length > 0) {
            final String key = genKey(chave, tipo, identificador);
            Uteis.logar(null, "Uploading a new object to S3 from a file " + key);
            ObjectMetadata om = new ObjectMetadata();
            om.setContentLength(bytes.length);
            ByteArrayInputStream bis = new ByteArrayInputStream(bytes);
            s3.putObject(bucketName, key, bis, om);
            return key;
        } else {
            return "ERRO: Array de Bytes informado � inv�lido!";
        }
    }

    @Override
    public String uploadObjectFromByteArrayV2(final String key, byte[] bytes) throws Exception {
        if (bytes != null && bytes.length > 0) {
            Uteis.logar(null, "Uploading a new object to S3 from a file " + key);
            ObjectMetadata om = new ObjectMetadata();
            om.setContentLength(bytes.length);
            ByteArrayInputStream bis = new ByteArrayInputStream(bytes);
            s3.putObject(bucketName, key, bis, om);
            return "ok";
        } else {
            return "ERRO: Array de Bytes informado é inválido!";
        }
    }

    @Override
    public String downloadObject(final String chave, MidiaEntidadeEnum tipo,
            final String identificador) throws Exception {
        boolean baixou = false;
        Uteis.logar(null, "Downloading an object");
        final String key = genKey(chave, tipo, identificador);
        S3Object object = s3.getObject(new GetObjectRequest(bucketName, key));
        Uteis.logar(null, "Content-Type: " + object.getObjectMetadata().getContentType());
        try {
            FileOutputStream fos = new FileOutputStream(key);
            BufferedReader reader = new BufferedReader(new InputStreamReader(object.getObjectContent()));
            while (true) {
                String line = reader.readLine();
                if (line == null) {
                    break;
                }
                fos.write(line.getBytes());
                baixou = true;
            }
            return key;
        } catch (IOException ex) {
            Logger.getLogger(AmazonS3Client.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }

    @Override
    public byte[] downloadObjectAsByteArray(final String chave, MidiaEntidadeEnum tipo, final String identificador) throws Exception {
        return downloadObjectWithExtensionAsByteArray(chave, tipo, identificador, null);
    }

    @Override
    public byte[] downloadObjectWithExtensionAsByteArray(final String chave, MidiaEntidadeEnum tipo, final String identificador, String extensao) throws Exception {
        String key;
        if (identificador == null) {
            if (tipo.name().contains("EMPRESA")) {
                key = "logoPadraoRelatorio.jpg";
            } else {
                key = "fotoPadrao.jpg";
            }
        } else {
            if (extensao == null) {
                key = genKey(chave, tipo, identificador);
            } else {
                key = genKey(chave, tipo, identificador, extensao);
            }

        }
        Uteis.logar(null, "Downloading an object " + key);
        byte[] foto = null;
        try {
            ObjectMetadata metadata = s3.getObjectMetadata(bucketName, key);
            if (metadata != null) {
                S3Object object = s3.getObject(new GetObjectRequest(bucketName, key));
                Uteis.logar(null, "Object: " + key + "\nContent-Type: " + object.getObjectMetadata().getContentType());
                try {
                    foto = IOUtils.toByteArray(object.getObjectContent());
                } catch (IOException ex) {
                    Logger.getLogger(AmazonS3Client.class.getName()).log(Level.SEVERE, null, ex);
                }
            } else {
                Uteis.logar(null, "Downloading an object FAILED: object not exists " + key);
            }
        } catch (Exception e) {
        }
        return foto;
    }

    @Override
    public void deleteObject(final String key) {
        s3.deleteObject(bucketName, key);
        Uteis.logar(null, "Removed object from S3 " + key);
    }

    public void uploadDirectory(final String prefixKeyDirectory, final String localDirectory) {
        TransferManager transferManager = new TransferManager(s3);
        transferManager.uploadDirectory(bucketName, prefixKeyDirectory,
                new File(localDirectory), true);
    }

    public String genKey(final String chave, MidiaEntidadeEnum tipo, final String identifier) throws Exception {
        return String.format("%s/%s/%s%s",
                chave,
                Uteis.encriptarAWS(tipo.name().toLowerCase()),
                Uteis.encriptarAWS(identifier),
                tipo.getExtensao());
    }

    public String genKey(final String chave, MidiaEntidadeEnum tipo, final String identifier, final String extensao) throws Exception {
        return String.format("%s/%s/%s%s",
                chave,
                Uteis.encriptarAWS(tipo.name().toLowerCase()),
                Uteis.encriptarAWS(identifier),
                extensao);
    }

    public static void main(String... args) {
        try {
            //        new AmazonS3Client().deleteContentsBeforeDate("706b5016c302165483e2fa8a535c02f7/97jxGKuba0*PGeIPqAkibQ==",
            //                Calendario.getInstance(2015, 8, 25).getTime());
//            new AmazonS3Client().listBucketsContentsBetweenDate("51c49d3456a082fe4bc340d84274dae1/97jxGKuba0*PGeIPqAkibQ==",
//                    Calendario.getInstance(2016, 01, 04).getTime(), Calendario.getInstance(2016, 01, 04, 14, 50, 0, 0).getTime());
//            new AmazonS3Client().listBucketsContents("51c49d3456a082fe4bc340d84274dae1/97jxGKuba0*PGeIPqAkibQ==");
            new AmazonS3Client().listBucketsContentsBetweenDate("51c49d3456a082fe4bc340d84274dae1/97jxGKuba0*PGeIPqAkibQ==",
                    Calendario.getInstance(2011, 01, 04).getTime(),
                    Calendario.getInstance(2016, 02, 01, 14, 50, 0, 0).getTime());

            Uteis.logar(null, Uteis.desencriptarAWS("92*TNBRA8hhJ6QtGO34@gA=="));
        } catch (Exception ex) {
            Logger.getLogger(AmazonS3Client.class.getName()).log(Level.SEVERE, null, ex);
        }

    }

    @Override
    public byte[] downloadObjectWithKeyByteArray(final String key) throws Exception {
        Uteis.logar(null, "Downloading an object " + key);
        byte[] foto = null;
        try {
            ObjectMetadata metadata = s3.getObjectMetadata(bucketName, key);
            if (metadata != null) {
                S3Object object = s3.getObject(new GetObjectRequest(bucketName, key));
                Uteis.logar(null, "Object: " + key + "\nContent-Type: " + object.getObjectMetadata().getContentType());
                try {
                    foto = IOUtils.toByteArray(object.getObjectContent());
                } catch (IOException ex) {
                    Logger.getLogger(AmazonS3Client.class.getName()).log(Level.SEVERE, null, ex);
                }
            } else {
                Uteis.logar(null, "Downloading an object FAILED: object not exists " + key);
            }
        } catch (Exception e) {
        }
        return foto;
    }
}
