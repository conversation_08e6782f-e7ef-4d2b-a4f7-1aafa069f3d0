/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.integracao.midias.zwinternal;

import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.FileUtilities;

import java.io.File;
import servicos.integracao.midias.MidiaService;
import servicos.integracao.midias.commons.MidiaEntidadeEnum;

/**
 * <AUTHOR>
 */
public class MidiaZWInternal extends MidiaService {

    @Override
    public String downloadObject(String chave, MidiaEntidadeEnum tipo, String identificador) throws Exception {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public byte[] downloadObjectWithKeyByteArray(final String key) throws Exception {
        throw new UnsupportedOperationException("Not supported yet.");
    }

    @Override
    public byte[] downloadObjectAsByteArray(String chave, MidiaEntidadeEnum tipo, String identificador) throws Exception {
        String path = Aplicacao.getProp(Aplicacao.diretorioFotos);
        final String key = genKey(chave, tipo, identificador);
        Uteis.logar(null, "Getting an object of ZW-INTERNAL as a ByteArray " + key);
        path += key;

        File file = new File(path);
        if (!file.exists()) {
            file = new File(Aplicacao.getProp(Aplicacao.diretorioFotos) + "fotoPadrao.jpg");
        }
        return FileUtilities.obterBytesArquivo(file);
    }

    public byte[] downloadObjectWithExtensionAsByteArray(final String chave, MidiaEntidadeEnum tipo, final String identificador, final String extensao) throws Exception {
        String path = Aplicacao.getProp(Aplicacao.diretorioFotos);
        final String key = genKey(chave, tipo, identificador, extensao);
        Uteis.logar(null, "Getting an object of ZW-INTERNAL as a ByteArray " + key);
        path += key;

        File file = new File(path);
        if (!file.exists()) {
            file = new File(Aplicacao.getProp(Aplicacao.diretorioFotos) + "fotoPadrao.jpg");
        }
        return FileUtilities.obterBytesArquivo(file);
    }

    @Override
    public boolean exists(String key) {
        String path = Aplicacao.getProp(Aplicacao.diretorioFotos);
        path += key;
        File file = new File(path);
        return file.exists();
    }

    @Override
    public void deleteObject(final String key) {
        if (exists(key)) {
            String path = Aplicacao.getProp(Aplicacao.diretorioFotos);
            path += key;
            File file = new File(path);
            file.delete();
            Uteis.logar(null, "Removed object from ZW-INTERNAL " + key);
        }
    }

    @Override
    public String uploadObject(String chave, MidiaEntidadeEnum tipo, String identificador, File file) throws Exception {
        String path = Aplicacao.getProp(Aplicacao.diretorioFotos);
        final String key = genKey(chave, tipo, identificador);
        Uteis.logar(null, "Uploading a new object to ZW-INTERNAL from a ByteArray " + identificador + key);
        path += key;

        File fileGerar = new File(path);
        FileUtilities.forceDirectory(fileGerar.getParent());
        FileUtilities.saveToFile(FileUtilities.obterBytesArquivo(file), path);
        return key;

    }

    @Override
    public String uploadObjectFromByteArray(String chave, MidiaEntidadeEnum tipo, String identificador, byte[] bytes) throws Exception {
        if (bytes != null && bytes.length > 0) {
            String path = Aplicacao.getProp(Aplicacao.diretorioFotos);
            final String key = genKey(chave, tipo, identificador);
            Uteis.logar(null, String.format("Uploading a new object %s %s %s to ZW-INTERNAL from a ByteArray with key %s",
                    chave, tipo, identificador, key));
            path += key;

            File file = new File(path);
            FileUtilities.forceDirectory(file.getParent());
            FileUtilities.saveToFile(bytes, path);
            return key;
        } else {
            return "ERRO: Array de Bytes informado � inv�lido!";
        }
    }

    @Override
    public String uploadObjectFromByteArrayV2(String key, byte[] bytes) throws Exception {
        return null;
    }

    public static void main(String[] args) {
//        try {
//            MidiaZWInternal midiaZWInternal = new MidiaZWInternal();
        //Testando uploadObjectFromByteArray;
//            Connection c = DriverManager.getConnection("************************************************", "postgres", "pactodb");
//            PreparedStatement ps = c.prepareStatement("SELECT codigo, foto FROM pessoa p WHERE LENGTH(foto) > 0 limit 10;");
//            ResultSet rs = ps.executeQuery();
//            while (rs.next()) {
//                System.out.println(rs.getString("codigo"));
//                midiaZWInternal.uploadObjectFromByteArray("sereia", MidiaEntidadeEnum.FOTO_PESSOA, rs.getString("codigo"), rs.getBytes("foto"));
//            }
        //Testando uploadObject;
//            File f = new File("C:\\Users\\<USER>\\Pictures\\travesseiro.jpg");
//            midiaZWInternal.uploadObject("sereia", MidiaEntidadeEnum.FOTO_PESSOA, "1", f);
        //Testando exists;
//            boolean existe = midiaZWInternal.exists("sereia/97jxGKuba0+PGeIPqAkibQ==/jtGo7xfGreYfjucCmU7FZg==.jpg");
//            System.out.println("Existe m�dia: " + existe);
//            existe = midiaZWInternal.exists("sereia/97jxGKuba0+PGeIPqAkibQ==/NaoPossoExistir.jpg");
//            System.out.println("Existe m�dia: " + existe);
        //Obtendo ByteArray;
//            System.out.println(midiaZWInternal.downloadObjectAsByteArray("sereia", MidiaEntidadeEnum.FOTO_PESSOA, "13591").length);
//        } catch (Exception ex) {
//            Uteis.logar(ex, MidiaZWInternal.class);
//        }
    }

    public String genKey(final String chave, MidiaEntidadeEnum tipo, final String identifier) throws Exception {
        return String.format("%s/%s/%s%s",
                chave,
                Uteis.encriptarZWInternal(tipo.name().toLowerCase()),
                Uteis.encriptarZWInternal(identifier),
                tipo.getExtensao());
    }

    public String genKey(final String chave, MidiaEntidadeEnum tipo, final String identifier, final String extensao) throws Exception {
        return String.format("%s/%s/%s%s", chave, Uteis.encriptarZWInternal(tipo.name().toLowerCase()), Uteis.encriptarZWInternal(identifier), extensao);
    }
}
