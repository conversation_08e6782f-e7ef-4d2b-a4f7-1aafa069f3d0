/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.integracao.midias;

import br.com.pacto.objeto.Aplicacao;
import java.io.File;
import servicos.integracao.midias.commons.MidiaConectividadeEnum;
import servicos.integracao.midias.commons.MidiaEntidadeEnum;

/**
 *
 * <AUTHOR>
 */
public abstract class MidiaService {

    private static MidiaService instance = null;

    /**
     * Gera um identificador �nico de armazenamento, baseado no tipo de m�dia e
     * retorna a URL para download
     *
     * @param chave
     * @param tipo
     * @param identificador
     * @return
     * @throws Exception
     */
    public abstract String downloadObject(final String chave, MidiaEntidadeEnum tipo,
            final String identificador) throws Exception;

    /**
     * Gera um identificador �nico de armazenamento, baseado no tipo de m�dia,
     * realiza o download do vetor de bytes e retorna-o
     *
     * @param chave
     * @param tipo
     * @param identificador
     * @return
     * @throws Exception
     */
    public abstract byte[] downloadObjectAsByteArray(final String chave,
            MidiaEntidadeEnum tipo, final String identificador) throws Exception;

    public abstract byte[] downloadObjectWithExtensionAsByteArray(final String chave, MidiaEntidadeEnum tipo, final String identificador, final String extensao) throws Exception;
    /**
     * Verifica se determinada midia com identificador �nico existe no
     * armazenamento, retornando VERDADEIRO em caso positivo
     *
     * @param key
     * @return
     */

    public abstract boolean exists(final String key);

    /**
     * Realiza upload de um arquivo a partir do seu descritor 'file'
     *
     * @param chave
     * @param tipo
     * @param identificador
     * @param file
     * @return
     * @throws Exception
     */
    public abstract String uploadObject(final String chave, MidiaEntidadeEnum tipo,
            final String identificador, File file) throws Exception;

    /**
     * Realiza upload de um arquivo a partir do seu conte�do em vetor de bytes
     *
     * @param chave
     * @param tipo
     * @param identificador
     * @param bytes
     * @return
     * @throws Exception
     */
    public abstract String uploadObjectFromByteArray(final String chave,
            MidiaEntidadeEnum tipo, final String identificador, byte[] bytes) throws Exception;

    /**
     * Sobreescreve um arquivo já existente
     *
     * @param key
     * @param bytes
     * @return
     * @throws Exception
     */
    public abstract String uploadObjectFromByteArrayV2(final String key, byte[] bytes) throws Exception;

    /**
     * Deleta da camada de armazenamento o arquivo associado
     *
     * @param key
     */
    public abstract void deleteObject(final String key);

    /**
     * Realiza a gera��o da chave
     */
    public abstract String genKey(final String chave,
            MidiaEntidadeEnum tipo, final String identifier) throws Exception;

    public abstract String genKey(final String chave, MidiaEntidadeEnum tipo, final String identifier, final String extensao) throws Exception;

    /**
     * Retorna inst�ncia Singleton da implementa��o do servi�o de Midias
     * especificada pela configura��o do ambiente
     *
     * @return
     * @throws Exception
     */
    public static MidiaService getInstance() throws Exception {
        if (instance == null) {
            MidiaConectividadeEnum type =
                    MidiaConectividadeEnum.valueOf(
                    Aplicacao.getProp(Aplicacao.typeMidiasService));
            instance = (MidiaService) type.getImpl().newInstance();
        }
        return instance;
    }

    //Usado somente quando em casas especificos
    private static MidiaService getInstanceAWS() throws Exception {
        if (instance == null) {
            MidiaConectividadeEnum type = MidiaConectividadeEnum.AWS_S3;
            instance = (MidiaService) type.getImpl().newInstance();
        }
        return instance;
    }

    public static MidiaService getInstanceAvaliacaoFisica() throws Exception {
        return getInstanceAWS();
    }
    public static MidiaService getInstanceBenchmark() throws Exception {
        return getInstanceAWS();
    }
    public static MidiaService getInstanceWood() throws Exception {
        return getInstanceAWS();
    }

    public static MidiaService getInstanceParceiro() throws Exception {
        return getInstanceAWS();
    }

    public abstract byte[] downloadObjectWithKeyByteArray(final String key) throws Exception;
}
