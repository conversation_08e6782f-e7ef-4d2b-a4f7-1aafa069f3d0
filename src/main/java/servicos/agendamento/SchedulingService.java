/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.agendamento;

import br.com.pacto.base.scheduling.agendamento.TaskAgendarRenovacaoTreino;
import br.com.pacto.base.scheduling.agendamento.TaskNotificationAgendamento;
import br.com.pacto.base.scheduling.gympass.TaskBookingGympass;
import br.com.pacto.base.scheduling.personal.TaskCheckOutAutomatico;
import br.com.pacto.base.scheduling.personal.TaskExpirarCreditos;
import br.com.pacto.base.scheduling.personal.TaskProcessarBIProfessores;
import br.com.pacto.base.scheduling.treino.TaskCompromissoTreino;
import it.sauronsoftware.cron4j.Task;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import org.springframework.context.support.ClassPathXmlApplicationContext;

/**
 *
 * <AUTHOR>
 */
public class SchedulingService {

    public static void main(String... args) {

        if (args.length == 0) {
            args = new String[]{"pacer", "AGENDA", "RENOVACAO_TREINO"};
        }
        final String key = args[0];
        List<TipoServicoScheduling> servicos;
        if (args.length > 1) {
            servicos = new ArrayList<TipoServicoScheduling>();
            for (int i = 1; i < args.length; i++) {
                try {
                    servicos.add(TipoServicoScheduling.valueOf(args[i]));
                } catch (Exception e) {
                    System.err.println(e.getMessage());
                }
            }
        } else {
            servicos = Arrays.asList(TipoServicoScheduling.values());
        }
        executarServicos(key, servicos);
    }

    public static void executarServicos(String key, List<TipoServicoScheduling> servicos) {
        ClassPathXmlApplicationContext ctx = new ClassPathXmlApplicationContext("applicationContext.xml");
        System.out.println(new Date(ctx.getStartupDate()));

        for (TipoServicoScheduling servico : servicos) {
            switch (servico) {
                case AGENDA:
                    TaskNotificationAgendamento taskNotificationAgendamento = new TaskNotificationAgendamento(key);
                    taskNotificationAgendamento.execute(null);
                    break;
                case RENOVACAO_TREINO:
                    //Verificar quais Programas de Treino estão próximos a Renovar
                    Task taskAgendarRenovacaoTreino = new TaskAgendarRenovacaoTreino(key);
                    taskAgendarRenovacaoTreino.execute(null);
                    break;
                case BI_PROFESSORES:
                    Task taskProcessarBIProfessores = new TaskProcessarBIProfessores(key);
                    taskProcessarBIProfessores.execute(null);
                    break;
                case CHECKOUT:
                    //verificar aulas de personal para fazer checkout automatico
                    Task taskCheckoutAutomatico = new TaskCheckOutAutomatico(key);
                    taskCheckoutAutomatico.execute(null);
                    break;
                case COMPROMISSO:
                    //Verificar quais alunos estão atrasados com seu compromisso ou não começaram a malhar            
                    Task taskLembrarCompromisso = new TaskCompromissoTreino(key);
                    taskLembrarCompromisso.execute(null);
                    break;
                case EXPIRAR_CREDITOS:
                    //expirar creditos de personal
                    Task taskExpirarCreditos = new TaskExpirarCreditos(key);
                    taskExpirarCreditos.execute(null);
                    break;
                case BOOKING_GYMPASS:
                    //Sincronizar aulas na gympass
                    Task taskBookingGympass = new TaskBookingGympass(key);
                    taskBookingGympass.execute(null);
                    break;
            }
        }
    }
}
