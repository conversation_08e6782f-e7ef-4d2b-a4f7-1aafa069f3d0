package br.com.pacto.controller.json.crossfit;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.jpa.service.intf.PovoadorAtividadeService;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.aparelho.Aparelho;
import br.com.pacto.bean.aparelho.AparelhoWod;
import br.com.pacto.bean.atividade.Atividade;
import br.com.pacto.bean.atividade.AtividadeWod;
import br.com.pacto.bean.benchmark.Benchmark;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.crossfit.EquipeEvento;
import br.com.pacto.bean.crossfit.EventoCrossfit;
import br.com.pacto.bean.crossfit.ParticipanteEquipeEvento;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.nivelwod.NivelWod;
import br.com.pacto.bean.tipowod.TipoWod;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.bean.wod.ComentarioWod;
import br.com.pacto.bean.wod.NivelCrossfitEnum;
import br.com.pacto.bean.wod.ScoreTreino;
import br.com.pacto.bean.wod.Wod;
import br.com.pacto.controller.json.atividade.AtividadeJSONControle;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.enumerador.crossfit.OrigemExercicio;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.empresa.EmpresaServiceImpl;
import br.com.pacto.service.intf.aparelho.AparelhoService;
import br.com.pacto.service.impl.wod.WodServiceImpl;
import br.com.pacto.service.intf.atividade.AtividadeService;
import br.com.pacto.service.intf.aula.AulaService;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.crossfit.EventoCrossfitService;
import br.com.pacto.service.intf.nivel.NivelService;
import br.com.pacto.service.intf.nivelwod.NivelWodService;
import br.com.pacto.service.intf.scoretreino.ComentarioWodLikeService;
import br.com.pacto.service.intf.scoretreino.ComentarioWodService;
import br.com.pacto.service.intf.tipowod.TipoWodService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.service.intf.wod.WodService;
import br.com.pacto.util.UteisValidacao;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.commons.codec.binary.Base64;
import org.json.JSONObject;
import org.jsoup.Connection;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import servicos.integracao.midias.MidiaService;
import servicos.integracao.midias.commons.MidiaEntidadeEnum;
import servicos.integracao.zw.json.WodJSON;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;


/**
 * Created by Joao Alcides on 14/02/2017.
 */
@Api(tags = "CrossFit")
@Controller
@RequestMapping("/crossfit")
public class CrossfitJSONControle extends SuperControle {

    private static final SimpleDateFormat FORMATADOR_DATA = new SimpleDateFormat("dd/MM/yyyy");

    @Autowired
    private WodService ws;
    @Autowired
    private EventoCrossfitService es;
    @Autowired
    private ClienteSinteticoService cs;
    @Autowired
    private ComentarioWodService comentarioWodService;
    @Autowired
    private ComentarioWodLikeService comentarioWodLikeService;
    @Autowired
    private AtividadeService atividadeService;
    @Autowired
    private AparelhoService aparelhoService;
    @Autowired
    private TipoWodService tipoWodService;
    @Autowired
    private WodService wodService;
    @Autowired
    private UsuarioService usuarioService;
    @Autowired
    private AulaService aulaService;
    @Autowired
    private EmpresaServiceImpl empresaService;
    @Autowired
    private ConfiguracaoSistemaService configService;
    @Autowired
    private PovoadorAtividadeService povoadorAtividadeService;
    @Autowired
    private NivelWodService nivelWodService;

    @ApiOperation(
            value = "Consultar WODs do dia",
            notes = "Consulta todos os WODs (Workout of the Day) programados para o dia atual. " +
                    "Retorna a lista de treinos de CrossFit disponíveis para execução no dia.",
            tags = "CrossFit"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)")
    })
    @RequestMapping(value = "{ctx}/wodsdia", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap wodsdia(
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            List<Wod> wods = ws.wodsDia(ctx, Calendario.hoje());
            mm.addAttribute(RETURN, wods);
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(CrossfitJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(
            value = "Consultar benchmarks disponíveis",
            notes = "Lista todos os benchmarks de CrossFit disponíveis no sistema. " +
                    "Benchmarks são WODs padronizados usados para medir performance e progresso dos atletas.",
            tags = "CrossFit"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)")
    })
    @RequestMapping(value = "{ctx}/benchmarks", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap benchmarks(
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            List<Benchmark> benchmarks = ws.benchmarks(ctx);
            mm.addAttribute(RETURN, benchmarks);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(CrossfitJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(
            value = "Consultar WOD sintético",
            notes = "Consulta informações sintéticas de um WOD específico. " +
                    "Retorna dados resumidos do treino de CrossFit solicitado.",
            tags = "CrossFit"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)")
    })
    @RequestMapping(value = "{ctx}/consultaWodSintetico", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap consultaWodSintetico(@PathVariable String ctx,
                                  @ApiParam(value = "Código do WOD a ser consultado", defaultValue = "1", required = true)
                                  @RequestParam final Integer wod,
                                  @ApiParam(value = "Matrícula do aluno para verificação de permissões", defaultValue = "12345")
                                  @RequestParam(required = false) final String matricula,
                                  @ApiParam(value = "Código do usuário para verificação de permissões", defaultValue = "1")
                                  @RequestParam(required = false) final Integer usuario) {
        return wodsGeral(ctx, null , null, matricula, wod, usuario, null);
    }

    @ApiOperation(
            value = "Consultar WODs por período",
            notes = "Consulta WODs (Workout of the Day) em um período específico. " +
                    "Permite filtrar por empresa, usuário e matrícula para personalizar os resultados.",
            tags = "CrossFit"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)")
    })
    @RequestMapping(value = "{ctx}/wods", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap wods(@PathVariable String ctx,
                  @ApiParam(value = "Data de início do período no formato dd/MM/yyyy", defaultValue = "01/01/2024", required = true)
                  @RequestParam final String inicio,
                  @ApiParam(value = "Data de fim do período no formato dd/MM/yyyy", defaultValue = "31/01/2024", required = true)
                  @RequestParam final String fim,
                  @ApiParam(value = "Matrícula do aluno para verificação de permissões", defaultValue = "12345")
                  @RequestParam(required = false) final String matricula,
                  @ApiParam(value = "Código da empresa para filtrar WODs específicos da unidade", defaultValue = "1")
                  @RequestParam(required = false) final Integer empresaCodigo,
                  @ApiParam(value = "Código do usuário para verificação de permissões", defaultValue = "1")
                  @RequestParam(required = false) final Integer usuario) {
        //return wodsGeral(ctx, inicio, fim, matricula, null, usuario);
        return wodsGeral(ctx, inicio, fim, matricula, null, usuario, empresaCodigo);
    }

    @ApiOperation(
            value = "Consultar WODs por período (versão 2)",
            notes = "Versão aprimorada para consulta de WODs (Workout of the Day) em um período específico. " +
                    "Inclui melhorias na performance e filtros mais precisos por empresa.",
            tags = "CrossFit"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)")
    })
    @RequestMapping(value = "{ctx}/v2/wods", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap wodsV2(@PathVariable String ctx,
                    @ApiParam(value = "Data de início do período no formato dd/MM/yyyy", defaultValue = "01/01/2024", required = true)
                    @RequestParam final String inicio,
                    @ApiParam(value = "Data de fim do período no formato dd/MM/yyyy", defaultValue = "31/01/2024", required = true)
                    @RequestParam final String fim,
                    @ApiParam(value = "Código da empresa no sistema ZW para filtrar WODs", defaultValue = "1", required = true)
                    @RequestParam final Integer empresaCodigoZw,
                    @ApiParam(value = "Matrícula do aluno para verificação de permissões", defaultValue = "12345")
                    @RequestParam(required = false) final String matricula,
                    @ApiParam(value = "Código do usuário para verificação de permissões", defaultValue = "1")
                    @RequestParam(required = false) final Integer usuario) {
        ModelMap mm = new ModelMap();
        try {
            List<Wod> wods = wodService.consultaWodsApp(ctx, empresaCodigoZw, inicio, fim, usuario, matricula, null);
            mm.addAttribute(RETURN, wods);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(CrossfitJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ResponseBody
    @ApiOperation(
            value = "Consultar datas futuras com WODs",
            notes = "Consulta as datas do próximo mês que possuem WODs (Workout of the Day) programados. " +
                    "Retorna uma lista de datas formatadas (dd/MM/yyyy) que possuem treinos de CrossFit disponíveis.",
            tags = "CrossFit"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)")
    })
    @RequestMapping(value = "{contexto}/wodsFuturos", method = RequestMethod.GET)
    public ModelMap wods(@PathVariable String contexto,
                         @ApiParam(value = "Código da empresa para filtrar WODs específicos da unidade", defaultValue = "1")
                         @RequestParam(value = "empresa", required = false) Integer empresa,
                         @ApiParam(value = "Data inicial em timestamp para consulta do período", defaultValue = "1640995200000", required = true)
                         @RequestParam("dataInicial") Long dataInicial) {
        final ModelMap modelMap = new ModelMap();

        final Calendar proximoMes = Calendar.getInstance();
        proximoMes.setTimeInMillis(dataInicial);
        proximoMes.add(Calendar.MONTH, 1);

        Set<Date> datasWod;
        try {
            datasWod = ws.obterDiasComWodPeriodo(contexto, empresa, dataInicial, proximoMes.getTimeInMillis());
        } catch (Exception ex) {
            modelMap.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(CrossfitJSONControle.class.getName()).log(Level.SEVERE, null, ex);
            return modelMap;
        }

        List<String> datasConvertidas = new ArrayList<String>(datasWod.size());
        for (Date dataWod : datasWod) {
            datasConvertidas.add(FORMATADOR_DATA.format(dataWod));
        }

        return modelMap.addAttribute(RETURN, datasConvertidas);
    }

    private ModelMap wodsGeral(String ctx, String inicio, String fim, String matricula, Integer wodCodigo, Integer usuario, Integer empresaCodigo) {
        ModelMap mm = new ModelMap();
        Empresa empresa = new Empresa();

        try {
            if (!UteisValidacao.emptyNumber(empresaCodigo))
                empresa = empresaService.obterPorId(ctx, empresaCodigo);
        } catch (ServiceException e) {
            //Uteis.logar(e, CrossfitJSONControle.class);
        }

        try {
            Date dataInicio = null;
            Date dataFim = null;
            if (inicio != null && fim != null) {
                dataInicio = Uteis.dataHoraZeradaUTC(inicio, empresa.getTimeZoneDefault());
                dataFim = Uteis.dataHoraZeradaUTC(fim, empresa.getTimeZoneDefault());
            }

            ConfiguracaoSistema confPermissaoWod = configService.consultarPorTipo(ctx, ConfiguracoesEnum.PERMITIR_VISUALIZAR_WOD);
            boolean permissaoVisualizarWod = Boolean.parseBoolean(confPermissaoWod.getValor());
            boolean podeUsarCross = ws.podeUsarCross(ctx, usuario, matricula, permissaoVisualizarWod);

            List<Wod> wods = ws.wods(ctx, dataInicio, dataFim, null, wodCodigo, null);
            for (Wod wod : wods) {
                wod.setContratoCrossfit(podeUsarCross);
                wod.setTemResultadoGeral(ws.existeResultado(ctx, wod.getCodigo()));
                for (AtividadeWod atividadeWod : wod.getAtividades()) {
                    wod.getAtividadeWodJSON().add(new AtividadeWodJSON(atividadeWod));
                }
                for (AparelhoWod aparelhoWod : wod.getAparelhos()) {
                    wod.getAparelhoWodJSON().add(new AparelhoWodJSON(aparelhoWod));
                }
            }
            mm.addAttribute(RETURN, wods);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(CrossfitJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(
            value = "Consultar tipos de WODs",
            notes = "Lista todos os tipos de WODs (Workout of the Day) disponíveis no sistema. " +
                    "Retorna um mapa com os nomes dos tipos e seus respectivos campos de resultado.",
            tags = "CrossFit"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)")
    })
    @RequestMapping(value = "{ctx}/tiposwods", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap tiposWods(@PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            Map<String, String[]> mapa = new HashMap<String, String[]>();

            List<TipoWod> listaTipoWod = tipoWodService.obterTodas(ctx);

            for (TipoWod tipoWod : listaTipoWod) {
                mapa.put(tipoWod.getNome(), tipoWod.getCamposResultado().split(","));
            }

            TipoWod tipoWod = tipoWodService.obterPorNome(ctx, "Amrap + Weight");
            if (tipoWod != null) {
                tipoWod.setOrderBy("ORDER BY rounds  DESC, peso DESC");
                tipoWodService.gravarTipoWod(ctx, tipoWod);

                List<Wod> wods = wodService.obterPorTipoWod(ctx, tipoWod.getCodigo());
                for (Wod wod : wods) {
                    if (wod.getTipoWod().getNome().equals("Amrap + Weight")) {
                        wodService.atualizarRanking(ctx, wod.getCodigo(), wod.getTipoWod().getOrderBy());

                    }
                }
            }

            mm.addAttribute(RETURN, mapa);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(CrossfitJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(
            value = "Consultar resultados de treinos",
            notes = "Consulta os resultados de treinos de CrossFit de um usuário em um período específico. " +
                    "Retorna o histórico de scores e performances do atleta nos WODs realizados.",
            tags = "CrossFit"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)")
    })
    @RequestMapping(value = "{ctx}/resultados", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap resultados(@PathVariable String ctx,
                        @ApiParam(value = "Data de início do período no formato dd/MM/yyyy", defaultValue = "01/01/2024", required = true)
                        @RequestParam final String inicio,
                        @ApiParam(value = "Data de fim do período no formato dd/MM/yyyy", defaultValue = "31/01/2024", required = true)
                        @RequestParam final String fim,
                        @ApiParam(value = "Matrícula do aluno para consulta dos resultados", defaultValue = "12345")
                        @RequestParam(required = false) final String matricula,
                        @ApiParam(value = "Código do usuário para consulta dos resultados", defaultValue = "1")
                        @RequestParam(required = false) final Integer usuario) {
        ModelMap mm = new ModelMap();
        try {
            Integer codUsuario = obterCodigoUsuario(ctx, usuario, matricula);
            List<ScoreTreino> scores = ws.resultados(ctx, Uteis.getDate(inicio, "dd/MM/yyyy"), Uteis.getDate(fim, "dd/MM/yyyy"), codUsuario, null);
            mm.addAttribute(RETURN, scores);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(CrossfitJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    private void validaUsuarioExiste(String ctx, Integer codUsuario) throws ServiceException {
        try {
            Usuario usuario = usuarioService.obterPorId(ctx, codUsuario, true);
            if (usuario == null) {
                throw new ServiceException("Usuário não encontrado.");
            }
        } catch (ServiceException e) {
            throw new ServiceException("Usuário não encontrado.");
        }
    }

    @ApiOperation(
            value = "Consultar ranking de WOD",
            notes = "Consulta o ranking de participantes de um WOD específico. " +
                    "Retorna a classificação ordenada dos atletas com suas respectivas performances.",
            tags = "CrossFit"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)")
    })
    @RequestMapping(value = "{ctx}/ranking", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap ranking(@PathVariable String ctx,
                     @ApiParam(value = "Código do WOD para consulta do ranking", defaultValue = "1", required = true)
                     @RequestParam final Integer wod,
                     HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            List<RankingJSON> scores = ws.ranking(ctx, wod, request);
            mm.addAttribute(RETURN, scores);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(CrossfitJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(
            value = "Verificar permissão para CrossFit",
            notes = "Verifica se um aluno possui permissão para utilizar os recursos de CrossFit. " +
                    "Retorna true se o aluno pode acessar WODs e funcionalidades relacionadas.",
            tags = "CrossFit"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)")
    })
    @RequestMapping(value = "{ctx}/podeusarcrossfit", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap podeUsarCrossfit(@PathVariable String ctx,
                              @ApiParam(value = "Matrícula do aluno para verificação de permissões", defaultValue = "12345", required = true)
                              @RequestParam final String matricula) {
        ModelMap mm = new ModelMap();
        try {
            ClienteSintetico clienteSintetico = cs.consultarPorMatricula(ctx, matricula);
            if (clienteSintetico == null) {
                throw new Exception("Não foi possível encontrar o aluno, talvez você ainda não adicionou ele ao TreinoWeb.");
            }
            if (independente(ctx) || clienteSintetico.getCrossfit()) {
                mm.addAttribute(RETURN, true);
            } else {
                mm.addAttribute(RETURN, aulaService.verificarAulasCrossfitAluno(ctx, matricula));
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(CrossfitJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }


    @ApiOperation(
            value = "Gravar resultado de treino",
            notes = "Registra o resultado de um treino de CrossFit para um usuário. " +
                    "Permite salvar tempo, peso, repetições, rounds e outros dados de performance.",
            tags = "CrossFit"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)")
    })
    @RequestMapping(value = "{ctx}/gravarresultado", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap gravarResultado(@PathVariable String ctx,
                             @ApiParam(value = "Código do WOD para registro do resultado", defaultValue = "1")
                             @RequestParam(required = false) final Integer wod,
                             @ApiParam(value = "Matrícula do aluno que realizou o treino", defaultValue = "12345")
                             @RequestParam(required = false) final String matricula,
                             @ApiParam(value = "Código do benchmark utilizado", defaultValue = "1")
                             @RequestParam(required = false) final Integer benchmark,
                             @ApiParam(value = "Tempo realizado no formato MM:SS ou apenas minutos", defaultValue = "15:30")
                             @RequestParam(required = false) final String tempo,
                             @ApiParam(value = "Peso utilizado no treino em quilogramas", defaultValue = "70.5")
                             @RequestParam(required = false) final Double peso,
                             @ApiParam(value = "Número de repetições realizadas", defaultValue = "100")
                             @RequestParam(required = false) final Integer repeticoes,
                             @ApiParam(value = "Número de rounds completados", defaultValue = "5")
                             @RequestParam(required = false) final Integer rounds,
                             @ApiParam(value = "Indica se foi realizado como RX (prescribed)", defaultValue = "true")
                             @RequestParam(required = false) final Boolean rx,
                             @ApiParam(value = "Comentário adicional sobre o treino", defaultValue = "Treino intenso!")
                             @RequestParam(required = false) final String comentario,
                             @ApiParam(value = "Código do usuário que realizou o treino", defaultValue = "1")
                             @RequestParam(required = false) final Integer usuario) {
        ModelMap mm = new ModelMap();
        try {
            Integer tempoEmMinutos;
            if (tempo.contains(":")) {
                String[] parts = tempo.split(":");
                tempoEmMinutos = Integer.parseInt(parts[0]);
            } else {
                tempoEmMinutos = Integer.parseInt(tempo);
            }

            NivelWod nivelWod = nivelWodService.obterPorNome (ctx, "Iniciante");
            if (rx) {
                nivelWod = nivelWodService.obterPorNome (ctx, "RX");
            }

            Integer codUsuario = obterCodigoUsuario(ctx, usuario, matricula);

            ScoreTreino score = ws.gravarScore(ctx, wod, codUsuario, benchmark, tempoEmMinutos,
                    peso, repeticoes, rounds, rx, comentario, nivelWod, null);
            mm.addAttribute(RETURN, score);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(CrossfitJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(
            value = "Consultar detalhes de WOD",
            notes = "Consulta informações detalhadas de um WOD específico, incluindo comentários e dados completos. " +
                    "Permite limitar a quantidade de resultados retornados.",
            tags = "CrossFit"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)")
    })
    @RequestMapping(value = "{ctx}/consultaWod", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap consultaWod(@PathVariable String ctx,
                         @ApiParam(value = "Código do WOD para consulta detalhada", defaultValue = "1", required = true)
                         @RequestParam final Integer wod,
                         @ApiParam(value = "Matrícula do aluno para personalização dos dados", defaultValue = "12345")
                         @RequestParam(required = false) final String matricula,
                         @ApiParam(value = "Limite de comentários a serem retornados", defaultValue = "10", required = true)
                         @RequestParam final Integer limit,
                         @ApiParam(value = "Número máximo de resultados", defaultValue = "50", required = true)
                         @RequestParam final Integer maxResults,
                         @ApiParam(value = "Código do usuário para personalização dos dados", defaultValue = "1")
                         @RequestParam(required = false) final Integer usuario,
                         HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            Wod wodresult = obterWod(ctx, wod);

            Integer codUsuario = obterCodigoUsuario(ctx, usuario, matricula);

            ws.preencherListaComentarios(ctx, wodresult, codUsuario, limit, maxResults, request);
            mm.addAttribute(RETURN, new WodJSON(wodresult));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(CrossfitJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(
            value = "Registrar like em comentário",
            notes = "Adiciona ou remove um like de um comentário de WOD. " +
                    "Permite interação social entre os usuários nos treinos de CrossFit.",
            tags = "CrossFit"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)")
    })
    @RequestMapping(value = "{ctx}/registrarLike", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap registrarLike(@PathVariable String ctx,
                           @ApiParam(value = "Matrícula do usuário que está dando o like", defaultValue = "12345")
                           @RequestParam(required = false) final String matricula,
                           @ApiParam(value = "Código do comentário que receberá o like", defaultValue = "1", required = true)
                           @RequestParam final Integer codComentarioWod,
                           @ApiParam(value = "True para adicionar like, false para remover", defaultValue = "true", required = true)
                           @RequestParam final boolean adicionar,
                           @ApiParam(value = "Código do usuário que está dando o like", defaultValue = "1")
                           @RequestParam(required = false) final Integer usuario) {
        ModelMap mm = new ModelMap();
        try {
            ComentarioWod comentarioWodObj = comentarioWodService.obterPorId(ctx, codComentarioWod);
            if (comentarioWodObj == null) {
                throw new Exception("Não foi possível encontrar o Comentario Wod.");
            }

            Integer codUsuario = obterCodigoUsuario(ctx, usuario, matricula);

            Usuario usuarioObj = usuarioService.obterPorId(ctx, codUsuario);
            if (usuarioObj == null) {
                throw new Exception("Usuário não encontrado.");
            }

            if (adicionar) {
                comentarioWodLikeService.adicionarComentarioWodLike(ctx, comentarioWodObj, usuarioObj);
                mm.addAttribute(RETURN, "Like adicionado!");
            } else {
                comentarioWodLikeService.excluirComentarioWodLike(ctx, comentarioWodObj, usuarioObj);
                mm.addAttribute(RETURN, "Like removido!");
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(CrossfitJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(
            value = "Comentar em WOD",
            notes = "Adiciona um comentário a um WOD específico. " +
                    "Permite que os usuários compartilhem experiências e dicas sobre os treinos.",
            tags = "CrossFit"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)")
    })
    @RequestMapping(value = "{ctx}/comentarWod", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap comentarWod(@PathVariable String ctx,
                         @ApiParam(value = "Código do WOD que receberá o comentário", defaultValue = "1", required = true)
                         @RequestParam final Integer codWod,
                         @ApiParam(value = "Matrícula do usuário que está comentando", defaultValue = "12345")
                         @RequestParam(required = false) final String matricula,
                         @ApiParam(value = "Texto do comentário", defaultValue = "Ótimo treino!", required = true)
                         @RequestParam final String comentario,
                         @ApiParam(value = "Código do usuário que está comentando", defaultValue = "1")
                         @RequestParam(required = false) final Integer usuario) {
        ModelMap mm = new ModelMap();
        try {
            Integer codUsuario = obterCodigoUsuario(ctx, usuario, matricula);
            String retorno = comentarWodGeral(ctx, codWod, codUsuario, comentario, null);
            if (retorno.startsWith("ERRO")) {
                throw new Exception(retorno);
            }
            mm.addAttribute(RETURN, retorno);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(CrossfitJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    private String comentarWodGeral(String ctx, Integer codWod, Integer usuario, String comentario, NivelCrossfitEnum nivelCrossfit) {
        try {
            Wod wodObj = obterWod(ctx, codWod);

            Usuario usuarioObj = usuarioService.obterPorId(ctx, usuario);
            if (usuarioObj == null) {
                throw new Exception("Usuário não encontrado.");
            }
            ComentarioWod comentarioWod = new ComentarioWod();
            comentarioWod.setUsuario(usuarioObj);
            comentarioWod.setWod(wodObj);
            comentarioWod.setNivelCrossfit(nivelCrossfit);
            comentarioWod.setComentario(comentario);
            comentarioWod.setDataRegistro(Calendario.hoje());
            comentarioWodService.inserir(ctx, comentarioWod);
            return "Comentário adicionado.";
        } catch (Exception ex) {
            Logger.getLogger(CrossfitJSONControle.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO:" + ex.getMessage();
        }
    }

    @ApiOperation(
            value = "Excluir comentário de WOD",
            notes = "Remove um comentário específico de um WOD. " +
                    "Exclui o comentário e todos os relacionamentos associados (likes, etc.).",
            tags = "CrossFit"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)")
    })
    @RequestMapping(value = "{ctx}/excluirComentario", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap excluirComentario(@PathVariable String ctx,
                               @ApiParam(value = "Código do comentário a ser excluído", defaultValue = "1", required = true)
                               @RequestParam final Integer codComentarioWod) {
        ModelMap mm = new ModelMap();
        try {
            ComentarioWod comentarioWod = comentarioWodService.obterPorId(ctx, codComentarioWod);
            if (comentarioWod == null) {
                throw new Exception("Não foi possível encontrar comentário.");
            }
            comentarioWodService.excluirComRelacionamentos(ctx, comentarioWod);
            mm.addAttribute(RETURN, "Comentário excluído.");
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(CrossfitJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(
            value = "Gravar resultado via aplicativo",
            notes = "Versão específica para aplicativo móvel para registrar resultado de treino de CrossFit. " +
                    "Inclui funcionalidades adicionais como notificações de ranking e validações específicas.",
            tags = "CrossFit"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)")
    })
    @RequestMapping(value = "{ctx}/gravarResultadoAPP", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap gravarResultadoAPP(@PathVariable String ctx,
                                @ApiParam(value = "Código do WOD para registro do resultado", defaultValue = "1")
                                @RequestParam(required = false) final Integer wod,
                                @ApiParam(value = "Matrícula do aluno que realizou o treino", defaultValue = "12345")
                                @RequestParam(required = false) final String matricula,
                                @ApiParam(value = "Código do benchmark utilizado", defaultValue = "1")
                                @RequestParam(required = false) final Integer benchmark,
                                @ApiParam(value = "Tempo realizado em minutos", defaultValue = "15")
                                @RequestParam(required = false) final Integer tempo,
                                @ApiParam(value = "Peso utilizado no treino em quilogramas", defaultValue = "70.5")
                                @RequestParam(required = false) final Double peso,
                                @ApiParam(value = "Número de repetições realizadas", defaultValue = "100")
                                @RequestParam(required = false) final Integer repeticoes,
                                @ApiParam(value = "Número de rounds completados", defaultValue = "5")
                                @RequestParam(required = false) final Integer rounds,
                                @ApiParam(value = "Código do nível de CrossFit utilizado", defaultValue = "1")
                                @RequestParam(required = false) final Integer nivel,
                                @ApiParam(value = "Comentário adicional sobre o treino", defaultValue = "Treino intenso!")
                                @RequestParam(required = false) final String comentario,
                                @ApiParam(value = "Código do usuário que realizou o treino", defaultValue = "1")
                                @RequestParam(required = false) final Integer usuario) {
        ModelMap mm = new ModelMap();
        try {
            Wod wodresult = obterWod(ctx, wod);

            Integer codUsuario = obterCodigoUsuario(ctx, usuario, matricula);
            validaUsuarioExiste(ctx, codUsuario);

            NivelWod nivelWod = nivelWodService.obterPorCodigo(ctx, nivel);
            if(nivelWod==null){
                throw new RuntimeException("Código do nível cross informado não existe!");
            }
            List<ScoreTreino> scoreTreinoAntesAtualizacao = ws.obterPrimeirasTresPosicoesWodRanking(ctx, wodresult.getCodigo());
            ScoreTreino score = ws.gravarScore(ctx, wodresult.getCodigo(), codUsuario, benchmark, tempo,
                    peso, repeticoes, rounds, false, comentario, nivelWod, null);
            if (!UteisValidacao.emptyString(comentario)) {
                comentarWodGeral(ctx, wodresult.getCodigo(), codUsuario, comentario, NivelCrossfitEnum.valueOf(nivel));
            }
            List<ScoreTreino> scoreTreinoDepoisAtualizacao = ws.obterPrimeirasTresPosicoesWodRanking(ctx, wodresult.getCodigo());
            try {
                ws.enviaNotificacaoTopTres(ctx, scoreTreinoAntesAtualizacao, scoreTreinoDepoisAtualizacao);
            } catch (Exception ex) {
                Logger.getLogger(CrossfitJSONControle.class.getName()).log(Level.SEVERE, "Erro ao enviar notificação top 3 wod!", ex);
            }

            mm.addAttribute(RETURN, ws.scoreTreinoPorCodigo(ctx,score.getCodigo()));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(CrossfitJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(
            value = "Obter atividades de CrossFit",
            notes = "Lista todas as atividades disponíveis para uso em WODs de CrossFit. " +
                    "Retorna exercícios e movimentos que podem ser incluídos nos treinos.",
            tags = "CrossFit"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)")
    })
    @RequestMapping(value = "{ctx}/obterAtividadesCrossFit", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap obterAtividadesCrossFit(@PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            List<Atividade> atividades = atividadeService.obterTodos(ctx, true, false, true);
            List<AtividadeWodJSON> retorno = new ArrayList<AtividadeWodJSON>();
            for (Atividade atividade : atividades) {
                retorno.add(new AtividadeWodJSON(atividade));
            }
            mm.addAttribute(RETURN, retorno);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(CrossfitJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(
            value = "Obter aparelhos de CrossFit",
            notes = "Lista todos os aparelhos e equipamentos disponíveis para uso em WODs de CrossFit. " +
                    "Retorna equipamentos que podem ser incluídos nos treinos.",
            tags = "CrossFit"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)")
    })
    @RequestMapping(value = "{ctx}/obterAparelhosCrossFit", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap obterAparelhosCrossFit(@PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            List<Aparelho> aparelhos = aparelhoService.obterTodos(ctx, true);
            List<AparelhoWodJSON> retorno = new ArrayList<AparelhoWodJSON>();
            for (Aparelho aparelho : aparelhos) {
                retorno.add(new AparelhoWodJSON(aparelho));
            }
            mm.addAttribute(RETURN, retorno);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(CrossfitJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(
            value = "Cadastrar WOD",
            notes = "Cadastra um novo WOD (Workout of the Day) no sistema. " +
                    "Recebe dados em formato JSON com todas as informações do treino.",
            tags = "CrossFit"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)")
    })
    @RequestMapping(value = "{ctx}/cadastrarWod", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap cadastrarWod(@PathVariable String ctx,
                          @ApiParam(value = "Dados do WOD em formato JSON", required = true)
                          @RequestParam String dadosWodJSON) {
        ModelMap mm = new ModelMap();
        try {
            mm = cadastroEdicaoExclusaoWod(ctx, dadosWodJSON);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(CrossfitJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @Transactional
    public ModelMap cadastroEdicaoExclusaoWod(String ctx, String dadosWodJSON) throws Exception {

        ModelMap mm = new ModelMap();

        try {

            JSONObject jsonObject = new JSONObject(dadosWodJSON);

            String operacao = jsonObject.getString("operacao");

            if (operacao.toUpperCase().equals("EXCLUIR")) {

                Wod wod = obterWod(ctx, jsonObject.getInt("codigo"));
                ws.excluir(ctx, wod);
                return mm.addAttribute(RETURN, "Wod excluído com sucesso.");

            } else if (operacao.toUpperCase().equals("ADICIONAR")) {

                Wod wod = new Wod();
                List<AtividadeWod> listAtividadeWod = new ArrayList<>();
                List<AparelhoWod> aparelhoWodList = new ArrayList<>();
                preencherWodDadosJSON(ctx, wod, jsonObject, listAtividadeWod, aparelhoWodList);
                ws.inserir(ctx, wod);
                return mm.addAttribute(RETURN, wod);

            } else if (operacao.toUpperCase().equals("EDITAR")) {

                Wod wod = obterWod(ctx, jsonObject.getInt("codigo"));
                List<AtividadeWod> listAtividadeWod = ws.obterListaAtividadesWod(ctx, jsonObject.getInt("codigo"));
                List<AparelhoWod> listAparelhoWod = ws.obterListaAparelhosWod(ctx, jsonObject.getInt("codigo"));
                preencherWodDadosJSON(ctx, wod, jsonObject, listAtividadeWod, listAparelhoWod);
                ws.alterar(ctx, wod);
                return mm.addAttribute(RETURN, wod);

            }
            return mm.addAttribute(RETURN, "Nenhuma operação (EXCLUIR | ADICIONAR | EDITAR) realizada");
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(CrossfitJSONControle.class.getName()).log(Level.SEVERE, null, ex);

        }
        return mm;
    }

    private void preencherWodDadosJSON(String ctx, Wod wod, JSONObject jsonObject, List<AtividadeWod> listAtividadeWod, List<AparelhoWod> listAparelhoWod) throws Exception {
        try {
            wod.setEmpresa(jsonObject.getInt("empresa"));
        }catch (Exception ignore){} // solicitação do APP.

        wod.setNome(jsonObject.getString("nome")); //Nome
        wod.setDia(Uteis.getDate(jsonObject.getString("dia"), "dd/MM/yyyy")); //DIA
        wod.setOrigemExercicio(OrigemExercicio.BOX); //Origem Wod
        wod.setTipoWodTabela(tipoWodService.obterPorCodigo(ctx, jsonObject.getInt("tipoWod"))); //Tipo Wod
        wod.setAlongamento(jsonObject.getString("alongamento")); //Alongamento Ou Mobilidade
        wod.setAquecimento(jsonObject.getString("aquecimento")); //Aquecimento
        wod.setParteTecnicaSkill(jsonObject.getString("parteTecnicaSkill")); //Parte Técnica Ou Skill
        wod.setComplexEmom(jsonObject.getString("complexEmom")); //Complex Ou EMOM
        wod.setDescricaoExercicios(jsonObject.getString("wod")); //Wod

        String atividades = jsonObject.getString("atividades");
        String aparelhos = jsonObject.getString("aparelhos");

        if (!UteisValidacao.emptyString(atividades)) {
            for (String codAti : atividades.split(",")) {
                boolean adicionar = true;
                Integer codigo = Integer.parseInt(codAti);
                for (AtividadeWod atividadeWod : listAtividadeWod) {
                    if (codigo.equals(atividadeWod.getAtividade().getCodigo())) {
                        adicionar = false;
                        break;
                    }
                }
                if (adicionar) {
                    Atividade atividadeAdd = atividadeService.obterPorId(ctx, codigo);
                    wod.getAtividades().add(new AtividadeWod(wod, atividadeAdd));
                }
                /*
                atividadesReceb.add(atividade);
                sbAtividadesReceb.append(";").append(atividade.getCodigo().toString()).append(";");*/
            }
        }

        if (!UteisValidacao.emptyString(aparelhos)) {
            for (String codApa : aparelhos.split(",")) {
                boolean adicionar = true;
                Integer codigo = Integer.parseInt(codApa);
                for (AparelhoWod aparelhoWod : listAparelhoWod) {
                    if (codigo.equals(aparelhoWod.getAparelho().getCodigo())) {
                        adicionar = false;
                        break;
                    }
                }
                if (adicionar) {
                    Aparelho aparelhoAdd = aparelhoService.obterPorId(ctx, codigo);
                    wod.getAparelhos().add(new AparelhoWod(wod, aparelhoAdd));
                }
            }
        }

        //IMAGEM
        String imagemBase64 = jsonObject.getString("imagemBase64");
        if (!UteisValidacao.emptyString(imagemBase64)) {
            String identificador = Calendario.getData(Calendario.hoje(), "ddMMyyyyhhMMss") + "-Wod-";
            String chaveImagem = MidiaService.getInstanceWood().uploadObjectFromByteArray(ctx,
                    MidiaEntidadeEnum.FOTO_ANEXO_JPG,
                    identificador,
                    Base64.decodeBase64(imagemBase64));
            wod.setUrlImagem(Aplicacao.obterUrlFotoDaNuvem(chaveImagem));
        }
    }

    @ApiOperation(
            value = "Consultar tipos de WOD",
            notes = "Lista todos os tipos de WOD disponíveis no sistema. " +
                    "Retorna informações sobre os diferentes formatos de treino de CrossFit.",
            tags = "CrossFit"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)")
    })
    @RequestMapping(value = "{ctx}/tipoWod", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap tipoWod(@PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            List<TipoWod> listaTipoWod = tipoWodService.obterTodas(ctx);
            mm.addAttribute(RETURN, listaTipoWod);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(CrossfitJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(
            value = "Obter WOD do site oficial",
            notes = "Busca o WOD oficial do dia no site do CrossFit. " +
                    "Retorna o treino oficial publicado pela CrossFit Inc. para a data especificada.",
            tags = "CrossFit"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)")
    })
    @RequestMapping(value = "{ctx}/obterWodSite", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap obterWodSite(@PathVariable String ctx,
                              @ApiParam(value = "Data para busca do WOD no formato dd/MM/yyyy", defaultValue = "01/01/2024", required = true)
                              @RequestParam String data) {
        ModelMap mm = new ModelMap();
        try {

            Date dataWod = Calendario.hoje();
            if (!UteisValidacao.emptyString(data)) {
                dataWod = Uteis.getDate(data, "dd/MM/yyyy");
            }

            StringBuilder url = new StringBuilder("https://www.crossfit.com/workout/");
            url.append(Uteis.getDataAplicandoFormatacao(dataWod, "yyyy/MM/dd"));
            Connection con = Jsoup.connect(url.toString());
            con.timeout(10000);
            Document doc = con.get();
            Element el = doc.select(".body .content").first();
            StringBuilder text = new StringBuilder();
            for (Element element : el.select("p:not(:last-child)")) {
                text.append(element.text()).append("\n");
            }
            if (el == null || el.text() == null) {
                throw new Exception("Não encontrado Wod do dia especificado.");
            }
            String descricaoExercicios = text.toString();
            if (!UteisValidacao.emptyString(descricaoExercicios)) {
                return mm.addAttribute(RETURN, descricaoExercicios);
            } else {
                throw new Exception("Erro ao buscar Wod");
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(CrossfitJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    private Integer obterCodigoUsuario(String ctx, Integer usuario, String matricula) throws Exception {
        Integer codUsuario;
        if (!UteisValidacao.emptyNumber(usuario)) {
            codUsuario = usuario;
        } else {
            Usuario usuarioBanco = usuarioService.consultarPorMatricula(ctx, Integer.parseInt(matricula));
            if (usuarioBanco == null) {
                throw new Exception("Usuário não encontrado.");
            }
            codUsuario = usuarioBanco.getCodigo();
        }

        if (UteisValidacao.emptyNumber(codUsuario)) {
            throw new Exception("Usuário não encontrado.");
        }
        return codUsuario;
    }


    private Wod obterWod(String ctx, Integer codWod) throws Exception {
        Wod wodresult = ws.obterPorId(ctx, codWod);
        if (wodresult == null) {
            throw new Exception("Não foi possível encontrar o Wod.");
        }
        wodresult.getAtividades();
        wodresult.getAparelhos();
        return wodresult;
    }

    @ApiOperation(
            value = "Consultar eventos de CrossFit",
            notes = "Lista todos os eventos de CrossFit cadastrados no sistema. " +
                    "Retorna competições, games e outros eventos relacionados ao CrossFit.",
            tags = "CrossFit"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)")
    })
    @RequestMapping(value = "{ctx}/eventos", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap eventos(@PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            List<EventoCrossfit> eventos = es.obterTodos(ctx);
            mm.addAttribute(RETURN, eventos);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(CrossfitJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(
            value = "Criar nova equipe",
            notes = "Cria uma nova equipe para eventos de CrossFit. " +
                    "Permite formar times para competições e games de CrossFit.",
            tags = "CrossFit"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)")
    })
    @RequestMapping(value = "{ctx}/novaEquipe", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap novoTime(@PathVariable String ctx,
                      @ApiParam(value = "Nome da equipe", defaultValue = "Team CrossFit", required = true)
                      @RequestParam String nome,
                      @ApiParam(value = "Código do nível da equipe", defaultValue = "1", required = true)
                      @RequestParam Integer nivel,
                      @ApiParam(value = "Matrícula do capitão da equipe", defaultValue = "12345", required = true)
                      @RequestParam Integer matricula) {
        ModelMap mm = new ModelMap();
        try {
            NivelWod nivelWod = nivelWodService.obterPorCodigo(ctx,nivel);
            EquipeEvento equipeEvento = new EquipeEvento();
            equipeEvento.setNome(nome);
            equipeEvento.setNivelWod(nivelWod);
            equipeEvento = es.inserirEquipe(ctx, equipeEvento, new ArrayList<ParticipanteEquipeEvento>());
            mm.addAttribute(RETURN, equipeEvento);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(CrossfitJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(
            value = "Consultar equipes do aluno",
            notes = "Lista todas as equipes das quais um aluno faz parte. " +
                    "Retorna as equipes de CrossFit associadas à matrícula informada.",
            tags = "CrossFit"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)")
    })
    @RequestMapping(value = "{ctx}/equipesAluno", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap equipesAluno(@PathVariable String ctx,
                      @ApiParam(value = "Matrícula do aluno para consulta das equipes", defaultValue = "12345", required = true)
                      @RequestParam Integer matricula) {
        ModelMap mm = new ModelMap();
        try {
            List<EquipeEvento> equipes = es.equipes(ctx, null);
            mm.addAttribute(RETURN, equipes);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(CrossfitJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(
            value = "Simular scores de treino",
            notes = "Simula scores de treino para um dia específico. " +
                    "Funcionalidade utilizada para testes e demonstrações do sistema.",
            tags = "CrossFit"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)")
    })
    @RequestMapping(value = "{ctx}/simularscore", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap simular(@PathVariable String ctx,
                     @ApiParam(value = "Data para simulação no formato dd_MM_yyyy", defaultValue = "01_01_2024")
                     @RequestParam(required = false) final String dia) {
        ModelMap mm = new ModelMap();
        try {
            Date data;
            if(dia == null){
                data = Calendario.hoje();
            }else{
                data = Uteis.getDate(dia, "dd_MM_yyyy");
            }
            ws.simularScore(ctx, data);
            mm.addAttribute(RETURN, "simulado");
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(CrossfitJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }


    @ApiOperation(
            value = "Cadastrar WOD compartilhado",
            notes = "Cadastra ou altera um WOD compartilhado no sistema. " +
                    "Permite sincronização de treinos entre diferentes unidades.",
            tags = "CrossFit"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)")
    })
    @RequestMapping(value = "{ctx}/compartilhado", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap tiposWods(@PathVariable String ctx,
                       @ApiParam(value = "Dados do WOD compartilhado em formato JSON", required = true)
                       @RequestBody String jsonBody) {
        ModelMap mm = new ModelMap();
        try {
            wodService.cadastrarAlterarWodCompartilhado(ctx, new JSONObject(jsonBody));
            mm.addAttribute(RETURN, "OK");
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(CrossfitJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(
            value = "Importar atividades de CrossFit",
            notes = "Importa atividades de CrossFit de uma empresa origem para uma empresa destino. " +
                    "Utilizado para replicar configurações de exercícios entre diferentes unidades.",
            tags = "CrossFit"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)")
    })
    @RequestMapping(value = "{ctxOrigem}/{ctxDestino}/importar-atividades-cross", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap importarAtividadesCross(@ApiParam(value = "Contexto da empresa origem", defaultValue = "empresa1", required = true)
                                     @PathVariable String ctxOrigem,
                                     @ApiParam(value = "Contexto da empresa destino", defaultValue = "empresa2", required = true)
                                     @PathVariable String ctxDestino) {
        /*
         * IMPORTADOR DE ATIVIDADES CROSS:
         * Utiliza uma chave como modelo (ctxOrigem) para importar para a chave desejada (ctxDestino)
         * A importação é realizada para todas unidades caso a ctxDestino tenha mais de uma unidade
         * ctxOrigem utilizada como modelo: f0f6e849216d5398379b17628602a77b - CROSS EXPERIENCE JARDIM ELDORADO - MG
         * KEYSEARCH: importaratividadecross; povoaratividadecross; atividadecross;
         */
        ModelMap mm = new ModelMap();
        try {
            String retorno = povoadorAtividadeService.runImportarAtividadesCross(ctxOrigem, ctxDestino);
            mm.addAttribute(STATUS_SUCESSO, retorno);
        } catch (ServiceException e) {
            mm.addAttribute(STATUS_ERRO, e.getMessage());
            Logger.getLogger(AtividadeJSONControle.class.getName()).log(Level.SEVERE, null, e);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return mm;
    }

    @ApiOperation(
            value = "Avaliar WOD",
            notes = "Permite que usuários avaliem um WOD específico. " +
                    "Registra feedback e avaliações sobre a qualidade e dificuldade dos treinos.",
            tags = "CrossFit"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)")
    })
    @RequestMapping(value = "{ctx}/avaliarWod", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap avaliarWods(@PathVariable String ctx,
                         @ApiParam(value = "Dados da avaliação do WOD", required = true)
                         @RequestBody AvaliacaoWodDTO avaliacaoWodDTO) {
        ModelMap mm = new ModelMap();
        try {
            wodService.avaliarWod(ctx, avaliacaoWodDTO);
            mm.addAttribute(RETURN, "OK");
        } catch (Exception e) {
            mm.addAttribute(STATUS_ERRO, e.getMessage());
            Logger.getLogger(CrossfitJSONControle.class.getName()).log(Level.SEVERE, null, e);
        }
        return mm;
    }

    @ApiOperation(
            value = "Consultar avaliações de WODs",
            notes = "Lista todas as avaliações de WODs registradas no sistema. " +
                    "Retorna feedback e classificações dos usuários sobre os treinos.",
            tags = "CrossFit"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)")
    })
    @RequestMapping(value = "{ctx}/avaliacoesWod", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap avaliacoesWod(@PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            mm.addAttribute(RETURN, wodService.avaliacoesWod(ctx));
        } catch (Exception e) {
            mm.addAttribute(STATUS_ERRO, e.getMessage());
            Logger.getLogger(CrossfitJSONControle.class.getName()).log(Level.SEVERE, null, e);
        }
        return mm;
    }

    @ApiOperation(
            value = "Excluir WODs por chaves em lote",
            notes = "Exclui WODs em lote baseado em uma lista de chaves fornecida via arquivo CSV. " +
                    "Operação administrativa para limpeza de dados em massa.",
            tags = "CrossFit"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)")
    })
    @ResponseBody
    @RequestMapping(value = "/run-excluir-wods-por-chaves", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ModelMap runExcluirWodsPorChaves(@ApiParam(value = "Dados da requisição contendo arquivo CSV em base64", required = true)
                                            @RequestBody Map<String, String> requestBody,
                                            @ApiParam(value = "Data de início do período para exclusão", defaultValue = "2024-01-01", required = true)
                                            @RequestParam("dataInicio") @DateTimeFormat(pattern = "yyyy-MM-dd") Date dataInicio,
                                            @ApiParam(value = "Data de fim do período para exclusão", defaultValue = "2024-12-31", required = true)
                                            @RequestParam("dataFim") @DateTimeFormat(pattern = "yyyy-MM-dd") Date dataFim  ) {
        // csv em base64 e contendo somente uma coluna e cada linha uma chave
        ModelMap mm = new ModelMap();
        try {
            String csvBase64Data = requestBody.get("fileBase64");
            ws.runExcluirWodsPorChaves(csvBase64Data, dataInicio, dataFim, mm);
        } catch (ServiceException e) {
            mm.addAttribute(STATUS_ERRO, e.getMessage());
            Logger.getLogger(CrossfitJSONControle.class.getName()).log(Level.SEVERE, "Erro ao excluir WODs", e);
        }
        return mm;
    }

    @ApiOperation(
            value = "Excluir WODs por chave específica",
            notes = "Exclui todos os WODs de uma chave específica em um período determinado. " +
                    "Operação administrativa para limpeza de dados de uma empresa específica.",
            tags = "CrossFit"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)")
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/excluir-wods-por-chave", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> excluirWodsPorChave(@PathVariable String ctx,
                                                                   @ApiParam(value = "Data de início do período para exclusão", defaultValue = "2024-01-01", required = true)
                                                                   @RequestParam("dataInicio") @DateTimeFormat(pattern = "yyyy-MM-dd") Date dataInicio,
                                                                   @ApiParam(value = "Data de fim do período para exclusão", defaultValue = "2024-12-31", required = true)
                                                                   @RequestParam("dataFim") @DateTimeFormat(pattern = "yyyy-MM-dd") Date dataFim) {
        try {
            String resultado = ws.excluirWodsPorChave(ctx, dataInicio, dataFim);
            return ResponseEntityFactory.ok(resultado);
        } catch (ServiceException e) {
            Logger.getLogger(CrossfitJSONControle.class.getName()).log(Level.SEVERE, "Erro ao excluir WODs para a chave: " + ctx, e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Listar níveis de WOD",
            notes = "Lista todos os níveis de WOD disponíveis no sistema. " +
                    "Retorna os diferentes níveis de dificuldade para classificação dos treinos (Iniciante, RX, Scaled, etc.).",
            tags = "CrossFit"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)")
    })
    @RequestMapping(value = "{ctx}/listaNivelWod", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap listaNivelWod(@PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            mm.addAttribute(RETURN, nivelWodService.listarNiveisWodPorChave(ctx));
        } catch (Exception e) {
            mm.addAttribute(STATUS_ERRO, e.getMessage());
            Logger.getLogger(CrossfitJSONControle.class.getName()).log(Level.SEVERE, null, e);
        }
        return mm;
    }
}
