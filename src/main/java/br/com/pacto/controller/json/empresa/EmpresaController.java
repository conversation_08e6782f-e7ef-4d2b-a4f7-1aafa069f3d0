
package br.com.pacto.controller.json.empresa;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.swagger.respostas.empresa.*;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by ulisses on 29/08/2018.
 */
@Controller
@RequestMapping("/psec/empresas")
public class EmpresaController {

    private final EmpresaService empresaService;
    private final UsuarioService usuarioService;

    @Autowired
    private SessaoService sessaoService;

    @Autowired
    public EmpresaController(EmpresaService empresaService, UsuarioService usuarioService) {
        Assert.notNull(empresaService, "O serviço de empresa não foi injetado corretamente");
        Assert.notNull(usuarioService, "O serviço de usuário não foi injetado corretamente");
        this.empresaService = empresaService;
        this.usuarioService = usuarioService;
    }

    @ApiOperation(
            value = "Listar empresas",
            notes = "Lista todas as empresas cadastradas no sistema. " +
                    "Retorna informações básicas das empresas incluindo código, nome e ID do ZW. " +
                    "A autenticação é realizada através dos headers 'chave' e 'empresaId' configurados globalmente.",
            tags = "Empresa"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListEmpresaBasicaResponseTO.class)
    })
    @ResponseBody
//    @Permissao(recursos = RecursoEnum.EMPRESA)
    @RequestMapping(value = "/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarEmpresas() {
        try {
            return ResponseEntityFactory.ok(empresaService.listarEmpresas());
        } catch (ServiceException e) {
            Logger.getLogger(EmpresaController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as empresas", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Obter link para ZW",
            notes = "Gera uma URL de acesso para o sistema ZW (Zillion Wellness) com os parâmetros de autenticação fornecidos. " +
                    "Utilizado para integração e redirecionamento entre sistemas. " +
                    "O parâmetro 'empresaId' é obrigatório e utilizado na construção da URL de redirecionamento.",
            tags = "Empresa"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaUrlZW.class)
    })
    @ResponseBody
//    @Permissao(recursos = RecursoEnum.EMPRESA)
    @RequestMapping(value = "/linkzw", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> linkzw(
            @ApiParam(value = "Nome de usuário OAMD para autenticação", defaultValue = "usuario.teste", required = true)
            @RequestParam("usuarioOamd") String usuarioOamd,
            @ApiParam(value = "URL de login do sistema ZW", defaultValue = "https://sistema.zw.com/login", required = true)
            @RequestParam("urlLogin") String urlLogin,
            @ApiParam(value = "ID da empresa para acesso", defaultValue = "1", required = true)
            @RequestHeader("empresaId") Integer empresaId,
            @ApiParam(value = "Token OAMD para autenticação", defaultValue = "abc123token")
            @RequestParam(value = "tokenOamd", required = false) String tokenOamd,
            @ApiParam(value = "ID da sessão ativa", defaultValue = "session123")
            @RequestParam(value = "sessionId", required = false) String sessionId
    ) {
        try {
            return ResponseEntityFactory.ok(usuarioService.urlZW(usuarioOamd, urlLogin, empresaId, sessionId, tokenOamd));
        } catch (ServiceException e) {
            Logger.getLogger(EmpresaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter url para o zw", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Ativar empresa",
            notes = "Ativa uma empresa no sistema criando um colaborador coordenador inicial. " +
                    "Processo de inicialização que configura a empresa e retorna token de acesso. " +
                    "A autenticação é realizada através dos headers 'chave' e 'empresaId' configurados globalmente.",
            tags = "Empresa"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaResponseActiveEmpresaDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/ativar", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> ativarEmpresa(
            @ApiParam(value = "Chave de identificação da empresa para ativação", defaultValue = "empresa123", required = true)
            @RequestParam(value = "chave") String chave,
            @ApiParam(value = "Dados do colaborador coordenador inicial", required = true)
            @RequestBody CreateClientDTO colaborador,
            HttpServletRequest request) {
        try {
            return ResponseEntityFactory.ok(empresaService.ativarEmpresa(chave, colaborador, request));
        } catch (ServiceException e) {
            e.printStackTrace();
            Logger.getLogger(EmpresaController.class.getName()).log(Level.SEVERE, "Erro ao tentar ativar empresa", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Buscar empresa por ID",
            notes = "Busca uma empresa específica através do seu código identificador. " +
                    "Retorna a entidade completa da empresa com todas as informações. " +
                    "A autenticação é realizada através dos headers 'chave' e 'empresaId' configurados globalmente.",
            tags = "Empresa"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaEmpresa.class)
    })
    @ResponseBody
//    @Permissao(recursos = RecursoEnum.EMPRESA)
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarEmpresa(
            @ApiParam(value = "Código único identificador da empresa", defaultValue = "1", required = true)
            @PathVariable() Integer id) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(empresaService.obterPorId(ctx, id));
        } catch (ServiceException e) {
            Logger.getLogger(EmpresaController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as empresas", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Buscar empresa por ID",
            notes = "Busca uma empresa específica retornando apenas os dados essenciais. " +
                    "Versão simplificada com informações básicas da empresa. " +
                    "A autenticação é realizada através dos headers 'chave' e 'empresaId' configurados globalmente.",
            tags = "Empresa"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaEmpresaDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/empdto/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarEmpresaDTO(
            @ApiParam(value = "Código único identificador da empresa", defaultValue = "1", required = true)
            @PathVariable() Integer id) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(empresaService.obterPorIdEmpDTO(ctx, id));
        } catch (ServiceException e) {
            Logger.getLogger(EmpresaController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as empresas", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Alterar empresa",
            notes = "Altera os dados de uma empresa existente no sistema. " +
                    "Permite atualizar informações como nome, timezone e imagem da empresa. " +
                    "A autenticação é realizada através dos headers 'chave' e 'empresaId' configurados globalmente.",
            tags = "Empresa"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaEmpresaDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/alterarEmpresa", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarEmpresa(
            @ApiParam(value = "Dados da empresa para alteração", required = true)
            @RequestBody EmpresaDTO empresa) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(empresaService.alterarEmpDTO(ctx, empresa));
        } catch (ServiceException e) {
            e.printStackTrace();
            Logger.getLogger(EmpresaController.class.getName()).log(Level.SEVERE, "Erro ao tentar ativar empresa", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Listar unidades ativas",
            notes = "Lista todas as unidades/empresas que estão ativas no sistema. " +
                    "Retorna apenas empresas com status ativo e sincronizadas com o ZW. " +
                    "A autenticação é realizada através dos headers 'chave' e 'empresaId' configurados globalmente.",
            tags = "Empresa"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListEmpresaBasicaResponseTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/ativas", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarUnidadesAtivas() {
        try {
            return ResponseEntityFactory.ok(empresaService.obterUnidadesAtivas());
        } catch (ServiceException e) {
            Logger.getLogger(EmpresaController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as unidades ativas", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            Logger.getLogger(EmpresaController.class.getName()).log(Level.SEVERE, "Erro inesperado ao listar unidades ativas", e);
            return ResponseEntityFactory.erroInterno("ERRO_LISTAR_UNIDADES_ATIVAS", e.getMessage());
        }
    }


}
