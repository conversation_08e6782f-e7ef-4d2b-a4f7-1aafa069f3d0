package br.com.pacto.controller.json.crossfit;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.benchmark.TipoBenchmarkTO;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.benchmark.TipoBenchmarkService;
import br.com.pacto.swagger.respostas.crossfit.ExemploRespostaTipoBenchmark;
import br.com.pacto.swagger.respostas.crossfit.ExemploRespostaListaTipoBenchmark;
import br.com.pacto.swagger.respostas.crossfit.ExemploRespostaTipoBenchmarkResponseTO;
import br.com.pacto.swagger.respostas.crossfit.ExemploRespostaExclusaoTipoBenchmark;
import io.swagger.annotations.*;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by ulisses on 23/08/2018.
 */
@Api(tags = "CrossFit")
@Controller
@RequestMapping("/psec/tipos-benchmark")
public class TipoBenchmarkController {

    private TipoBenchmarkService tipoBenchmarkService;

    @Autowired
    public TipoBenchmarkController(TipoBenchmarkService tipoBenchmarkService) {
        Assert.notNull(tipoBenchmarkService, "O serviço de tipo Benchmark não foi injetado corretamente");
        this.tipoBenchmarkService = tipoBenchmarkService;
    }


    @ApiOperation(
            value = "Consultar tipos de benchmark com paginação",
            notes = "Lista todos os tipos de benchmark disponíveis no sistema com suporte a filtros e paginação. " +
                    "Permite filtrar por nome do tipo de benchmark para facilitar a busca.",
            tags = "Tipos Benchmark"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "empresaId", value = "Código identificador da empresa", required = true, paramType = "header", dataType = "long", example = "1"),
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaTipoBenchmark.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.TIPO_BENCHMARK)
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarTipoBenchmark(
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                    "<strong>Filtros disponíveis:</strong>\n" +
                    "- <strong>quicksearchValue:</strong> Termo de busca para nome do tipo de benchmark.\n" +
                    "- <strong>quicksearchFields:</strong> Define em qual campo o 'quicksearchValue' será aplicado (Deve ser informado como uma lista ex: [\"nome\"]).",
                    defaultValue = "{\"quicksearchValue\":\"Hero\", \"quicksearchFields\":[\"nome\"]}")
            @RequestParam(value = "filters", required = false) JSONObject filtros,
            @ApiIgnore
            PaginadorDTO paginadorDTO) throws JSONException {
        try {
            FiltroTipoBenchmarkJSON filtroTipoBenchmarkJSON = new FiltroTipoBenchmarkJSON(filtros);
            return ResponseEntityFactory.ok(tipoBenchmarkService.consultarTipoBenchmark(filtroTipoBenchmarkJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(TipoBenchmarkController.class.getName()).log(Level.SEVERE, "Erro ao consultar os tipos de Benchmark", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar todos os tipos de benchmark",
            notes = "Retorna todos os tipos de benchmark disponíveis no sistema sem paginação. " +
                    "Utilizado para preenchimento de listas de seleção e formulários.",
            tags = "Tipos Benchmark"
    )
    @ApiImplicitParam(name = "empresaId", value = "Código identificador da empresa", required = true, paramType = "header", dataType = "long", example = "1")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListaTipoBenchmark.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.TIPO_BENCHMARK)
    @RequestMapping(value = "/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarTodos() {
        try {
            return ResponseEntityFactory.ok(tipoBenchmarkService.consultarTodos());
        } catch (ServiceException e) {
            Logger.getLogger(TipoBenchmarkController.class.getName()).log(Level.SEVERE, "Erro ao consultar todos os tipos de Benchmark", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Incluir novo tipo de benchmark",
            notes = "Cadastra um novo tipo de benchmark no sistema. " +
                    "O tipo de benchmark é usado para categorizar os diferentes tipos de WODs (Hero, Girl, Games, etc.).",
            tags = "Tipos Benchmark"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaTipoBenchmarkResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.TIPO_BENCHMARK)
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> incluirBenchmark(
            @ApiParam(value = "Dados do tipo de benchmark a ser incluído", required = true)
            @RequestBody TipoBenchmarkTO tipoBenchmarkTO) {
        try {
            return ResponseEntityFactory.ok(tipoBenchmarkService.inserir(tipoBenchmarkTO));
        } catch (ServiceException e) {
            Logger.getLogger(TipoBenchmarkController.class.getName()).log(Level.SEVERE, "Erro ao tentar incluir tipo de Benchmark", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Consultar tipo de benchmark por ID",
            notes = "Busca um tipo de benchmark específico através do seu ID único. " +
                    "Retorna os dados completos do tipo de benchmark solicitado.",
            tags = "Tipos Benchmark"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaTipoBenchmarkResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.TIPO_BENCHMARK)
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarTipoBenchmark(
            @ApiParam(value = "ID único do tipo de benchmark", defaultValue = "1", required = true)
            @PathVariable("id") final Integer id) {
        try {
            return ResponseEntityFactory.ok(tipoBenchmarkService.consultar(id));
        } catch (ServiceException e) {
            Logger.getLogger(TipoBenchmarkController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar o tipo de Benchmark", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Alterar tipo de benchmark existente",
            notes = "Atualiza os dados de um tipo de benchmark existente no sistema. " +
                    "Permite modificar o nome e outras propriedades do tipo de benchmark.",
            tags = "Tipos Benchmark"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaTipoBenchmarkResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.TIPO_BENCHMARK)
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarTipoBenchmark(
            @ApiParam(value = "ID único do tipo de benchmark a ser alterado", defaultValue = "1", required = true)
            @PathVariable("id") final Integer id,
            @ApiParam(value = "Dados atualizados do tipo de benchmark", required = true)
            @RequestBody TipoBenchmarkTO tipoBenchmarkTO) {
        try {
            tipoBenchmarkTO.setId(id);
            return ResponseEntityFactory.ok(tipoBenchmarkService.alterar(tipoBenchmarkTO));

        } catch (ServiceException e) {
            Logger.getLogger(TipoBenchmarkController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar tipo de Benchmark", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Excluir tipo de benchmark",
            notes = "Remove um tipo de benchmark do sistema. " +
                    "A exclusão só será permitida se o tipo não estiver sendo utilizado por nenhum benchmark.",
            tags = "Tipos Benchmark"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaExclusaoTipoBenchmark.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.TIPO_BENCHMARK)
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> excluirTipoBenchmark(
            @ApiParam(value = "ID único do tipo de benchmark a ser excluído", defaultValue = "1", required = true)
            @PathVariable("id") final Integer id) {
        try {
            tipoBenchmarkService.excluir(id);
            return ResponseEntityFactory.ok(true);
        } catch (ServiceException e) {
            Logger.getLogger(TipoBenchmarkController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir tipo de Benchmark", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroEstaSendoUtilizado(e.getChaveExcecao(), e.getMessage());
            }
        }
    }


}
