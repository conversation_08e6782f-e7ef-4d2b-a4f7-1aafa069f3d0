@echo off
rem Author: <PERSON>

set URL_ZW="http://192.168.3.47:8080/TreinoWeb"

rem running cypress in graphic mode
set BROWSER="chrome"

rem running cypress in graphic mode console mode
rem set BROWSER="electron"

rem check if node is installed
node -v 2> Nul
if "%errorlevel%" == "9009" (
    echo node nao está instalado nesse ambiente, requerida v6.9 ou superior
) else (
    call npm install
	start 	cypress run -b "%BROWSER%" --config baseUrl="%URL_ZW%"
)

pause