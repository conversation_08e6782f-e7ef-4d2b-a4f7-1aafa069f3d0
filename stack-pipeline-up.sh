#!/bin/bash

export HOST_PIPELINE="*********"
export HOST_DNS="pipe-treino.pactosolucoes.com.br"

export ZW_PORT=13000
export STACK_NAME=pipe-treino-$ZW_PORT

find . -iname "pipe-treino-*.ports" -delete

echo "$ZW_PORT" > $STACK_NAME.ports

function getRandomPort() {
    valorTemp=$(shuf -i 6000-6999 |head -n 1)

    while [[ `cat $STACK_NAME.ports |grep $valorTemp |wc -l` -gt 0 ]]
    do
        valorTemp=$(shuf -i 6000-6999 |head -n 1)
        echo "$valorTemp" >> $STACK_NAME.ports
    done

    echo "$valorTemp" >> $STACK_NAME.ports

    echo "$valorTemp"
}

export PG_PORT=13001
export TR_PORT=13002
export DISCOVERY_PORT=13003
export AUT_PORT=13004
export BI_MS_PORT=13005
export CAD_MS_PORT=13006
export DASH_API_PORT=13007
export LOGIN_PORT=13008
export ADM_PORT=13009
export OAMD_PORT=13010
export FULL_REPORT_PORT=13011
export PRODUTO_MS_PORT=13012
export PACTO_PAY_PORT=13013
export PERSONAGEM_PORT=13014
export PLANO_MS_PORT=13015
export NTR_PORT=13016
export DYNAMODB_PORT=13017

export ZW_URL=http://$HOST_DNS:$ZW_PORT/ZillyonWeb
export TREINO_URL=http://$HOST_DNS:$TR_PORT/TreinoWeb
export LOGIN_URL=http://$HOST_DNS:$LOGIN_PORT/LoginApp

sed -i -e "s/STACK_NAME=.*/STACK_NAME=$STACK_NAME/g" .env
sed -i -e "s/ZW_PORT=.*/ZW_PORT=$ZW_PORT/g" .env
sed -i -e "s/TR_PORT=.*/TR_PORT=$TR_PORT/g" .env
sed -i -e "s~ZW_URL=.*~ZW_URL=$ZW_URL~g" .env
sed -i -e "s~TREINO_URL=.*~TREINO_URL=$TREINO_URL~g" .env
sed -i -e "s~LOGIN_URL=.*~LOGIN_URL=$LOGIN_URL~g" .env
sed -i -e "s/PLANO_MS_PORT=.*/PLANO_MS_PORT=$PLANO_MS_PORT/g" .env
sed -i -e "s/NTR_PORT=.*/NTR_PORT=$NTR_PORT/g" .env
sed -i -e "s/HOST_PIPELINE=.*/HOST_PIPELINE=$HOST_PIPELINE/g" .env
sed -i -e "s/HOST_DNS=.*/HOST_DNS=$HOST_DNS/g" .env
sed -i -e "s/DISCOVERY_PORT=.*/DISCOVERY_PORT=$DISCOVERY_PORT/g" .env
sed -i -e "s/AUT_PORT=.*/AUT_PORT=$AUT_PORT/g" .env
sed -i -e "s/BI_MS_PORT=.*/BI_MS_PORT=$BI_MS_PORT/g" .env
sed -i -e "s/CAD_MS_PORT=.*/CAD_MS_PORT=$CAD_MS_PORT/g" .env
sed -i -e "s/DASH_API_PORT=.*/DASH_API_PORT=$DASH_API_PORT/g" .env
sed -i -e "s/LOGIN_PORT=.*/LOGIN_PORT=$LOGIN_PORT/g" .env
sed -i -e "s/ADM_PORT=.*/ADM_PORT=$ADM_PORT/g" .env
sed -i -e "s/OAMD_PORT=.*/OAMD_PORT=$OAMD_PORT/g" .env
sed -i -e "s/FULL_REPORT_PORT=.*/FULL_REPORT_PORT=$FULL_REPORT_PORT/g" .env
sed -i -e "s/PRODUTO_MS_PORT=.*/PRODUTO_MS_PORT=$PRODUTO_MS_PORT/g" .env
sed -i -e "s/PACTO_PAY_PORT=.*/PACTO_PAY_PORT=$PACTO_PAY_PORT/g" .env
sed -i -e "s/PERSONAGEM_PORT=.*/PERSONAGEM_PORT=$PERSONAGEM_PORT/g" .env
sed -i -e "s/PG_PORT=.*/PG_PORT=$PG_PORT/g" .env
sed -i -e "s/DYNAMODB_PORT=.*/DYNAMODB_PORT=$DYNAMODB_PORT/g" .env

cat .env

#cleaning trash!
docker container prune --force
docker volume prune --force

echo
docker pull registry.gitlab.com/plataformazw/docker-pacto/postgres:9.4
docker pull registry.gitlab.com/plataformazw/zw/tomcat:master
docker pull registry.gitlab.com/plataformazw/zw_ui/novo-treino:master

echo "Removing old stacks..."
docker stack rm $(docker stack ls | grep -e "treino-" | awk '{print $1}')
sleep 30s

echo "Stopping old cypress-treino containers running..."
docker ps --format='{{.Names}}' |grep cypress-treino |awk '{print $1}' |xargs docker stop
ssh -o StrictHostKeyChecking=no ${USER}@z2-srv02 "docker ps --format='{{.Names}}' |grep cypress-treino |awk '{print \$1}' |xargs docker stop"
ssh -o StrictHostKeyChecking=no ${USER}@z1-srv-p "docker ps --format='{{.Names}}' |grep cypress-treino |awk '{print \$1}' |xargs docker stop"
ssh -o StrictHostKeyChecking=no ${USER}@z2-srv-p "docker ps --format='{{.Names}}' |grep cypress-treino |awk '{print \$1}' |xargs docker stop"


set -e
echo
docker stack deploy -c docker-compose-prepare.yml ${STACK_NAME} --with-registry-auth

echo
echo "Sleeping por 120s..."
sleep 120s

echo
dockerize -wait $ZW_URL -wait $TREINO_URL -wait $LOGIN_URL --timeout 20m -wait-retry-interval 30s

#curl --request POST $TREINO_URL/prest/config/teste/removeFactory
#curl --request POST $TREINO_URL/prest/config/reload
curl --request POST $TREINO_URL/prest/config/teste/updateBD
curl --request POST $ZW_URL/prest/versao?chave=teste
echo
