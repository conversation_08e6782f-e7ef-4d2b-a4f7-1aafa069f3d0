<?xml version="1.0" encoding="UTF-8"?>
<project-shared-configuration>
    <!--
This file contains additional configuration written by modules in the NetBeans IDE.
The configuration is intended to be shared among all the users of project and
therefore it is assumed to be part of version control checkout.
Without this configuration present, some functionality in the IDE may be limited or fail altogether.
-->
    <properties xmlns="http://www.netbeans.org/ns/maven-properties-data/1">
        <!--
Properties that influence various parts of the IDE, especially code formatting and the like. 
You can copy and paste the single properties, into the pom.xml file and the IDE will pick them up.
That way multiple projects can share the same settings (useful for formatting rules for example).
Any value defined here will override the pom.xml file value but is only applicable to the current project.
-->
        <netbeans.hint.j2eeVersion>1.6-web</netbeans.hint.j2eeVersion>
        <netbeans.compile.on.save>false</netbeans.compile.on.save>
        <netbeans.deploy.on.save>false</netbeans.deploy.on.save>
    </properties>
</project-shared-configuration>
