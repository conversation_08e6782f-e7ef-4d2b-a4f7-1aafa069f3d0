{
    // See https://go.microsoft.com/fwlink/?LinkId=733558 
    // for the documentation about the tasks.json format
    "version": "2.0.0",
    "tasks": [
        {
            "label": "build",
            "type": "shell",
            "command": "mvn clean package -Ptomcat7,desenv,docker,remote-dependencies",
            "group": "build"
        },
        {
            "label": "build-restart",
            "type": "shell",
            "command": "mvn clean package -Ptomcat7,desenv,docker,remote-dependencies && docker-compose stop treino && docker-compose up treino",
            "group": {
                "kind": "build",
                "isDefault": true
            }
        }
    ]
}