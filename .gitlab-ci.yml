stages:
  - build
  - test
  - finish

docker-tomcat-build:
  stage: build
  interruptible: true
  only:
    - master
    - merge_requests
    - tags
    - develop
  tags:
    - locaweb
    - large
    - shell
  variables:
    TAG: registry.gitlab.com/plataformazw/treino/tomcat:$CI_COMMIT_REF_SLUG
  script:
    - docker login -u $CI_USER -p $CI_USER_PASS $CI_REGISTRY
    - mvn clean package -P tomcat7,docker
    - docker build --no-cache -t $TAG .
    - docker push $TAG
  retry:
   max: 2
   when: runner_system_failure

tag-version:
  image: registry.gitlab.com/plataformazw/tag-versions:master
  stage: finish
  when: on_success
  tags:
    - docker
  only:
    - master
  except:
    changes:
      - .bumpversion.cfg
#      - .gitlab-*.yml
  script:
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - git config --global user.name "Pipeline"
    - git config --global user.email "<EMAIL>"
    - git tag -l | xargs git tag -d
    - bumpversion  patch
    - git remote show origin
    - git remote set-url --<NAME_EMAIL>:$CI_PROJECT_PATH
    - git remote show origin
    - git tag -f latest -m "Deploy tag"
    - GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no" git push origin HEAD:$CI_COMMIT_REF_NAME
    - GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no" git push -f --tags
  retry:
    max: 2
    when: runner_system_failure

include:
  - local: .gitlab-ci-stages.yml
