#!/usr/bin/env bash

set -e

file_properties=/usr/local/tomcat/webapps/TreinoWeb/WEB-INF/classes/br/com/pacto/util/resources/OpcoesGlobais.properties
original_file_properties=/app/OpcoesGlobais.properties
build_file_properties=/app/OpcoesGlobais.build.properties
cfgBD_file=/usr/local/tomcat/webapps/TreinoWeb/WEB-INF/classes/br/com/pacto/base/oamd/cfgBD.xml

cp $build_file_properties $file_properties

if [ -f "$file_properties" ]; then
    sed -i "s~discoveryUrls.*=.*~discoveryUrls=$DISCOVERY_URL~g" $file_properties
    sed -i "s~ambienteTeste.*=.*~ambienteTeste=$AMBIENTE_TESTE~g" $file_properties
    sed -i "s~ipServidoresMemCached.*=.*~ipServidoresMemCached=$SERVIDOR_MEMCACHED~g" $file_properties
    sed -i "s~tokenAcessoAPICliente.*=.*~tokenAcessoAPICliente=$TOKENS_ACESSO_API_CLIENTE~g" $file_properties
    sed -i "s~tokenGymPassV3.*=.*~tokenGymPassV3=$TOKEN_GYMPASS_V3~g" $file_properties
    sed -i "s~urlZillyonWebIntegProp.*=.*~urlZillyonWebIntegProp=$URL_ZW_INTEGRACAO~g" $file_properties
    sed -i "s~AUTH_SECRET_PATH.*=.*~AUTH_SECRET_PATH=$AUTH_SECRET_PATH~g" $file_properties
    sed -i "s~AUTH_SECRET_PERSONA_PATH.*=.*~AUTH_SECRET_PERSONA_PATH=$AUTH_SECRET_PERSONA_PATH~g" $file_properties
    sed -i -E "s~<url-oamd>.*~<url-oamd>$DB_OAMD2_URL</url-oamd>~g" $cfgBD_file
    sed -i "s~ENABLE_NEW_LOGIN.*=.*~ENABLE_NEW_LOGIN=$ENABLE_NEW_LOGIN~g" $file_properties
    sed -i "s~consultaUsuariosAppPeloFireBase.*=.*~consultaUsuariosAppPeloFireBase=$CONSULTA_USUARIOS_APP_PELO_FIREBASE~g" $file_properties
    sed -i "s~fotosParaNuvem.*=.*~fotosParaNuvem=$FOTOS_NUVEM~g" $file_properties
    echo "Application properties:"
    cat $file_properties
    echo "cfgBD.xml"
    cat $cfgBD_file
    exit 0
else
    echo "Setup environment failed"
    echo "File $file_properties not found"
    exit 0
fi
