#!/bin/bash
set -e

setup-env.sh

# Se a variável de nome não for passada, usa um default
if [ -z "$NEW_RELIC_APP_NAME" ]; then
  echo "[New Relic] NEW_RELIC_APP_NAME não setada. Então não vou ativar o NR no container."
else
  echo "[New Relic] Aplicando NEW_RELIC_APP_NAME=$NEW_RELIC_APP_NAME"
    # Edita o app_name dentro do bloco common (usado de verdade)
    sed -i '/^common:/,/^[^[:space:]]/ s/^.*app_name:.*$/  app_name: "'"$NEW_RELIC_APP_NAME"'"/' /usr/local/newrelic/newrelic.yml
    # Edita enable_auto_app_naming no mesmo bloco
    sed -i '/^common:/,/^[^[:space:]]/ s/^.*enable_auto_app_naming:.*$/  enable_auto_app_naming: false/' /usr/local/newrelic/newrelic.yml

  echo "### Enabling NewRelic Java Agent: $NEW_RELIC_APP_NAME"
  export JAVA_OPTS="$JAVA_OPTS -javaagent:/usr/local/newrelic/newrelic.jar"
  echo "JAVA_OPTS=$JAVA_OPTS"
fi

catalina.sh jpda run $@
